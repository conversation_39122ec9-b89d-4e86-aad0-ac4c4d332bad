#include "pjt_glb_head.h"
#include "INS_Data.h"
#include "UartAdapter.h"
#include "systick.h"
#include "fpgad.h"
#include "protocol.h"
//#include "bsp_fmc.h"
#include "fmc_operation.h"
#include "config.h"


#include "app_tool.h"

extern AppSettingTypeDef  hDefaultSetting ;

//static uint32_t    r_times = 0;
extern __IO uint16_t fm_ptl_cksm_get  ;   //
extern __IO uint16_t fm_ptl_cksm_calc ;   //
extern __IO uint16_t fm_ptl_fw1f_maxlen;  //
extern __IO uint16_t fm_ptl_length;
extern      uint8_t  fm_ptl_rxbuf_max[Fm_MaxSize] ; 

///==global paras=============================================================================================================
/*********************
 \brief g_use_gdw_print
        In gdwatch.h at line80, enable app_ins422_printf GDW message
 \value  true, enable printf
 \value  false, disable printf 
**********************/
bool g_use_gdw_print  = false; 



///==end global paras====================================================================================end global paras=====
