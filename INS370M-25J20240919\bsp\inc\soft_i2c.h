#ifndef __SOFT_I2C__H__
#define __SOFT_I2C__H__
#include "gd32f4xx.h"
#include "bsp_sys.h"

typedef struct SOFT_I2C_io_t
{
	uint32_t gpiox;
	uint16_t    pin;
} Soft_I2C_IO_Type;

typedef struct bsp_SOFT_I2C
{
	Soft_I2C_IO_Type scl;
	Soft_I2C_IO_Type sda;
}BSP_SOFT_I2C_Type;

#define SCL_H(x)      gpio_bit_set(((x)->scl).gpiox,((x)->scl).pin)
#define SCL_L(x)      gpio_bit_reset(((x)->scl).gpiox,((x)->scl).pin)
#define SDA_O_H(x)    gpio_bit_set(((x)->sda).gpiox,((x)->sda).pin)
#define SDA_O_L(x)    gpio_bit_reset(((x)->sda).gpiox,((x)->sda).pin)
#define SDA_I(x)      gpio_input_bit_get(((x)->sda).gpiox,((x)->sda).pin)
#define SCL_I(x)      gpio_input_bit_get(((x)->sda).gpiox,((x)->scl).pin)

#endif  //!__SOFT_I2C__H__
