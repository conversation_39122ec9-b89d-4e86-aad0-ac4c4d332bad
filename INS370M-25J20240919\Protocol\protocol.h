#ifndef __PROTOCOL_H____
#define __PROTOCOL_H____

#undef COMMON_EXT
#ifdef  __GOL_PROTOCOL_C__
    #define COMMON_EXT
#else
    #define COMMON_EXT extern
#endif

#include "config.h"
#include <stdio.h>
#include "board.h"
#include "gd32f4xx.h"
#include "frame_analysis.h"

#pragma pack(1)
//typedef  struct poll_data_F
//{
//    uint16_t	data1;
//    uint16_t 	data2;
//    uint16_t	data3;
//} POLL_DATA_Fx, *pPOLL_DATA_F;	

//typedef  struct
//{
//    uint8_t 			header[3];	//0xbd,0xdb,0x0b
//    short 				roll;		//横滚角
//    short 				pitch;		//俯仰角
//    short				azimuth;	//方位角
//    short 				gyroX;		//陀螺x轴
//    short 				gyroY;		//陀螺y轴
//#ifdef	RS422_PROTOCOL_OLD
//	short				gyroZ;		//陀螺z轴
//#else
//	long				gyroZ;		//陀螺z轴
//#endif    
//    short 				accelX;		//加表x轴
//    short 				accelY;		//加表y轴
//    short				accelZ;		//加表z轴
//    long				latitude;	//纬度
//    long				longitude;	//经度
//    long				altitude;	//高度
//    short				ve;			//东向速度
//    short				vn;			//北向速度
//    short				vu;			//天向速度
//    uint8_t				status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向角
//    uint8_t				reserved[6];
//    POLL_DATA_Fx		poll_frame;
//    uint32_t			gps_time;
//    uint8_t				type;
//    uint8_t				xor_verify1;
//    uint32_t			gps_week;
//    uint8_t				xor_verify2;
//} DATA_STREAM;	
#pragma pack()

	
enum
{
    e_GyroX =  0,
    e_GyroY =  1,
    e_GyroZ =  2,
    e_AccelX = 4,
    e_AccelY = 5,
    e_AccelZ = 6
};

#define	IMUSTATUS_SET(x)	(IMUStatus |= ((uint32_t)0x01U<<(x)))


COMMON_EXT uint32_t  		IMUStatus;

COMMON_EXT void protocol_crc32_init(void);
COMMON_EXT void protocol_fillGipotData(void* pnav, void* pheading);
COMMON_EXT void protocol_fillGrimuData(void* pnav, void* pheading);
COMMON_EXT void protocol_fillRawimuData(void* pnav, void* pheading);
COMMON_EXT void protocol_report(uint8_t channel);
COMMON_EXT void protocol_gnrmcDataGet(uint8_t* pData, uint16_t* dataLen);
COMMON_EXT void protocol_gnggaDataGet(uint8_t* pData, uint16_t* dataLen);
COMMON_EXT void iPMV_protocolParse(uint8_t* pData, uint16_t dataLen);
COMMON_EXT void iPMV_protocol_report(void* pnav, void *gps);



#endif




