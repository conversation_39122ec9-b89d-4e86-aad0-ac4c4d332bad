#ifndef _EXTERNGLOBALDATA_H
#define _EXTERNGLOBALDATA_H
/***********************************************************************************************************************************/
/*EXTERNGLOBALDATA.h                                                                                                             */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*                                                                                                                             */
/*GNSSlocusGen.m                                                              */
/*                 */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "DATASTRUCT.h"
extern SysVar g_SysVar;    //扩展系统变量结构体全局变量的作用域

extern SelfTest g_SelfTest;

extern InitBind g_InitBind;//扩展惯导初始装订结构体全局变量的作用域

extern Align g_Align;//扩展惯导初始对准结构体全局变量的作用域

extern Navi g_Navi;//扩展导航结构体全局变量的作用域

extern Kalman g_Kalman;//扩展Kalman滤波结构体全局变量的作用域

extern GNSSData g_GNSSData_GNSS,g_GNSSData_VMC,g_GNSSData_In_Use;//声明GPS1数据结构体全局变量//声明GPS1数据结构体全局变量

//extern GNSSData g_GNSSData2;//声明GPS2数据结构体全局变量

//extern ADCData g_ADCData1;  //声明通道1提供的ADC数据结构体全局变量

//extern ADCData g_ADCData2;  //声明通道2提供的ADC数据结构体全局变量

extern Compen g_Compen;//扩展补偿参数结构体全局变量的作用域

extern InertialSysAlign g_InertialSysAlign;//扩展惯性凝固坐标系初始对准结构体全局变量的作用域

extern DynamicInertialSysAlign g_DynamicInertialSysAlign;

extern IMUSmoothAverage g_IMUSmoothAverage;
#endif
