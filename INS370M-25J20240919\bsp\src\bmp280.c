#include "bmp280.h"

struct bmp2_dev bmpDev;
struct bmp2_config bmpCfg;
struct bmp2_data BMP280Data;

uint32_t bmp280_init(void)
{
	//int8_t rslt = 0;
	uint32_t meas_time;
	
	/* Interface selection is to be updated as parameter
	 * For I2C :  BMP2_I2C_INTF
	 * For SPI :  BMP2_SPI_INTF
	*/
	bmp2_interface_selection(&bmpDev, BMP2_I2C_INTF);
	bmp2_init(&bmpDev);
	/* Always read the current settings before writing, especially when all the configuration is not modified */
	bmp2_get_config(&bmpCfg, &bmpDev);
	
	/* Configuring the over-sampling mode, filter coefficient and output data rate */
	/* Overwrite the desired settings */
	bmpCfg.filter = BMP2_FILTER_OFF;

	/* Over-sampling mode is set as high resolution i.e., os_pres = 8x and os_temp = 1x */
	bmpCfg.os_mode = BMP2_OS_MODE_HIGH_RESOLUTION;

	/* Setting the output data rate */
	bmpCfg.odr = BMP2_ODR_250_MS;
	//rslt = bmp2_set_config(&bmpCfg, &bmpDev);
	
	/* Set normal power mode */
	//rslt = bmp2_set_power_mode(BMP2_POWERMODE_NORMAL, &bmpCfg, &bmpDev);
	
	/* Calculate measurement time in microseconds */
	//rslt = bmp2_compute_meas_time(&meas_time, &bmpCfg, &bmpDev);
	
	return meas_time;
}

int8_t bmp280_get_data(uint32_t period, struct bmp2_dev *dev, struct bmp2_data* comp_data)
{
	int8_t rslt = BMP2_E_NULL_PTR;
	struct bmp2_status status;
	rslt = bmp2_get_status(&status, dev);
	if (status.measuring == BMP2_MEAS_DONE)
	{
		/* Delay between measurements */
		dev->delay_us(period, dev->intf_ptr);

		/* Read compensated data */
		rslt = bmp2_get_sensor_data(comp_data, dev);

		#ifdef BMP2_64BIT_COMPENSATION
		comp_data.pressure = comp_data.pressure / 256;
		#endif
		
	}
	return rslt;
}

