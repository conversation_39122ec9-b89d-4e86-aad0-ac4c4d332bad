/*!
    \file  exception_handlers.c
    \brief Enhanced exception handlers with debug information
*/

#include "gd32f4xx.h"
#include <stdio.h>

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
    printf("*** NMI EXCEPTION ***\n");
    printf("Non-Maskable Interrupt occurred\n");
    printf("System will restart in 2 seconds...\n");
    
    // 延时2秒后重启
    for(volatile int i = 0; i < 2000000; i++);
    
    // 软件复位
    NVIC_SystemReset();
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    printf("*** HARD FAULT EXCEPTION ***\n");
    printf("System has encountered a hard fault!\n");
    printf("This usually indicates:\n");
    printf("1. Invalid memory access (most common)\n");
    printf("2. Unaligned memory access\n");
    printf("3. Division by zero\n");
    printf("4. Invalid instruction\n");
    printf("5. Stack overflow\n");
    printf("Program will restart in 3 seconds...\n");
    
    // 延时3秒后重启
    for(volatile int i = 0; i < 3000000; i++);
    
    // 软件复位
    NVIC_SystemReset();
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    printf("*** MEMORY MANAGEMENT FAULT ***\n");
    printf("Memory protection violation occurred!\n");
    printf("This usually indicates:\n");
    printf("1. Access to invalid memory region\n");
    printf("2. Stack overflow\n");
    printf("3. Heap corruption\n");
    printf("Program will restart in 3 seconds...\n");
    
    // 延时3秒后重启
    for(volatile int i = 0; i < 3000000; i++);
    
    // 软件复位
    NVIC_SystemReset();
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    printf("*** BUS FAULT EXCEPTION ***\n");
    printf("Bus error occurred!\n");
    printf("This usually indicates:\n");
    printf("1. Invalid memory address access\n");
    printf("2. Peripheral access error\n");
    printf("3. EXMC/FMC configuration error\n");
    printf("4. DMA configuration error\n");
    printf("Program will restart in 3 seconds...\n");
    
    // 延时3秒后重启
    for(volatile int i = 0; i < 3000000; i++);
    
    // 软件复位
    NVIC_SystemReset();
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    printf("*** USAGE FAULT EXCEPTION ***\n");
    printf("Usage fault occurred!\n");
    printf("This usually indicates:\n");
    printf("1. Undefined instruction\n");
    printf("2. Invalid state\n");
    printf("3. Invalid PC load\n");
    printf("4. No coprocessor\n");
    printf("Program will restart in 3 seconds...\n");
    
    // 延时3秒后重启
    for(volatile int i = 0; i < 3000000; i++);
    
    // 软件复位
    NVIC_SystemReset();
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
    printf("*** SVC EXCEPTION ***\n");
    printf("Supervisor call occurred\n");
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
    printf("*** DEBUG MONITOR EXCEPTION ***\n");
    printf("Debug monitor exception occurred\n");
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
    printf("*** PENDSV EXCEPTION ***\n");
    printf("Pendable service call occurred\n");
}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
    // SysTick通常用于系统时钟，不需要特殊处理
    // 如果需要，可以在这里添加系统时钟相关的处理
}
