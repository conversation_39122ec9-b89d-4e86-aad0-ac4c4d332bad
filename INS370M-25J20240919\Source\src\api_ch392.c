#include "appmain.h"
#include "stddef.h"
#include "string.h"
#include "systick.h"
#include "config.h"

#define  SocketNum					2			/*TCP SERVER Socket(SocketNum -1)*/
#define  TcpMss						536			/*TCPMSS*/
unsigned char <PERSON><PERSON><PERSON>er[4096];			/*  */
//struct  _SOCK_INF  SockInf;                      /* Socket */

//#define	CH392_INT_PIN_NUM		GD32F450_PB8_PIN_NUM	//GD32F450_PB6_PIN_NUM
//#define	CH392_RST_PIN_NUM		GD32F450_PE6_PIN_NUM

//#define	ETH_SPI4_NSS_NUM		GD32F450_PH5_PIN_NUM	//GD32F450_PF6_PIN_NUM

//#define	CH392_CS_HIGH			gd32_pin_write(ETH_SPI4_NSS_NUM, PIN_HIGH)
//#define	CH392_CS_LOW			gd32_pin_write(ETH_SPI4_NSS_NUM, PIN_LOW)
//#define	CH392_INT_PIN_WIRE()	gd32_pin_read(CH392_INT_PIN_NUM)
////#define CH395_INT_PIN_INPUT()		(gpio_input_bit_get(CH395_INT_PORT,CH395_INT_PIN))   /* INT */ 

//#define xCH392CmdStart()       CH392_CS_LOW                       /*  */
//#define xEndCH392Cmd()         CH392_CS_HIGH                       /* */

//#include "CH392CMD.C"

//struct _CH392_SYS  CH392Inf;  
												//ping ***********	//	ping ************
const uint8_t CH392IPAddr[4] = {10,250,6,178};	//{192,168,1,10};	//ping ************             
const uint8_t CH392GWIPAddr[4] = {10,250,6,254};	//{192,168,1,1};   //************              
const uint8_t CH392IPMask[4] = {255,255,255,0};                 


/* socket */
const uint8_t  Socket0DesIP[4] = {10,250,6,78};                   /* Socket 0IP */
const uint16_t Socket0SourPort = 8080;                              /* Socket 0 */
const uint16_t Socket0DesPort  = 1000;                              /* Socket 0 */






//static struct ins_spi_configuration ch392_spi_cfg =
//{
//    //.mode = INS_SPI_MODE_0 | INS_SPI_MSB,
//    //.data_width = 8,
//    //.max_hz  = 8000000ul	/* ch378 max freq 30M(spi) */
//};

//static struct gd32_spi_cs  ch392_spi_cs =
//{
// //   .rcu_periph = RCU_SPI4,
// //   .periph = RCU_GPIOF,
// //   .periph1 = RCU_GPIOH,
// //   .periph2 = RCU_GPIOE,
//	//.nss_periph = RCU_GPIOH,
// //   .spi_periph = SPI4,
// //   .GPIOx = GPIOF,
//	//.NSS_GPIOx = GPIOH,
//	//.SCK_GPIOx = GPIOH,
// //   .MISO_GPIOx = GPIOF,
// //   .MOSI_GPIOx = GPIOF,
// //   .RST_GPIOx = GPIOE,
// //   .alt_func_num = GPIO_AF_5,	//6 change to 5
// //   .SPI_NSS_Pin = GPIO_PIN_5,	//PH5
// //   .SPI_SCK_Pin = GPIO_PIN_6,	//PH6
// //   .SPI_MISO_Pin = GPIO_PIN_8,	//0K  PF8
// //   .SPI_MOSI_Pin = GPIO_PIN_9,	//OK  PF9
// //   .SPI_RST_Pin  = CH392_RST_PIN_NUM	//PE6
//};

void CH392_Port_Init( void )
{

    //hw_spi_init(&ch392_spi_cfg, &ch392_spi_cs);
 
}



/*******************************************************************************
 * Function Name  : SPI1_WriteRead
 * Description    : spi 
 * Input          : u8 Data                               
 * Return         : None
 *******************************************************************************/
uint8_t SPI4_WriteRead(uint8_t data)
{  
	//uint8_t retry=0;
	//uint8_t ret = 0;
	
	//while(RESET == spi_i2s_flag_get(SPI4, SPI_FLAG_TBE))
	//{
	//  retry++;
	//	if(retry>200) return 0;
	//}
 //   // Send the byte
 //   spi_i2s_data_transmit(SPI4, data);
 //   while(RESET == spi_i2s_flag_get(SPI4, SPI_FLAG_RBNE))
 //   {
	//  retry++;
	//	if(retry>200) return 0;
	//}
 //   // Get the received data
 //   //return spi_i2s_data_receive(SPI4);
	//ret = spi_i2s_data_receive(SPI4);
	//return ret;
}

/*******************************************************************************
 * Function Name  : SPI1_MasterTransByte
 * Description    : SPI1 Send One Byte   
 * Input          : u8 Data                               
 * Return         : None
 *******************************************************************************/
void SPI4_MasterTransByte(uint8_t Data)
{ 
	SPI4_WriteRead(Data);
}

/*******************************************************************************
 * Function Name  : SPI1_MasterRecvByte
 * Description    : SPI1 Receive One Byte   
 * Input          : None                                 
 * Return         : u8 
 *******************************************************************************/
uint8_t SPI4_MasterRecvByte(void)
{
	return SPI4_WriteRead(0x00);
}

/******************************************************************************
* Function Name  : xWriteCH392Cmd
* Description    : CH392
* Input          : cmd 8
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH392Cmd(uint8_t cmd)                                          
{                                                                    
	//xEndCH392Cmd();                                                  /* CSCD */
	//xCH392CmdStart();                                               /* CS */
	//SPI4_MasterTransByte(cmd);                                       /* SPI */
	//board_delay_us(10);                                                     /* ,1.5uS1.5uS */
}

/******************************************************************************
* Function Name  : xWriteCH392Data
* Description    : CH392
* Input          : mdata 8
* Output         : None
* Return         : None
*******************************************************************************/
void  xWriteCH392Data(uint8_t mdata)
{  
	SPI4_MasterTransByte(mdata);                                     /* SPI */
	delay_ms(2); 
}

/*******************************************************************************
* Function Name  : xReadCH392Data
* Description    : CH392
* Input          : None
* Output         : None
* Return         : 8
*******************************************************************************/
uint8_t   xReadCH392Data( void )                                                  
{
	uint8_t i;

	i = SPI4_MasterRecvByte();                                        /* SPI */
	//board_delay_us(2); 
	return i;
}

uint8_t  CH392Init(void)
{
	//uint8_t i;

	//i = CH392CMDCheckExist(0x65);                      
	//if(i != 0x9a)return CH392_ERR_UNKNOW;                            /* 0509080801090605010604010402070101050803010XFA */
	//CH392CMDSetIPAddr(CH392Inf.IPAddr);                              /* 070001CH3920802IP080100 */
	//CH392CMDSetGWIPAddr(CH392Inf.GWIPAddr);                          /* 07000101030101080100 */
	//CH392CMDSetMASKAddr(CH392Inf.MASKAddr);                          /* 070001070103050300050501020106030209255.255.255.0*/   
	//i = CH392CMDInitCH392();                                         /* 060108040304CH39204060401 */
	//return i;
}



#if 1
/**********************************************************************************
* Function Name  : InitCH392InfParam
* Description    : CH392Inf
* Input          : None
* Output         : None
* Return         : None
**********************************************************************************/
void InitCH392InfParam(void)
{
	//memset(&CH392Inf,0,sizeof(CH392Inf));                            /* CH392Inf*/
	//memcpy(CH392Inf.IPAddr,CH392IPAddr,sizeof(CH392IPAddr));         /* IPCH392Inf */
	//memcpy(CH392Inf.GWIPAddr,CH392GWIPAddr,sizeof(CH392GWIPAddr));   /* IPCH392Inf */
	//memcpy(CH392Inf.MASKAddr,CH392IPMask,sizeof(CH392IPMask));       /* CH392Inf */
}

/**********************************************************************************
* Function Name  : InitSocketParam
* Description    : socket
* Input          : None
* Output         : None
* Return         : None
**********************************************************************************/
void InitSocketParam(void)
{
	//memset(&SockInf,0,sizeof(SockInf));                              /* SockInf[0]*/
	//memcpy(SockInf.IPAddr,Socket0DesIP,sizeof(Socket0DesIP));        /* IP */
	//SockInf.DesPort = Socket0DesPort;                                /*  */
	//SockInf.SourPort = Socket0SourPort;                              /*  */
	//SockInf.ProtoType = PROTO_TYPE_TCP;                              /* TCP */
}

/**********************************************************************************
* Function Name  : CH392SocketInitOpen
* Description    : CH392 socket socket
* Input          : None
* Output         : None
* Return         : None
**********************************************************************************/
//extern	struct _SOCK_INF SockInf;
void CH392SocketInitOpen(void)
{
	//UINT8 i;

	//CH392SetSocketDesIP(0,SockInf.IPAddr);                           /* socket 0IP */         
	//CH392SetSocketProtType(0,SockInf.ProtoType);                     /* socket 0 */
	//CH392SetSocketDesPort(0,SockInf.DesPort);                        /* socket 0 */
	//CH392SetSocketSourPort(0,SockInf.SourPort);                      /* socket 0 */
	//i = CH392OpenSocket(0);                                          /* socket 0 */
	//mStopIfError(i);                                                 /*  */
	//i=CH392TCPListen(0);
	//mStopIfError(i);	
}

/**********************************************************************************
* Function Name  : CH392SocketInterrupt
* Description    : CH392 socket ,
* Input          : sockindex
* Output         : None
* Return         : None
**********************************************************************************/
void CH392GetRecvData(uint8_t sockindex,uint16_t len,unsigned char *pbuf);
void CH392SocketInterrupt(uint8_t sockindex)
{
	//UINT8  sock_int_socket;
	//UINT16 len;

	//sock_int_socket = CH392GetSocketInt(sockindex);                   /* socket  */
	//if(sock_int_socket & SINT_STAT_SENBUF_FREE)                       /*  */
	//{
	//}
	//if(sock_int_socket & SINT_STAT_SEND_OK)                           /*  */
	//{
	//}
	//if(sock_int_socket & SINT_STAT_RECV)                              /*  */
	//{
	//   len = CH392GetRecvLength(sockindex);                          /*  */
	//   printf("receive len = %d\r\n",len);
	//   CH392GetRecvData(sockindex,len,MyBuffer);
	//   CH392SendData(sockindex,MyBuffer,len);
	//}
	//if(sock_int_socket & SINT_STAT_CONNECT)                          /* TCP*/
	//{
	//   printf("SINT_STAT_CONNECT\r\n");
	//}
	//if(sock_int_socket & SINT_STAT_DISCONNECT)                        /* TCP */
	//{
	//  printf("SINT_STAT_DISCONNECT \r\n");
	//}
	//if(sock_int_socket & SINT_STAT_TIM_OUT)                           /* TCP */
	//{                                                                 /*TCP TCP CLIENT*/                                                                   /*Socket*/ 
	//   printf("SINT_STAT_TIM_OUT\r\n");
	//}
}

/**********************************************************************************
* Function Name  : CH392GlobalInterrupt
* Description    : CH392
* Input          : None
* Output         : None
* Return         : None
**********************************************************************************/
void CH392GlobalInterrupt(void)
{
	//UINT16  init_status;
	//UINT8  buf[10]; 

	//init_status = CH392CMDGetGlobIntStatus_ALL();
	//if(init_status & GINT_STAT_UNREACH)                              /*  */
	//{
	//	CH392CMDGetUnreachIPPT(buf);                                
	//}
	//if(init_status & GINT_STAT_IP_CONFLI)                            /* IPCH392 IPCH392*/
	//{
	//}
	//if(init_status & GINT_STAT_PHY_CHANGE)                           /* PHY*/
	//{
	//	printf("Init status : GINT_STAT_PHY_CHANGE\r\n");
	//  }
	//if(init_status & GINT_STAT_SOCK0)
	//{
	//	CH392SocketInterrupt(0);                                     /* socket 0*/
	//}
	//if(init_status & GINT_STAT_SOCK1)                               
	//{
	//	CH392SocketInterrupt(1);                                     /* socket 1*/
	//}
	//if(init_status & GINT_STAT_SOCK2)                                
	//{
	//	CH392SocketInterrupt(2);                                     /* socket 2*/
	//}
	//if(init_status & GINT_STAT_SOCK3)                                
	//{
	//	CH392SocketInterrupt(3);                                     /* socket 3*/
	//}
}
#endif


void InitSocketParam(void);
//#define Query392Interrupt()  PAin(0) 
void ch392_init(void)
{
	//uint8_t i;
	
	//CH392_Port_Init();
	//CH392CMDReset();
	//mDelaymS(100);
	//i = CH392CMDGetVer();
	//InitCH392InfParam();                                        
	//i = CH392Init();        
	//printf("delete warning: %d.\r\n", i);
 //   while(1) {                                                                /* */
 //      if(CH392CMDGetPHYStatus() == PHY_DISCONN) {                   /* CH392 */
 //          delay_ms(200);                                            /* 200MS */
 //      }
 //      else {
 //          printf("CH392 Connect Ethernet\r\n");                       /* CH392 */
 //          break;
 //      }
 //   }
 //   InitSocketParam();                                               /* socket */
 //   CH392SocketInitOpen();
 //   while(1) {
	//	if(CH392_INT_PIN_WIRE()==0) {
	//		CH392GlobalInterrupt();
	//	}
 //   }	
}




