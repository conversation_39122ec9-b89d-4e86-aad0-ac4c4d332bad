# INS600-21A驱动移植到INS370M-25J20240919测试建议

## 移植完成的驱动
1. **GPIO驱动** - LED控制宏定义已修复
2. **UART4驱动** - 完全移植，使用PC12/PD2引脚
3. **UART6驱动** - 完全移植，使用PF6/PF7引脚
4. **SPI驱动** - 已验证完整性
5. **Flash驱动** - 已验证一致性
6. **FMC驱动** - 完全移植，包括SDRAM、SRAM、Flash操作函数

## 测试步骤

### 1. 编译测试
```bash
# 编译项目，确保没有编译错误
# 检查所有新添加的函数和变量声明是否正确
```

### 2. GPIO测试
```c
// 在main函数中添加LED测试代码
void test_gpio_leds(void)
{
    // 测试LED_WHEEL
    LED_WHEEL_ON();
    delay_ms(500);
    LED_WHEEL_OFF();
    delay_ms(500);
    
    // 测试LED_STATE  
    LED_STATE_ON();
    delay_ms(500);
    LED_STATE_OFF();
    delay_ms(500);
}
```

### 3. UART4测试
```c
// 测试UART4发送功能
void test_uart4(void)
{
    char test_msg[] = "UART4 Test Message\r\n";
    uart4sendmsg(test_msg, strlen(test_msg));
}

// 测试UART4接收功能
// 检查USART4_IRQHandler是否正确接收数据
// 验证grxbuffer缓冲区是否正常工作
```

### 4. UART6测试
```c
// 测试UART6初始化和通信
void test_uart6(void)
{
    // 验证UART6是否正确初始化
    // 检查PF6/PF7引脚配置
    // 测试USART6_IRQHandler中断处理
}
```

### 5. SPI测试
```c
// 测试SPI通信功能
void test_spi(void)
{
    // 测试CH395网络芯片（硬件SPI）
    CH395_PORT_INIT();
    CH395_RST();
    uint8_t ver = CH395CMDGetVer();
    printf("CH395 Hardware SPI Version: 0x%02X\n", ver);

    // 测试CH378 USB主机控制器（硬件SPI）
    CH378_Port_Init();
    uint8_t result = mInitCH378Host();
    printf("CH378 Hardware SPI Init: 0x%02X\n", result);
}

// 测试多接口功能
void test_multiple_interfaces(void)
{
    // 测试CH395软件SPI接口
    // 注意：需要根据实际硬件连接选择合适的接口
    printf("Testing CH395 Software SPI...\n");
    // CH395软件SPI初始化和测试代码

    // 测试CH395并行接口
    printf("Testing CH395 Parallel Interface...\n");
    // CH395并行接口初始化和测试代码

    // 测试CH395 UART接口
    printf("Testing CH395 UART Interface...\n");
    // CH395 UART接口初始化和测试代码

    // 测试CH378软件SPI接口
    printf("Testing CH378 Software SPI...\n");
    // CH378软件SPI初始化和测试代码

    // 测试CH378 UART接口
    printf("Testing CH378 UART Interface...\n");
    // CH378 UART接口初始化和测试代码
}
```

### 6. Flash测试
```c
// 测试Flash读写功能
void test_flash(void)
{
    // 测试参数读取
    ReadParaFromFlash();

    // 验证stSetPara变量是否正确初始化
    printf("Setbaud: %d\n", stSetPara.Setbaud);
    printf("Setfre: %d\n", stSetPara.Setfre);
}
```

### 7. FMC测试
```c
// 测试FMC功能
void test_fmc(void)
{
    // 测试SDRAM初始化
    exmc_synchronous_dynamic_ram_init(EXMC_SDRAM_DEVICE0);

    // 测试SRAM初始化
    exmc_asynchronous_sram_init();

    // 测试FPGA通信
    uint16_t test_data = 0x1234;
    uint16_t read_data = 0;

    // 写入测试数据
    FMC_WriteWord(SPACE_COM, 0x100, test_data);

    // 读取测试数据
    read_data = FMC_ReadWord(SPACE_COM, 0x100);

    // 验证数据一致性
    if (read_data == test_data) {
        printf("FMC test passed\n");
    } else {
        printf("FMC test failed\n");
    }

    // 测试DRAM读写
    uint16_t dram_test_data[10] = {0x1111, 0x2222, 0x3333, 0x4444, 0x5555,
                                   0x6666, 0x7777, 0x8888, 0x9999, 0xAAAA};
    uint16_t dram_read_data[10];

    // 写入DRAM
    if (DRam_Write(0x100, dram_test_data, 10)) {
        printf("DRAM write success\n");
    }

    // 从DRAM读取
    if (DRam_Read(0x100, dram_read_data, 10)) {
        printf("DRAM read success\n");

        // 验证数据
        int match = 1;
        for (int i = 0; i < 10; i++) {
            if (dram_test_data[i] != dram_read_data[i]) {
                match = 0;
                break;
            }
        }

        if (match) {
            printf("DRAM data verification passed\n");
        } else {
            printf("DRAM data verification failed\n");
        }
    }

    // 测试Flash操作函数
    uint32_t test_address = 0x08020000;
    int32_t flash_test_data = 0x12345678;
    int32_t flash_read_data = 0;

    // 读取Flash数据
    fmc_read_32bit_data(test_address, 1, &flash_read_data);
    printf("Flash data at 0x%08X: 0x%08X\n", test_address, flash_read_data);
}
```

## 关键验证点

### 1. 中断优先级
- 确认UART4_IRQn和UART6_IRQn中断优先级设置正确
- 验证中断不会相互冲突

### 2. 波特率配置
- 验证stSetPara.Setbaud*100计算是否正确
- 确认gprotocol_send_baudrate6变量值是否合理

### 3. 引脚配置
- UART4: PC12(TX), PD2(RX) - 复用功能AF8
- UART6: PF7(TX), PF6(RX) - 复用功能AF8
- 确认引脚没有冲突

### 4. 缓冲区管理
- 验证grxbuffer、grxlen、grxst等变量正确工作
- 确认缓冲区不会溢出

### 5. FMC功能验证
- 确认SDRAM初始化正常
- 验证SRAM读写操作
- 测试FPGA通信功能
- 检查DRAM缓冲区操作
- 验证Flash扇区操作函数

## 可能的问题和解决方案

### 1. 编译错误
- 检查头文件包含是否完整
- 验证函数声明和定义是否匹配

### 2. 中断不触发
- 检查NVIC配置是否正确
- 验证GPIO复用功能设置

### 3. 数据传输异常
- 检查波特率设置是否正确
- 验证引脚连接是否正常

### 4. 参数读取失败
- 确认Flash地址配置正确
- 验证ReadParaFromFlash函数调用时机

## 测试通过标准

1. ✅ 编译无错误无警告
2. ✅ LED能够正常闪烁
3. ✅ UART4能够正常发送和接收数据
4. ✅ UART6能够正常初始化和通信
5. ✅ SPI功能正常（CH395网络芯片和CH378 USB主机控制器）
6. ✅ 多接口功能正常（硬件SPI、软件SPI、并行接口、UART接口）
7. ✅ Flash参数读取正常
8. ✅ FMC SDRAM/SRAM初始化成功
9. ✅ FPGA通信正常
10. ✅ DRAM读写操作正确
11. ✅ 网络通信功能正常
12. ✅ USB主机功能正常
13. ✅ 文件系统操作正常
14. ✅ 系统整体运行稳定

## 注意事项

1. 测试前确保硬件连接正确
2. 逐步测试，先测试基础功能再测试复杂功能
3. 如果发现问题，请检查移植的代码是否与INS600-21A项目完全一致
4. 建议使用示波器或逻辑分析仪验证信号质量
