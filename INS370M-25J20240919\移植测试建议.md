# INS600-21A驱动移植到INS370M-25J20240919测试建议

## 移植完成的驱动
1. **GPIO驱动** - LED控制宏定义已修复
2. **UART4驱动** - 完全移植，使用PC12/PD2引脚
3. **UART6驱动** - 完全移植，使用PF6/PF7引脚  
4. **SPI驱动** - 已验证完整性
5. **Flash驱动** - 已验证一致性

## 测试步骤

### 1. 编译测试
```bash
# 编译项目，确保没有编译错误
# 检查所有新添加的函数和变量声明是否正确
```

### 2. GPIO测试
```c
// 在main函数中添加LED测试代码
void test_gpio_leds(void)
{
    // 测试LED_WHEEL
    LED_WHEEL_ON();
    delay_ms(500);
    LED_WHEEL_OFF();
    delay_ms(500);
    
    // 测试LED_STATE  
    LED_STATE_ON();
    delay_ms(500);
    LED_STATE_OFF();
    delay_ms(500);
}
```

### 3. UART4测试
```c
// 测试UART4发送功能
void test_uart4(void)
{
    char test_msg[] = "UART4 Test Message\r\n";
    uart4sendmsg(test_msg, strlen(test_msg));
}

// 测试UART4接收功能
// 检查USART4_IRQHandler是否正确接收数据
// 验证grxbuffer缓冲区是否正常工作
```

### 4. UART6测试
```c
// 测试UART6初始化和通信
void test_uart6(void)
{
    // 验证UART6是否正确初始化
    // 检查PF6/PF7引脚配置
    // 测试USART6_IRQHandler中断处理
}
```

### 5. SPI测试
```c
// 测试SPI通信功能
void test_spi(void)
{
    // 使用现有的SPI驱动函数进行测试
    // 验证CH378_SPI_HW.C中的函数是否正常工作
}
```

### 6. Flash测试
```c
// 测试Flash读写功能
void test_flash(void)
{
    // 测试参数读取
    ReadParaFromFlash();
    
    // 验证stSetPara变量是否正确初始化
    printf("Setbaud: %d\n", stSetPara.Setbaud);
    printf("Setfre: %d\n", stSetPara.Setfre);
}
```

## 关键验证点

### 1. 中断优先级
- 确认UART4_IRQn和UART6_IRQn中断优先级设置正确
- 验证中断不会相互冲突

### 2. 波特率配置
- 验证stSetPara.Setbaud*100计算是否正确
- 确认gprotocol_send_baudrate6变量值是否合理

### 3. 引脚配置
- UART4: PC12(TX), PD2(RX) - 复用功能AF8
- UART6: PF7(TX), PF6(RX) - 复用功能AF8
- 确认引脚没有冲突

### 4. 缓冲区管理
- 验证grxbuffer、grxlen、grxst等变量正确工作
- 确认缓冲区不会溢出

## 可能的问题和解决方案

### 1. 编译错误
- 检查头文件包含是否完整
- 验证函数声明和定义是否匹配

### 2. 中断不触发
- 检查NVIC配置是否正确
- 验证GPIO复用功能设置

### 3. 数据传输异常
- 检查波特率设置是否正确
- 验证引脚连接是否正常

### 4. 参数读取失败
- 确认Flash地址配置正确
- 验证ReadParaFromFlash函数调用时机

## 测试通过标准

1. ✅ 编译无错误无警告
2. ✅ LED能够正常闪烁
3. ✅ UART4能够正常发送和接收数据
4. ✅ UART6能够正常初始化和通信
5. ✅ SPI功能正常
6. ✅ Flash参数读取正常
7. ✅ 系统整体运行稳定

## 注意事项

1. 测试前确保硬件连接正确
2. 逐步测试，先测试基础功能再测试复杂功能
3. 如果发现问题，请检查移植的代码是否与INS600-21A项目完全一致
4. 建议使用示波器或逻辑分析仪验证信号质量
