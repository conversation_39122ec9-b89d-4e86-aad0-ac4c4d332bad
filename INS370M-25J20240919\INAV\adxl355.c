#include "appmain.h"
// #include "gdtypedefine.h"
#include "ins.h"
#include "board.h"
// GD32F4xx平台适配
// #include "hpm_clock_drv.h"
// #include "hpm_uart_drv.h"
// #include "uart_dma.h"

// GD32F4xx平台适配 - 注释掉HPM平台特定定义
/*
#define L355_UART HPM_UART3
#define L355_UART_IRQ IRQn_UART3
#define L355_UART_CLK_NAME clock_uart3
#define L355_UART_TX_DMA_REQ HPM_DMA_SRC_UART3_TX
#define L355_UART_RX_DMA_REQ HPM_DMA_SRC_UART3_RX
#define L355_UART_DMA_CONTROLLER HPM_HDMA
#define L355_UART_DMAMUX_CONTROLLER HPM_DMAMUX
#define L355_UART_TX_DMA_CHN (2U)
#define L355_UART_RX_DMA_CHN (3U)
#define L355_UART_DMA_IRQ IRQn_HDMA
*/

void UART7_IRQHandler(void);
uint16_t calculateCRC(uint8_t *data, uint16_t length);
// GD32F4xx平台适配 - 简化缓冲区定义
#define U4RX_MAXCOUNT 1024
static uint8_t l355_uart_rx_buf[U4RX_MAXCOUNT * 2];
static uint8_t l355_uart_tx_buf[U4RX_MAXCOUNT];
bool l355_uart_tx_done;
// 注释掉HPM平台特定的DMA描述符
/*
static dma_linked_descriptor_t l355_tx_descriptors[2];
static dma_linked_descriptor_t l355_rx_descriptors[2];
*/

// GD32F4xx平台适配 - 注释掉HPM平台特定结构
/*
static uart_dma_ctx_t l355_uart =
    {
        .uart_base = L355_UART,
        .uart_src_freq = 0,
        .baudrate = 115200,
        .tx_done = &l355_uart_tx_done,
        .core_id = HPM_CORE0,
        .tx_buff = l355_uart_tx_buf,
        .tx_size = sizeof(l355_uart_tx_buf),
        .rx_buff = l355_uart_rx_buf,
        .rx_size = sizeof(l355_uart_rx_buf),
        .old_rxlen = 0,
        .dmamux_base = L355_UART_DMAMUX_CONTROLLER,
        .dmairq = L355_UART_DMA_IRQ,
        .dmamux_tx_src = L355_UART_TX_DMA_REQ,
        .dmamux_rx_src = L355_UART_RX_DMA_REQ,
        .dma_base = L355_UART_DMA_CONTROLLER,
        .dma_tx_ch = L355_UART_TX_DMA_CHN,
        .dma_rx_ch = L355_UART_RX_DMA_CHN,
        .tx_descriptor = l355_tx_descriptors,
        .rx_descriptor = l355_rx_descriptors,
};
*/

uint8_t uart7RxBuf[15];
void uart7sendmsg2(uint8_t *txbuf, int size)
{
#if 0
    while (!get_uart_tx_idle((void *)&l355_uart))
    {
        printf("4\r\n");
    }
    memcpy(l355_uart.tx_buff, txbuf, size);
    if (uart_dma_output((void *)&l355_uart, l355_uart.tx_buff, size) < 0)
    {
        printf("l355 uart tx send failed!\r\n");
    }
#endif
}

bool adxl355_is_running = false;

// ADXL355 00 06 00 ff 00 0a 38 2c
uint8_t ADXL355_CMD[8] = {0x00, 0x06, 0x00, 0xff, 0x00, 0x0a, 0x38, 0x2c};

void ADXL355_UART7_Init(void)
{
#if 0
    board_init_uart(L355_UART);
    l355_uart.uart_src_freq = clock_get_frequency(L355_UART_CLK_NAME);
    l355_uart.baudrate = 115200;

    if (uart_dma_init((void *)&l355_uart) != 0)
    {
        printf("BAD! l355 uart dma init fail!\r\n");
        while (1)
            ;
    }
#endif
}

typedef enum
{
    RCV_STATE_PID = 1,
    RCV_STATE_CMD = 2,
    RCV_STATE_LEN = 3,
    RCV_STATE_DAT = 4,
    RCV_STATE_CRC1 = 5,
    RCV_STATE_CRC2 = 6
} ADXL355_PRO_STATE;

MYUNION temp66;

uint8_t rx = 0;
uint8_t rx_num = 0;
uint16_t crc16_cal = 0;
uint16_t crc16_rcv = 0;

uint32_t data3 = 0, data2 = 0, data1 = 0;
uint32_t datau = 0;
int32_t datai = 0;

double adxl355_ax = 0.0;
double adxl355_ay = 0.0;
double adxl355_az = 0.0;

unsigned short adlxdata[12];

ADXL355_PRO_STATE RCV_STATE = RCV_STATE_PID;

// note: must polling
void l355_uart_recv_polling(void)
{
    return;
#if 0
    uint32_t i;
    uint8_t recv_buff[15];
    uint32_t len = uart_dma_recv_polling((void *)&l355_uart, recv_buff, sizeof(recv_buff));
    if (len == 0)
    {
        return;
    }
    for (i = 0; i < len; i++)
    {
        rx = recv_buff[i];
        switch (RCV_STATE)
        {
        case RCV_STATE_PID:
            if (0x00 == rx)
            {
                rx_num = 0;
                memset(uart7RxBuf, 0, sizeof(uart7RxBuf));
                uart7RxBuf[rx_num] = rx;
                RCV_STATE = RCV_STATE_CMD;
            }
            else
            {
                rx_num = 0;
                memset(uart7RxBuf, 0, sizeof(uart7RxBuf));
                RCV_STATE = RCV_STATE_PID;
            }
            break;

        case RCV_STATE_CMD:
            if (0x03 == rx)
            {
                rx_num++;
                uart7RxBuf[rx_num] = rx;
                RCV_STATE = RCV_STATE_LEN;
            }
            else
            {
                rx_num = 0;
                memset(uart7RxBuf, 0, sizeof(uart7RxBuf));
                RCV_STATE = RCV_STATE_PID;
            }
            break;

        case RCV_STATE_LEN:
            if (0x09 == rx)
            {
                rx_num++;
                uart7RxBuf[rx_num] = rx;
                RCV_STATE = RCV_STATE_DAT;
            }
            else
            {
                rx_num = 0;
                memset(uart7RxBuf, 0, sizeof(uart7RxBuf));
                RCV_STATE = RCV_STATE_PID;
            }
            break;

        case RCV_STATE_DAT:
            if (rx_num < 10)
            {
                rx_num++;
                uart7RxBuf[rx_num] = rx;
                RCV_STATE = RCV_STATE_DAT;
            }
            else
            {
                rx_num++;
                uart7RxBuf[rx_num] = rx;
                crc16_cal = calculateCRC(uart7RxBuf, rx_num + 1);
                RCV_STATE = RCV_STATE_CRC1;
            }
            break;

        case RCV_STATE_CRC1:
            rx_num++;
            uart7RxBuf[rx_num] = rx;
            RCV_STATE = RCV_STATE_CRC2;
            break;

        case RCV_STATE_CRC2:
            rx_num++;
            uart7RxBuf[rx_num] = rx;
            crc16_rcv = uart7RxBuf[rx_num] << 8 | uart7RxBuf[rx_num - 1];
            if (crc16_rcv == crc16_cal)
            {
                data3 = (uint32_t)uart7RxBuf[3];
                data2 = (uint32_t)uart7RxBuf[4];
                data1 = (uint32_t)uart7RxBuf[5];
                datau = data3 << 12 | data2 << 4 | data1 >> 4;
                if (datau & 0x80000)
                    datai = datau | 0xfff00000; // 20
                else
                    datai = datau;

                adxl355_ax = datai / 64000.0;
                temp66.datai = datai;
                // gfpgadata[5] = temp33.buf[0];
                // gfpgadata[6] = temp33.buf[1];
                adlxdata[0] = temp66.buf[0];
                adlxdata[1] = temp66.buf[1];

                data3 = (uint32_t)uart7RxBuf[6];
                data2 = (uint32_t)uart7RxBuf[7];
                data1 = (uint32_t)uart7RxBuf[8];
                datau = data3 << 12 | data2 << 4 | data1 >> 4;
                if (datau & 0x80000)
                    datai = datau | 0xfff00000; // 20
                else
                    datai = datau;
                adxl355_ay = datai / 64000.0;

                temp66.datai = datai;
                // gfpgadata[7] = temp33.buf[0];
                // gfpgadata[8] = temp33.buf[1];
                adlxdata[2] = temp66.buf[0];
                adlxdata[3] = temp66.buf[1];

                data3 = (uint32_t)uart7RxBuf[9];
                data2 = (uint32_t)uart7RxBuf[10];
                data1 = (uint32_t)uart7RxBuf[11];
                datau = data3 << 12 | data2 << 4 | data1 >> 4;
                if (datau & 0x80000)
                    datai = datau | 0xfff00000; // 20
                else
                    datai = datau;
                adxl355_az = datai / 64000.0;

                temp66.datai = datai;
                // gfpgadata[9] = temp33.buf[0];
                // gfpgadata[10] = temp33.buf[1];
                adlxdata[4] = temp66.buf[0];
                adlxdata[5] = temp66.buf[1];

                adxl355_is_running = true;
            }
            RCV_STATE = RCV_STATE_PID;
            break;

        default:
            rx_num = 0;
            RCV_STATE = RCV_STATE_PID;
            break;
        }
    }
#endif
}

uint16_t calculateCRC(uint8_t *data, uint16_t length)
{
    uint16_t crc = 0xFFFF;
    uint16_t polynomial = 0xA001;

    for (uint16_t i = 0; i < length; ++i)
    {
        crc ^= data[i];

        for (uint8_t j = 0; j < 8; ++j)
        {
            if (crc & 0x0001)
            {
                crc >>= 1;
                crc ^= polynomial;
            }
            else
            {
                crc >>= 1;
            }
        }
    }

    return crc;
}
