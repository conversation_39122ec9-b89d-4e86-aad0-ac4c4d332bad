#ifndef __GOL_FRAME_ANALYSIS_C__
#define __GOL_FRAME_ANALYSIS_C__

#include "string.h"
#include "math.h"
#include "frame_analysis.h"
//#include "pin_numbers_def.h"

//#include "drv_gpio.h"
//#include "drv_rtc.h"

//#include "exmc_sram.h"
#include "uartadapter.h"
#include "gnss.h"
//#include "DRamAdapter.h"
#include "computerFrameParse.h"
//#include "nav_task.h"
//#include "nav.h"
#include "gdtypedefine.h"
#include "appdefine.h"
#include "CONST.h"
#include "TYPEDEFINE.h"


RS422_FRAME_DEFx	rs422_frame;
RS422_FRAME_DEFx	grs422_frame;
navi_test_t			grs422_frameD;
GPSDataTypeDef		ggpsorgdata;

//传递陀螺，加速度计数据
extern ANGRATE r_Gyro[3];

extern ACCELER Acc[3];


uint8_t xor_check(uint8_t *buf, uint16_t len)
{
    uint16_t i = 0;
    uint8_t x = 0;

    for(; i < len; i++)
    {
        x = x ^ (*(buf + i));
    }

    return x;
}

uint8_t sum_check(uint8_t *buf, uint16_t len )
{
    uint8_t checksum = 0, i;

    for (i = 0; i < len; i++)
    {
        checksum += buf[i];
    }

    checksum  = ~checksum;

    return checksum;

}
//参考PA-IMU-460.PDF
#define IMU_FRAME_HEADER_LSB                  0x7F
#define IMU_FRAME_HEADER_MSB                  0x80
//IMU_DATA_TypeDef imu_info;
#define	AXIS_INITIAL_X	1
#define	AXIS_INITIAL_Y	2
#define	AXIS_INITIAL_Z	3
typedef struct
{
    uint8_t index;
    int8_t	axis[3];
} AXIS_TypeDef;
AXIS_TypeDef axisInfo;
const AXIS_TypeDef axisTab[] =
{
    {0, AXIS_INITIAL_X, AXIS_INITIAL_Y, AXIS_INITIAL_Z},
    {1, AXIS_INITIAL_X, AXIS_INITIAL_Y, AXIS_INITIAL_Z},
    {2, AXIS_INITIAL_X, -AXIS_INITIAL_Y, -AXIS_INITIAL_Z},
    {3, AXIS_INITIAL_X, AXIS_INITIAL_Z, -AXIS_INITIAL_Y},
    {4, AXIS_INITIAL_X, -AXIS_INITIAL_Z, AXIS_INITIAL_Y},
    {5, -AXIS_INITIAL_X, AXIS_INITIAL_Y, -AXIS_INITIAL_Z},
    {6, -AXIS_INITIAL_X, -AXIS_INITIAL_Y, AXIS_INITIAL_Z},
    {7, -AXIS_INITIAL_X, AXIS_INITIAL_Z, AXIS_INITIAL_Y},
    {8, -AXIS_INITIAL_X, -AXIS_INITIAL_Z, -AXIS_INITIAL_Y},
    {9, AXIS_INITIAL_Y, AXIS_INITIAL_X, -AXIS_INITIAL_Z},
    {10, AXIS_INITIAL_Y, -AXIS_INITIAL_X, AXIS_INITIAL_Z},
    {11, AXIS_INITIAL_Y, AXIS_INITIAL_Z, AXIS_INITIAL_X},
    {12, AXIS_INITIAL_Y, -AXIS_INITIAL_Z, -AXIS_INITIAL_X},
    {13, -AXIS_INITIAL_Y, AXIS_INITIAL_X, AXIS_INITIAL_Z},
    {14, -AXIS_INITIAL_Y, -AXIS_INITIAL_X, -AXIS_INITIAL_Z},
    {15, -AXIS_INITIAL_Y, AXIS_INITIAL_Z, -AXIS_INITIAL_X},
    {16, -AXIS_INITIAL_Y, -AXIS_INITIAL_Z, AXIS_INITIAL_X},
    {17, AXIS_INITIAL_Z, AXIS_INITIAL_X, AXIS_INITIAL_Y},
    {18, AXIS_INITIAL_Z, -AXIS_INITIAL_X, -AXIS_INITIAL_Y},
    {19, AXIS_INITIAL_Z, AXIS_INITIAL_Y, -AXIS_INITIAL_X},
    {20, AXIS_INITIAL_Z, -AXIS_INITIAL_Y, AXIS_INITIAL_X},
    {21, -AXIS_INITIAL_Z, AXIS_INITIAL_X, -AXIS_INITIAL_Y},
    {22, -AXIS_INITIAL_Z, -AXIS_INITIAL_X, AXIS_INITIAL_Y},
    {23, -AXIS_INITIAL_Z, AXIS_INITIAL_Y, AXIS_INITIAL_X},
    {24, -AXIS_INITIAL_Z, -AXIS_INITIAL_Y, -AXIS_INITIAL_X}
};

navi_test_t	navi_test_info;
char navi_test_str[600] = {0,};
uint8_t navi_test_sendBuf[600];
static uint32_t			xgpsTrueTime, xgpsTime = 0;
void frame_imu_and_gnss_send(void* pnav, void *gps)
{
//	uint8_t ppsDelay = 0;
//    uint32_t len;
//    uint8_t id, err = 0;
//    uint16_t freq;
//    //rtc_update_struct* rtc;
//    _NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
//    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;
//#if 0
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460

//    navi_test_info.accelX = imuParseData.accelGrp[0];
//    navi_test_info.gyroX  = imuParseData.gyroGrp[0];
//    navi_test_info.accelY = imuParseData.accelGrp[1];
//    navi_test_info.gyroY  = imuParseData.gyroGrp[1];
//    navi_test_info.accelZ = imuParseData.accelGrp[2];
//    navi_test_info.gyroZ  = imuParseData.gyroGrp[2];
//    navi_test_info.sensor_temp = combineData.imuInfo.sensorTemp;
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//    navi_test_info.accelX = combineData.scha634Info.acc_x;
//    navi_test_info.gyroX  = combineData.scha634Info.gyro_x;
//    navi_test_info.accelY = combineData.scha634Info.acc_y;
//    navi_test_info.gyroY  = combineData.scha634Info.gyro_y;
//    navi_test_info.accelZ = combineData.scha634Info.acc_z;
//    navi_test_info.gyroZ  = combineData.scha634Info.gyro_z;
//    navi_test_info.sensor_temp = combineData.imuInfo.sensorTemp;
//    navi_test_info.sensor_temp = (combineData.scha634Info.temp_due + combineData.scha634Info.temp_uno) / 2;
//#endif

//    if(combineData.imuSelect)
//        navi_test_info.gyroZ  = combineData.ifogInfo.gyroGrp[2];

//#else
//    axisInfo.index = comm_axis_read();
//    axisInfo.axis[0] = axisTab[axisInfo.index].axis[0];
//    axisInfo.axis[1] = axisTab[axisInfo.index].axis[1];
//    axisInfo.axis[2] = axisTab[axisInfo.index].axis[2];

//    id = abs(axisInfo.axis[0]);
//    id -= 1;

//    if(axisInfo.axis[0] > 0)
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelX = imuParseData.accelGrp[id];
//        navi_test_info.gyroX  = imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelX = combineData.scha634Info.acc_x;
//        navi_test_info.gyroX  = combineData.scha634Info.gyro_x;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//			navi_test_info.accelX = combineData.adis16465Info.acc_x;
//			navi_test_info.gyroX  = combineData.adis16465Info.gyro_x;
//#endif
//    }
//    else
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelX = -imuParseData.accelGrp[id];
//        navi_test_info.gyroX  = -imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelX = -combineData.scha634Info.acc_x;
//        navi_test_info.gyroX  = -combineData.scha634Info.gyro_x;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//		navi_test_info.accelX = -combineData.adis16465Info.acc_x;
//		navi_test_info.gyroX  = -combineData.adis16465Info.gyro_x;
//#endif
//    }

//    id = abs(axisInfo.axis[1]);
//    id -= 1;

//    if(axisInfo.axis[1] > 0)
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelY = imuParseData.accelGrp[id];
//        navi_test_info.gyroY  = imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelY = combineData.scha634Info.acc_y;
//        navi_test_info.gyroY  = combineData.scha634Info.gyro_y;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//				navi_test_info.accelY = combineData.adis16465Info.acc_y;
//				navi_test_info.gyroY  = combineData.adis16465Info.gyro_y;
//#endif
//    }
//    else
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelY = -imuParseData.accelGrp[id];
//        navi_test_info.gyroY  = -imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelY = -combineData.scha634Info.acc_y;
//        navi_test_info.gyroY  = -combineData.scha634Info.gyro_y;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//				navi_test_info.accelY = -combineData.adis16465Info.acc_y;
//				navi_test_info.gyroY  = -combineData.adis16465Info.gyro_y;
//#endif

//    }

//    id = abs(axisInfo.axis[2]);
//    id -= 1;

//    if(axisInfo.axis[2] > 0)
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelZ = imuParseData.accelGrp[id];
//        navi_test_info.gyroZ  = imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelZ = combineData.scha634Info.acc_z;
//        navi_test_info.gyroZ  = combineData.scha634Info.gyro_z;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//				navi_test_info.accelZ = combineData.adis16465Info.acc_z;
//				navi_test_info.gyroZ  = combineData.adis16465Info.gyro_z;
//#endif

//    }
//    else
//    {
//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//        navi_test_info.accelZ = -imuParseData.accelGrp[id];
//        navi_test_info.gyroZ  = -imuParseData.gyroGrp[id];
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//        navi_test_info.accelZ = -combineData.scha634Info.acc_z;
//        navi_test_info.gyroZ  = -combineData.scha634Info.gyro_z;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//				navi_test_info.accelZ = combineData.adis16465Info.acc_z;
//				navi_test_info.gyroZ  = combineData.adis16465Info.gyro_z;
//#endif

//    }

//#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
//    navi_test_info.sensor_temp = combineData.imuInfo.sensorTemp;
//#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//    navi_test_info.sensor_temp = (combineData.scha634Info.temp_due + combineData.scha634Info.temp_uno) / 2;
//#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
//		navi_test_info.sensor_temp = combineData.adis16465Info.temp;
//#endif


//    if(combineData.imuSelect)
//        navi_test_info.gyroZ  = combineData.ifogInfo.gyroGrp[2];

//#endif
//    //if(navi_test_info.gps_valid == 0)navi_test_info.timestamp = 0.0;

////    rtc = rtc_update();
////    navi_test_info.imuTimestamp = rtc->gpsTime;
//    //////////////////////////////////////////////////////////////////////
//    freq = comm_read_currentFreq();

//    if(navi_test_info.gps_valid == 0)
//        ppsDelay = 0;
//    else
//    {
//        ppsDelay = navi_test_info.pps_cnt;
//    }


//    if(navi_test_info.pps_en)
//    {
//        navi_test_info.counter = 0;
//        xgpsTrueTime = gnss->gpssecond - gnss->gpssecond % 1000;
//    }
//    else
//    {
//    	xgpsTrueTime += (1000 / freq);
//        navi_test_info.counter++;

//        if(navi_test_info.counter > 199)err = 1;
//    }

//    navi_test_info.pps_cnt++;

//    ////////////////////////////////////////////////////////////////////////
//#if 0
//    sprintf(navi_test_str, "#imugps,%d,%0.3f,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%d,%d,%d,%d,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.7f,%0.7f,%0.3f,%0.5f,%0.5f,%0.5f,%0.5f\r\n", \
//            navi_test_info.counter, navi_test_info.imuTimestamp, xgpsTrueTime, navi_test_info.accelX, navi_test_info.accelY, navi_test_info.accelZ, \
//            navi_test_info.gyroX, navi_test_info.gyroY, navi_test_info.gyroZ, navi_test_info.sensor_temp, navi_test_info.gps_valid, navi_test_info.gpsWeek, \
//            navi_test_info.gpsSec, navi_test_info.starNum, navi_test_info.rtkStatus, navi_test_info.lon, navi_test_info.lat, navi_test_info.alt, \
//            navi_test_info.vu, navi_test_info.heading, navi_test_info.pitch, navi_test_info.lon_std, navi_test_info.lat_std, navi_test_info.alt_std, \
//            navi_test_info.hdgstddev, navi_test_info.ptchstddev, navi_test_info.hdop, \
//            result->pitch, result->roll, result->heading, result->ve, result->vn, result->vu, result->latitude, result->longitude, result->altitude, \
//            combineData.canInfo.data.WheelSpeed_Front_Left, combineData.canInfo.data.WheelSpeed_Back_Left, combineData.canInfo.data.WheelSpeed_Front_Right, combineData.canInfo.data.WheelSpeed_Back_Right);
//#else
//    sprintf(navi_test_str, "#imugps,%d,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%d,%d,%d,%d,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%d\r\n", \
//            navi_test_info.counter, xgpsTrueTime, navi_test_info.accelX, navi_test_info.accelY, navi_test_info.accelZ, \
//            navi_test_info.gyroX, navi_test_info.gyroY, navi_test_info.gyroZ, navi_test_info.sensor_temp, navi_test_info.pps_en, navi_test_info.gps_valid, ppsDelay, \
//            navi_test_info.starNum, navi_test_info.rtkStatus, navi_test_info.lon, navi_test_info.lat, navi_test_info.alt, \
//            navi_test_info.vu, navi_test_info.hdop, navi_test_info.trackTrue, err);
//    //sprintf(navi_test_str, "#imugps,%d,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%d,%d\r\n", \
//    //		  navi_test_info.counter, xgpsTrueTime, navi_test_info.accelX, navi_test_info.accelY, navi_test_info.accelZ, navi_test_info.gyroX, navi_test_info.gyroY, navi_test_info.gyroZ,err, hGPSData.ppsDelay);
//#endif
//    //	  sprintf(navi_test_str, "#imugps,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.7f,%0.7f,%0.3f,%0.5f,%0.5f,%0.5f,%0.5f\r\n", \
//    //			  result->pitch, result->roll, result->heading, result->ve, result->vn, result->vu, result->latitude, result->longitude, result->altitude, \
//    //			  combineData.canInfo.data.WheelSpeed_Front_Left, combineData.canInfo.data.WheelSpeed_Back_Left, combineData.canInfo.data.WheelSpeed_Front_Right, combineData.canInfo.data.WheelSpeed_Back_Right);
//    len = strlen(navi_test_str);
//    memcpy((void*)&navi_test_sendBuf[0], (void*)navi_test_str, len);
//    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, navi_test_sendBuf);
//    navi_test_info.pps_en = 0; 
//    navi_test_info.gps_valid = 0;
//    hGPSData.ppsDelay = 0;
}

void frame_navi_and_gnss_send(void* pnav, void *gps)
{
//    uint16_t len, freq;
//    rtc_update_struct* rtc;
//    _NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
//    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

//    rtc = rtc_update();
//    navi_test_info.imuTimestamp = rtc->gpsTime;
//    freq = comm_read_currentFreq();

//    if(xgpsTime != gnss->gpssecond)
//    {
//        xgpsTime = gnss->gpssecond;

//        if(xgpsTrueTime == xgpsTime)
//        {
//            xgpsTrueTime += (1000 / freq);
//        }
//        else
//        {
//            xgpsTrueTime = xgpsTime;
//        }
//    }
//    else
//    {
//        xgpsTrueTime += (1000 / freq);
//    }

//    sprintf(navi_test_str, "#navgps,%d,%0.5f,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%d,%d\r\n", \
//            navi_test_info.counter, navi_test_info.imuTimestamp, xgpsTrueTime, result->pitch, result->roll, result->heading, \
//            result->ve, result->vn, result->vu, result->latitude, result->longitude, result->altitude, \
//            gnss->Pitch, gnss->Roll, gnss->Heading, gnss->ve, gnss->vn, gnss->vu, \
//            gnss->Lat, gnss->Lon, gnss->Altitude, gnss->StarNum, gnss->rtkStatus);
//    len = strlen(navi_test_str);
//    memcpy((void*)&navi_test_sendBuf[0], (void*)navi_test_str, len);
//    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, navi_test_sendBuf);
//    navi_test_info.counter++;
}

void frame_miscel_send(void* pcomb, void *gps, void* pnav)
{
	#if 0
	uint8_t ppsDelay = 0;
	uint16_t len,freq;
	CombineDataTypeDef* comb = (CombineDataTypeDef*)pcomb;
	GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;
	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
	
    freq = comm_read_currentFreq();

    if(navi_test_info.gps_valid == 0)
        ppsDelay = 0;
    else
    {
        ppsDelay = navi_test_info.pps_cnt;
    }


    if(navi_test_info.pps_en)
    {
        navi_test_info.counter = 0;
        xgpsTrueTime = gnss->gpssecond - gnss->gpssecond % 1000;
    }
    else
    {
    	xgpsTrueTime += (1000 / freq);
        navi_test_info.counter++;
    }
#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
        navi_test_info.accelX = combineData.imuInfo.accelGrp[0];
        navi_test_info.accelY = combineData.imuInfo.accelGrp[1];
        navi_test_info.accelZ = combineData.imuInfo.accelGrp[2];
        navi_test_info.gyroX = combineData.imuInfo.gyroGrp[0];
        navi_test_info.gyroY = combineData.imuInfo.gyroGrp[1];
        navi_test_info.gyroZ = combineData.imuInfo.gyroGrp[2];
        navi_test_info.sensor_temp = combineData.imuInfo.sensorTemp;
#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
        navi_test_info.accelX = combineData.scha634Info.acc_x;
        navi_test_info.accelY = combineData.scha634Info.acc_y;
        navi_test_info.accelZ = combineData.scha634Info.acc_z;
        navi_test_info.gyroX = combineData.scha634Info.gyro_x;
        navi_test_info.gyroY = combineData.scha634Info.gyro_y;
        navi_test_info.gyroZ = combineData.scha634Info.gyro_z;
        navi_test_info.sensor_temp = combineData.scha634Info.temp_due;
#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
		navi_test_info.accelX = combineData.adis16465Info.acc_x;
        navi_test_info.accelY = combineData.adis16465Info.acc_y;
        navi_test_info.accelZ = combineData.adis16465Info.acc_z;
        navi_test_info.gyroX = combineData.adis16465Info.gyro_x;
        navi_test_info.gyroY = combineData.adis16465Info.gyro_y;
        navi_test_info.gyroZ = combineData.adis16465Info.gyro_z;
        navi_test_info.sensor_temp = combineData.adis16465Info.temp;
#endif
    navi_test_info.pps_cnt++;
	#if c_outputmode == c_outputmode_normal
    sprintf(navi_test_str, "#navgps,%d,%d,%d,%d,%d,%d,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%d,%d,%d,%d,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%d,%d,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%d,%d,%d,%d,%d,%d,%d,%.7f\r\n", \
            navi_test_info.counter,comb->gnssInfo.gpsweek,comb->gnssInfo.gpssecond,navi_test_info.pps_en,navi_test_info.gps_valid,ppsDelay, \
            navi_test_info.gyroX,navi_test_info.gyroY,navi_test_info.gyroZ,navi_test_info.sensor_temp,navi_test_info.accelX,navi_test_info.accelY,navi_test_info.accelZ, \
            comb->gnssInfo.Lon,comb->gnssInfo.Lat,comb->gnssInfo.Altitude,comb->gnssInfo.ve,comb->gnssInfo.vn,comb->gnssInfo.vu, \
            comb->gnssInfo.Pitch,comb->gnssInfo.Roll,comb->gnssInfo.Heading,comb->gnssInfo.PositioningState,comb->gnssInfo.rtkStatus,comb->gnssInfo.StarNum, \
            comb->canInfo.counter,comb->canInfo.data.WheelSpeed_Front_Left,comb->canInfo.data.WheelSpeed_Front_Right,comb->canInfo.data.WheelSpeed_Back_Left,comb->canInfo.data.WheelSpeed_Back_Right, \
            comb->canInfo.data.WheelSteer,comb->canInfo.data.OdoPulse_1,comb->canInfo.data.OdoPulse_2,comb->canInfo.data.Gear, \
            comb->Adj.Nav_Standard_flag,comb->Adj.gnssAtt_from_vehicle2[0],comb->Adj.gnssAtt_from_vehicle2[1],comb->Adj.gnssAtt_from_vehicle2[2], \
            comb->Adj.acc_off[0],comb->Adj.acc_off[1],comb->Adj.acc_off[2],comb->Adj.gyro_off[0],comb->Adj.gyro_off[1],comb->Adj.gyro_off[2],gnss->trackTrue,\
            result->Nav_Status,result->Nav_Standard_flag,result->imuSelect,result->memsType,result->use_gps_flag,result->fusion_source,comb->gnssInfo.headingStatus, comb->gnssInfo.gpssecond982);
    len = strlen(navi_test_str);
    memcpy((void*)&navi_test_sendBuf[0], (void*)navi_test_str, len);
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, navi_test_sendBuf);
    navi_test_info.pps_en = 0; 
    navi_test_info.gps_valid = 0;
	#endif
	
	#if c_outputmode == c_outputmode_gdw
	gdwrxdataTX_t	tempd;
	tempd.gdwdata.navi_test_info_counter = navi_test_info.counter / 2;
	tempd.gdwdata.gnssInfo_gpsweek = comb->gnssInfo.gpsweek;
	tempd.gdwdata.gnssInfo_gpssecond = comb->gnssInfo.gpssecond;
	tempd.gdwdata.navi_test_info_pps_en = navi_test_info.pps_en;
	tempd.gdwdata.navi_test_info_gps_valid = navi_test_info.gps_valid;
	tempd.gdwdata.ppsDelay = ppsDelay;
	tempd.gdwdata.navi_test_info_gyroX = navi_test_info.gyroX;
	tempd.gdwdata.navi_test_info_gyroY = navi_test_info.gyroY;
	tempd.gdwdata.navi_test_info_gyroZ = navi_test_info.gyroZ;
	tempd.gdwdata.navi_test_info_sensor_temp = navi_test_info.sensor_temp;
	tempd.gdwdata.navi_test_info_accelX = navi_test_info.accelX;
	tempd.gdwdata.navi_test_info_accelY = navi_test_info.accelY;
	tempd.gdwdata.navi_test_info_accelZ = navi_test_info.accelZ;
	tempd.gdwdata.gnssInfo_Lon = comb->gnssInfo.Lon;
	tempd.gdwdata.gnssInfo_Lat = comb->gnssInfo.Lat;
	tempd.gdwdata.gnssInfo_Altitude = comb->gnssInfo.Altitude;
	tempd.gdwdata.gnssInfo_ve = comb->gnssInfo.ve;
	tempd.gdwdata.gnssInfo_vn = comb->gnssInfo.vn;
	tempd.gdwdata.gnssInfo_vu = comb->gnssInfo.vu;
	tempd.gdwdata.gnssInfo_Pitch = comb->gnssInfo.Pitch;
	tempd.gdwdata.gnssInfo_Roll = comb->gnssInfo.Roll;
	tempd.gdwdata.gnssInfo_Heading = comb->gnssInfo.Heading;
	tempd.gdwdata.gnssInfo_PositioningState = comb->gnssInfo.PositioningState;
	tempd.gdwdata.gnssInfo_rtkStatus = comb->gnssInfo.rtkStatus;
	tempd.gdwdata.gnssInfo_StarNum = comb->gnssInfo.StarNum;
	tempd.gdwdata.canInfo_counter = comb->canInfo.counter;
	tempd.gdwdata.canInfo_data_WheelSpeed_Front_Left = comb->canInfo.data.WheelSpeed_Front_Left;
	tempd.gdwdata.canInfo_data_WheelSpeed_Front_Right = comb->canInfo.data.WheelSpeed_Front_Right;
	tempd.gdwdata.canInfo_data_WheelSpeed_Back_Left = comb->canInfo.data.WheelSpeed_Back_Left;
	tempd.gdwdata.canInfo_data_WheelSpeed_Back_Right = comb->canInfo.data.WheelSpeed_Back_Right;
	tempd.gdwdata.canInfo_data_WheelSteer = comb->canInfo.data.WheelSteer;
	tempd.gdwdata.canInfo_data_OdoPulse_1 = comb->canInfo.data.OdoPulse_1;
	tempd.gdwdata.canInfo_data_OdoPulse_2 = comb->canInfo.data.OdoPulse_2;
	tempd.gdwdata.canInfo_data_Gear = comb->canInfo.data.Gear;
	tempd.gdwdata.Adj_Nav_Standard_flag = comb->Adj.Nav_Standard_flag;
	tempd.gdwdata.Adj_gnssAtt_from_vehicle2_0 = comb->Adj.gnssAtt_from_vehicle2[0];
	tempd.gdwdata.Adj_gnssAtt_from_vehicle2_1 = comb->Adj.gnssAtt_from_vehicle2[1];
	tempd.gdwdata.Adj_gnssAtt_from_vehicle2_2 = comb->Adj.gnssAtt_from_vehicle2[2];
	tempd.gdwdata.Adj_acc_off_0 = comb->Adj.acc_off[0];
	tempd.gdwdata.Adj_acc_off_1 = comb->Adj.acc_off[1];
	tempd.gdwdata.Adj_acc_off_2 = comb->Adj.acc_off[2];
	tempd.gdwdata.Adj_gyro_off_0 = comb->Adj.gyro_off[0];
	tempd.gdwdata.Adj_gyro_off_1 = comb->Adj.gyro_off[1];
	tempd.gdwdata.Adj_gyro_off_2 = comb->Adj.gyro_off[2];
	tempd.gdwdata.gnss_trackTrue = gnss->trackTrue;
	tempd.gdwdata.result_Nav_Status = result->Nav_Status;
	tempd.gdwdata.result_Nav_Standard_flag = result->Nav_Standard_flag;
	tempd.gdwdata.result_imuSelect = result->imuSelect;
	tempd.gdwdata.result_memsType = result->memsType;
	tempd.gdwdata.result_use_gps_flag = result->use_gps_flag;
	tempd.gdwdata.result_fusion_source = result->fusion_source;
	tempd.gdwdata.gnssInfo_headingStatus = comb->gnssInfo.headingStatus;
	tempd.gdwdata.gnssInfo_gpssecond982 = comb->gnssInfo.gpssecond982;
	//tempd.gdwdata.fog0 = gfog0;
	
	
	unsigned char *ptmpu8;
	unsigned short checksum;
	ptmpu8 = (unsigned char *)&tempd;
	checksum = 0;
	//gimuorgdatasendtopc.cacv = scha63x_cac_values;
	tempd.head = 0x55aa;
	tempd.len = sizeof(tempd);
	tempd.type = DRIVERSDATATYPE_GDW;
	tempd.packet = ggdworgdata_packet++;

	__disable_irq();    //----------------------------------
	tempd.packetT = gdriverspacket;
	for (int i = 0; i < sizeof(tempd) - 2; i++) {
		checksum += *ptmpu8++;
	}
	tempd.checksum = checksum;
	if (gdriverdatalist.size < GD_DRIVERSDATA_MAXCOUNT) {
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driversdatatype = DRIVERSDATATYPE_GDW;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driverspacket = gdriverspacket;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].data.gdw = tempd;
		gdriverspacket++;
		gdriverdatalist.size++;
	}       
	__enable_irq();     //----------------------------------
	#endif
	#endif
}
//此函数输出若干测试数据
void frame_form(void)
{
//    static uint8_t tCnt = 0;
//    uint8_t freq = (200 / comm_read_currentFreq());
//    tCnt++;

//    if(tCnt >= freq)
//    {
//        tCnt = 0;

//        switch(combineData.outputType)
//        {
//            case 1:
//                frame_navi_and_gnss_send(&NAV_Data_Out, &hGPSData);
//                break;

//            case 2://波特率需要设置成921600
//                frame_imu_and_gnss_send(&NAV_Data_Out, &hGPSData);
//                break;

//			case 3://波特率需要设置成921600
//                frame_miscel_send(&combineData, &hGPSData,&NAV_Data_Out);
//                break;
//                
//            default:
//                break;
//        }
//    }
}
//imu460数据解析
uint8_t frame_fill_imu(uint8_t* pData, uint16_t dataLen)
{
#define	Accel_Scale 	20.0
#define	Rate_Scale 		1260.0
#define	Angle_Scale 	360.0
#define	Temp_Scale		200.0
#define	Sensor_Scale 	65536.0

    uint8_t  ret = INS_ERROR;
#if 0
    uint8_t  sum, calSum;
    uint8_t  data_l, data_h;
    short	 temp;
    uint8_t  length = 0;
    uint8_t  *p = pData;

    while( 1 )
    {
        data_l = *p++;
        data_h = *p++;
        length ++;

        if((IMU_FRAME_HEADER_LSB == data_l) && (IMU_FRAME_HEADER_MSB == data_h))
        {
            sum = sum_check(p, 20 );
            calSum = *(p + 20);

            if(sum == calSum)
            {

                memcpy((void*)&imu_info.syn_low, p - 2, sizeof(IMU_DATA_TypeDef));

                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.accelGrp[0] = temp * Accel_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.accelGrp[1] = temp * Accel_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.accelGrp[2] = temp * Accel_Scale / Sensor_Scale;

                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.gyroGrp[0] = temp * Rate_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.gyroGrp[1] = temp * Rate_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.gyroGrp[2] = temp * Rate_Scale / Sensor_Scale;

                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.roll = temp * Angle_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.pitch = temp * Angle_Scale / Sensor_Scale;
                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.azimuth = temp * Angle_Scale / Sensor_Scale;

                temp = *p++;
                temp |= (*p++) << 8;
                imuParseData.sensorTemp = temp * Temp_Scale / Sensor_Scale;

                imuParseData.counter++;

                break;
            }

        }

        if(length > dataLen)break;
    }
	#endif
#undef	Accel_Scale
#undef	Rate_Scale
#undef	Angle_Scale
#undef	Temp_Scale
#undef	Sensor_Scale
    return ret;
}
//光纤陀螺数据解析
uint8_t frame_fill_ifog(uint8_t* pData, uint16_t dataLen)
{
#define GYRO_FACTOR    		320000.0//780000.0

    //int32_t gyroscope;
    int16_t gyroTemp;
    int32_t gyroscope;
    uint8_t  sum, calSum;
    uint8_t	 temp;
    uint8_t  ret = INS_ERROR;
    uint8_t  *p = pData;

    if(*p == 0x80)
    {
        sum = xor_check(p + 1, 5 );
        calSum = *(p + 6);

        if(sum == calSum)
        {
            temp = (*(p + 2)) << 7 | *(p + 1);
            gyroscope = temp;
            temp = (*(p + 3)) << 6 | (*(p + 2)) >> 1;
            gyroscope |= temp << 8;
            temp = (*(p + 4)) << 5 | (*(p + 3)) >> 2;
            gyroscope |= temp << 16;
            temp = (*(p + 5)) << 4 | (*(p + 4)) >> 3;
            gyroscope |= temp << 24;
            //iFogParseData.gyroGrp[2] = gyroscope;
            //iFogParseData.gyroGrp[2] /= GYRO_FACTOR;
#ifdef configUse_debug
            navi_test_info.iFogData.gyroGrp[2] = iFogParseData.gyroGrp[2];
#endif
            sum = xor_check(p + 7, 2 );
            calSum = *(p + 9);

            if(sum == calSum)
            {
                temp = (*(p + 8)) << 7 | *(p + 7);
                gyroTemp = temp;
                temp = (*(p + 8)) >> 1 ;
                gyroTemp |= temp << 8;
				
                if(gyroTemp & 0x2000)
                {
                    gyroTemp = ~gyroTemp + 1;
                    gyroTemp &= 0x3fff;
                    gyroTemp = -gyroTemp;
                }

                //iFogParseData.sensorTemp = gyroTemp / 16.0;
#ifdef configUse_debug
                navi_test_info.iFogData.sensorTemp = iFogParseData.sensorTemp;
#endif
                ret = INS_EOK;
            }
        }
    }

#undef	GYRO_FACTOR
    return ret;
}

extern void syn_arm2(void);
void syn_arm2(void)
{
    //ARM1_TO_ARM2_SYN
//    gd32_pin_write(SYN_ARM_IO, PIN_HIGH);
//    gd32_pin_write(SYN_ARM_IO, PIN_LOW);
//    gd32_pin_write(SYN_ARM_IO, PIN_LOW);
//    gd32_pin_write(SYN_ARM_IO, PIN_HIGH);
}
//写数据到DRAM
void uart3sendmsg(char *txbuf, int size);
void uart4sendmsg(char *txbuf, int size);
void frame_writeDram(void)
{
    //DRam_Write(0x400, (uint16_t*)rs422_frame.fpga_cache, RS422_FRAME_LENGTH / 2);
    //syn_arm2();
	//uart4sendmsg((char*)rs422_frame.fpga_cache, RS422_FRAME_LENGTH);
}

//中海达协议
void frame_iPMV_pack_and_send(void* pnav, void *gps)
{
//	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
//    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

//    moduleStatus.header[0] = RS422_FRAME_HEADER_L;
//    moduleStatus.header[1] = RS422_FRAME_HEADER_M;
//    moduleStatus.header[2] = RS422_FRAME_HEADER_H;

//	moduleStatus.expiredStatus = 0;
//	moduleStatus.calibStatus = result->Nav_Standard_flag;
//	moduleStatus.gnssStatus = (gnss->PositioningState == 'A')?1:0;
//	moduleStatus.imuStatus = 1;
//	moduleStatus.diffStatus = 1;
//	moduleStatus.odoStatus = combineData.canInfo.counter ? 1 : 0;
//    moduleStatus.xor = xor_check(&moduleStatus.status, sizeof(moduleStatus) - 5 );
//    moduleStatus.end = 0xbf;
}

/*!
    \brief    导远协议数据打包及发送
    \param[in]  pnav：算法数据指针
                gps：gps数据指针
    \param[out] none
    \retval     none
*/
void frame_pack_and_send(void* pnav, void *gps)
{
#define	Accel_Scale 		50//memes: 12 光纤：50
#define	Rate_Scale 			300
#define	IFof_Rate_Scale 	600
#define	Angle_Scale 		360
#define	Temp_Scale			200
#define	Sensor_Scale 		32768
#define	IFof_Sensor_Scale 	2147483648
#define EXP_E    			2.718282

    uint16_t freq;
    uint8_t xor, id;
    double temp;
    long gyroTemp[3], accelTemp[3];
    long gyrozTemp;
    //DEV_StatusTypedef status;
	//_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    navoutdata_t* result = &gnavout;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)&ggpsorgdata;

    result->gyroX = StrMiddleWare.fogx;
    result->gyroY = StrMiddleWare.fogy;
    result->gyroZ = StrMiddleWare.fogz;
    result->accelX = StrMiddleWare.accelerometerx;
    result->accelY = StrMiddleWare.accelerometery;
    result->accelZ = StrMiddleWare.accelerometerz;

    rs422_frame.data_stream.header[0] = RS422_FRAME_HEADER_L;
    rs422_frame.data_stream.header[1] = RS422_FRAME_HEADER_M;
    rs422_frame.data_stream.header[2] = RS422_FRAME_HEADER_H;

    rs422_frame.data_stream.pitch = (short)(result->pitch / Angle_Scale * Sensor_Scale);
    rs422_frame.data_stream.roll = (short)(result->roll / Angle_Scale * Sensor_Scale);
    rs422_frame.data_stream.azimuth = (short)(result->azimuth / Angle_Scale * Sensor_Scale);

    //gyroTemp[0] = (short)(result->gyroX / Rate_Scale * Sensor_Scale);
    //gyroTemp[1] = (short)(result->gyroY / Rate_Scale * Sensor_Scale);
    gyroTemp[0] = (long)((r_Gyro[0]/D2R) / IFof_Rate_Scale * IFof_Sensor_Scale);
    gyroTemp[1] = (long)((r_Gyro[1]/D2R) / IFof_Rate_Scale * IFof_Sensor_Scale);
#ifdef	RS422_PROTOCOL_OLD
    gyroTemp[2] = (short)(result->gyroZ / Rate_Scale * Sensor_Scale);
#else
	//gyrozTemp   = (long)(result->gyroZ / IFof_Rate_Scale * IFof_Sensor_Scale);
	gyrozTemp   = (long)((r_Gyro[2]/D2R) / IFof_Rate_Scale * IFof_Sensor_Scale);
#endif		

    //accelTemp[0] = (short)(result->accelX / Accel_Scale * Sensor_Scale);
    //accelTemp[1] = (short)(result->accelY / Accel_Scale * Sensor_Scale);
    //accelTemp[2] = (short)(result->accelZ / Accel_Scale * Sensor_Scale);
    accelTemp[0] = (long)((Acc[0]/G) / Accel_Scale * IFof_Sensor_Scale);
    accelTemp[1] = (long)((Acc[1]/G) / Accel_Scale * IFof_Sensor_Scale);
    accelTemp[2] = (long)((Acc[2]/G) / Accel_Scale * IFof_Sensor_Scale);
    //////////////////////////////////////////////////////////
    axisInfo.index = comm_axis_read();
    axisInfo.axis[0] = axisTab[axisInfo.index].axis[0];
    axisInfo.axis[1] = axisTab[axisInfo.index].axis[1];
    axisInfo.axis[2] = axisTab[axisInfo.index].axis[2];

    id = abs(axisInfo.axis[0]);
    id -= 1;

    if(axisInfo.axis[0] > 0)
    {
        rs422_frame.data_stream.accelX = accelTemp[id];
        rs422_frame.data_stream.gyroX  = gyroTemp[id];
    }
    else
    {
        rs422_frame.data_stream.accelX = -accelTemp[id];
        rs422_frame.data_stream.gyroX  = -gyroTemp[id];
    }

    id = abs(axisInfo.axis[1]);
    id -= 1;

    if(axisInfo.axis[1] > 0)
    {
        rs422_frame.data_stream.accelY = accelTemp[id];
        rs422_frame.data_stream.gyroY  = gyroTemp[id];
    }
    else
    {
        rs422_frame.data_stream.accelY = -accelTemp[id];
        rs422_frame.data_stream.gyroY  = -gyroTemp[id];
    }

    id = abs(axisInfo.axis[2]);
    id -= 1;

    if(axisInfo.axis[2] > 0)
    {
        rs422_frame.data_stream.accelZ = accelTemp[id];
        #ifdef	RS422_PROTOCOL_OLD
		rs422_frame.data_stream.gyroZ  = gyroTemp[id];
        #else
        rs422_frame.data_stream.gyroZ  = gyrozTemp;
        #endif
    }
    else
    {
        rs422_frame.data_stream.accelZ = -accelTemp[id];
#ifdef	RS422_PROTOCOL_OLD
		rs422_frame.data_stream.gyroZ  = -gyroTemp[id];
#else
		rs422_frame.data_stream.gyroZ  = -gyrozTemp;
#endif
    }

//    if(combineData.debug)
//    {
//        rs422_frame.data_stream.latitude = (long)(gnss->Lat * 10000000);
//        rs422_frame.data_stream.longitude = (long)(gnss->Lon * 10000000);
//        rs422_frame.data_stream.altitude = (long)(gnss->Altitude * 1000);
//    }
//    else
//    {
        rs422_frame.data_stream.latitude = (long)(result->latitude * 10000000);
        rs422_frame.data_stream.longitude = (long)(result->longitude * 10000000);
        rs422_frame.data_stream.altitude = (long)(result->altitude * 1000);
//    }

    rs422_frame.data_stream.ve = (short)(result->ve / 100.0f * Sensor_Scale);
    rs422_frame.data_stream.vn = (short)(result->vn / 100.0f * Sensor_Scale);
    rs422_frame.data_stream.vu = (short)(result->vu / 100.0f * Sensor_Scale);

    rs422_frame.data_stream.status = 0;

    //rs422_frame.data_stream.Nav_Status = result->Nav_Status;
    rs422_frame.data_stream.reserved[0] = gnss->leapsec;

    if(gnss->ResolveState[0] == 0)
        rs422_frame.data_stream.status |= 0x1;
    else
        rs422_frame.data_stream.status &= ~0x1;

    if(gnss->ResolveState[1] == 0)
        rs422_frame.data_stream.status |= 0x2;
    else
        rs422_frame.data_stream.status &= ~0x2;

    if(gnss->ResolveState[2] == 0)
        rs422_frame.data_stream.status |= 0x4;
    else
        rs422_frame.data_stream.status &= ~0x4;

    switch(rs422_frame.data_stream.type)
    {
//        case locating_info_prec:
//        {
//            temp = log(gnss->LatStd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data1 = temp;
//            temp = log(gnss->LonStd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data2 = temp;
//            temp = log(gnss->AltitudeStd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data3 = temp;
//        }
//        break;

//        case speed_info_prec:
//        {
//            temp = log(gnss->vestd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data1 = temp;
//            temp = log(gnss->vnstd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data2 = temp;
//            temp = log(gnss->vustd) / log(EXP_E);
//            temp *= 100;
//            rs422_frame.data_stream.poll_frame.data3 = temp;
//        }
//        break;

        case pos_info_prec:
        {
            //temp = gnss->rollstd / 100;
            rs422_frame.data_stream.poll_frame.data1 = gins912data.ppsdelay10ns/1000;//pps延迟
            temp = log(gnss->ptchstddev) / log(EXP_E);
            temp *= 100;
            rs422_frame.data_stream.poll_frame.data2 = temp;
            temp = log(gnss->hdgstddev) / log(EXP_E);
            temp *= 100;
            rs422_frame.data_stream.poll_frame.data3 = temp;
        }
        break;

        case dev_inter_temp:
        {
#if IMU_MODULE_SELECT == IMU_MODULE_IMU460
            rs422_frame.data_stream.poll_frame.data1 = imuParseData.sensorTemp * Sensor_Scale / Temp_Scale;
#elif IMU_MODULE_SELECT == IMU_MODULE_SCHA634
            //temp = (combineData.scha634Info.temp_due + combineData.scha634Info.temp_uno) / 2;
			temp = result->temperature;
            rs422_frame.data_stream.poll_frame.data1 = temp * Sensor_Scale / Temp_Scale;
#elif IMU_MODULE_SELECT == IMU_MODULE_ADIS16465
			rs422_frame.data_stream.poll_frame.data1 = combineData.adis16465Info.temp * Sensor_Scale / Temp_Scale;
#endif

            rs422_frame.data_stream.poll_frame.data2 = 0;
            rs422_frame.data_stream.poll_frame.data3 = 0;
        }
        break;

        case gps_status:
        {
            rs422_frame.data_stream.poll_frame.data1 = gnss->PositionType[0];
            rs422_frame.data_stream.poll_frame.data2 = gnss->StarNum;
            rs422_frame.data_stream.poll_frame.data3 = gnss->PositionType[2];
        }
        break;

        case rotate_status:
        {
            rs422_frame.data_stream.poll_frame.data1 = 0;
            rs422_frame.data_stream.poll_frame.data2 = 0;	//combineData.canInfo.flag;
            rs422_frame.data_stream.poll_frame.data3 = 0;
        }
        break;

        case gnss_baseline:
            rs422_frame.data_stream.poll_frame.data1 = gnss->baseline * 1000;
            rs422_frame.data_stream.poll_frame.data2 = 0;
            rs422_frame.data_stream.poll_frame.data3 = 0;
            break;

        case gnss_rtkStatus:
            rs422_frame.data_stream.poll_frame.data1 = gnss->rtkStatus;
            rs422_frame.data_stream.poll_frame.data2 = 0;
            rs422_frame.data_stream.poll_frame.data3 = 0;
            break;

        case para_adj:
            rs422_frame.data_stream.poll_frame.data1 = 0;	//result->Nav_Standard_flag;
            rs422_frame.data_stream.poll_frame.data2 = 0;
            rs422_frame.data_stream.poll_frame.data3 = 0;
            break;

		case calib_rate:
            rs422_frame.data_stream.poll_frame.data1 = (uint16_t)0xf42c6;
            rs422_frame.data_stream.poll_frame.data2 = (uint16_t)(0xf42c6>>16);
            rs422_frame.data_stream.poll_frame.data3 = 0;
            break;
        default:
            rs422_frame.data_stream.poll_frame.data1 = 0;
            rs422_frame.data_stream.poll_frame.data2 = 0;
            rs422_frame.data_stream.poll_frame.data3 = 0;
            break;
    }

    freq = comm_read_currentFreq();

    if(xgpsTime != gnss->gpssecond)
    {
        xgpsTime = gnss->gpssecond;

        if(xgpsTrueTime == xgpsTime)
        {
            xgpsTrueTime += (1000 / freq);
        }
        else
        {
            xgpsTrueTime = xgpsTime;
        }
    }
    else
    {
        xgpsTrueTime += (1000 / freq);
    }

    //rs422_frame.data_stream.gps_time = xgpsTrueTime * 4;
    //rs422_frame.data_stream.gps_time = gnss->gpssecond;
    //rs422_frame.data_stream.gps_time = NAV_Data_Out.gpssecond * 4;、
	
	//static unsigned int ggpstimescount = 0;
	//result->gps_time = ggpstimescount++;
	
    rs422_frame.data_stream.gps_time = result->gps_time;// * 4;

    rs422_frame.data_stream.gps_week = result->gps_week;//gnss->gpsweek;

    xor = xor_check(rs422_frame.data_stream.header, sizeof(rs422_frame.data_stream) - 6);
    rs422_frame.data_stream.xor_verify1 = xor;

    xor = xor_check(rs422_frame.data_stream.header, sizeof(rs422_frame.data_stream) - 1);
    rs422_frame.data_stream.xor_verify2 = xor;

    //if(INS_EOK == gnss_isLocation())
    {
        //gd32_usart_write((uint8_t*)&rs422_frame.data_stream.header[0],sizeof(rs422_frame.data_stream));
        Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, sizeof(rs422_frame.data_stream), (uint8_t*)&rs422_frame.data_stream.header[0]);
    }
    rs422_frame.data_stream.type++;

    if(rs422_frame.data_stream.type >= 60)rs422_frame.data_stream.type = 0;

    //frame_writeDram();

#undef	Accel_Scale
#undef	Rate_Scale
#undef	IFof_Rate_Scale
#undef	Angle_Scale
#undef	Temp_Scale
#undef	Sensor_Scale
#undef	IFof_Sensor_Scale
#undef	EXP_E
}
//数据帧初始化
void frame_init(void)
{
    memset(rs422_frame.fpga_cache, 0x0, sizeof(rs422_frame.fpga_cache));
}


#endif

