#ifndef __FRAME_ANALYSIS_H__
#define __FRAME_ANALYSIS_H__

#include "gd32f4xx.h"
#include "INS_Data.h"
#include "insdef.h"

#include "algorithm.h"
#include "tlhtype.h"

#undef COMMON_EXT
#ifdef  __GOL_FRAME_ANALYSIS_C__
    #define COMMON_EXT
#else
    #define COMMON_EXT extern
#endif

//#define	RS422_PROTOCOL_OLD

#define RS422_FRAME_HEADER_L                  0xBD
#define RS422_FRAME_HEADER_M                  0xDB
#define RS422_FRAME_HEADER_H                  0x0B

#ifdef	RS422_PROTOCOL_OLD
#define	RS422_FRAME_LENGTH	63
#else
#define	RS422_FRAME_LENGTH	(100)	//65
#endif

typedef struct imu_data
{
    uint8_t syn_low;
    uint8_t syn_high;
    uint8_t accelX_l;
    uint8_t accelX_h;
    uint8_t accelY_l;
    uint8_t accelY_h;
    uint8_t accelZ_l;
    uint8_t accelZ_h;
    uint8_t gyroX_l;
    uint8_t gyroX_h;
    uint8_t gyroY_l;
    uint8_t gyroY_h;
    uint8_t gyroZ_l;
    uint8_t gyroZ_h;
    uint8_t roll_l;
    uint8_t roll_h;
    uint8_t pitch_l;
    uint8_t pitch_h;
    uint8_t	azimuth_l;
    uint8_t	azimuth_h;
    uint8_t	sensor_temp_l;
    uint8_t	sensor_temp_h;
    uint8_t crc;
} IMU_DATA_TypeDef;

//typedef enum poll_data_type
//{
//    locating_info_prec = 0,
//    speed_info_prec = 1,
//    pos_info_prec = 2,
//    dev_inter_temp = 22,
//    gps_status     = 32,
//    rotate_status = 33,
//    gnss_baseline = 34,
//    gnss_rtkStatus = 35,
//    para_adj = 36,
//    calib_rate = 37
//} POLL_DATA_TypeDef;

#pragma pack(1)

typedef  struct poll_data_F
{
//    uint16_t	data1;
//    uint16_t 	data2;
//    uint16_t	data3;
  
    int16_t data1;
    int16_t data2;
    int16_t	data3;
    //uint32_t	gps_time;
    //uint8_t		type;
} POLL_DATA_Fx, *pPOLL_DATA_F;

typedef union
{
    struct
    {
        uint8_t pos: 1;
        uint8_t spd: 1;
        uint8_t posture: 1;
        uint8_t courseAngle: 1;
        uint8_t hold: 4;
    } statusBits;
    uint8_t dev_status;
} DEV_StatusTypedef;

typedef  struct
{
    uint8_t 			header[3];	//0xbd,0xdb,0x0b
    short 				roll;		//横滚角
    short 				pitch;		//俯仰角
    short				azimuth;	//方位角
    short 				gyroX;		//陀螺x轴
    short 				gyroY;		//陀螺y轴
#ifdef	RS422_PROTOCOL_OLD
	short				gyroZ;		//陀螺z轴
#else
	long				gyroZ;		//陀螺z轴
#endif    
    short 				accelX;		//加表x轴
    short 				accelY;		//加表y轴
    short				accelZ;		//加表z轴
    long				latitude;	//纬度
    long				longitude;	//经度
    long				altitude;	//高度
    short				ve;			//东向速度
    short				vn;			//北向速度
    short				vu;			//天向速度
    uint8_t				status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向角
    //uint32_t 			Nav_Status;
    uint8_t				reserved[6];
    POLL_DATA_Fx		poll_frame;
    uint32_t			gps_time;
    uint8_t				type;
    uint8_t				xor_verify1;
    uint32_t			gps_week;
    uint8_t				xor_verify2;
} DATA_STREAM;


typedef  struct _data_streamT
{
    uint8_t 			header[3];	//0xbd,0xdb,0x0b
    short 				roll;		//横滚角
    short 				pitch;		//俯仰角
    short				azimuth;	//方位角
    short 				gyroX;		//陀螺x轴
    short 				gyroY;		//陀螺y轴
#ifdef	RS422_PROTOCOL_OLD
	short				gyroZ;		//陀螺z轴
#else
	long				gyroZ;		//陀螺z轴
#endif    
    short 				accelX;		//加表x轴
    short 				accelY;		//加表y轴
    short				accelZ;		//加表z轴
    long				latitude;	//纬度
    long				longitude;	//经度
    long				altitude;	//高度
    short				ve;			//东向速度
    short				vn;			//北向速度
    short				vu;			//天向速度
    uint8_t				status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向角
    //uint32_t 			Nav_Status;
    uint8_t				reserved[6];
    POLL_DATA_Fx		poll_frame;
    uint32_t			gps_time;
    uint8_t				type;
    uint8_t				xor_verify1;
    uint32_t			gps_week;
    uint8_t				xor_verify2;
	double				latitudeD;
	double				longitudeD;
	double				altitudeD;
    uint8_t				xor_verify3;
} data_streamT_t;


typedef union rs422_frame_define
{
    DATA_STREAM data_stream;
    uint8_t fpga_cache[RS422_FRAME_LENGTH];
} RS422_FRAME_DEFx, *pRS422_FRAME_DEF;


typedef  struct
{
    uint16_t 	counter;
    double		imuTimestamp;
    float		timestamp;
    float 		accelX;
    float 		accelY;
    float 		accelZ;
    float 		gyroX;
    float 		gyroY;
    float 		gyroZ;
    float		sensor_temp;
    uint8_t		gps_valid;
    uint16_t 	gpsWeek;
    uint32_t 	gpsSec;
    uint8_t		starNum;
    uint8_t		rtkStatus;
    float		lon;
    float		lat;
    float		alt;
    float		vn;		//bill
    float		ve;		//bill
    float		vu;
    float		heading;
	float		roll;		//bill
    float 		pitch;
	float		azimuth;	//bill
	
    uint8_t		ins_gnssflag_pos;
    uint8_t		ins_numsv;
    uint8_t		ins_gnssflag_heading;
    uint8_t		ins_self_check;
	
    uint8_t		ins_car_status;
    uint16_t	ins_gnss_week;
    uint8_t		ins_status;
	
	
    float		lon_std;
    float		lat_std;
    float		alt_std;
    float		hdgstddev;
    float		ptchstddev;
    float		hdop;
    float		trackTrue;
    volatile 	uint8_t pps_en;
    volatile 	uint8_t pps_cnt;
    uint16_t 	s_ppsDelay;
    IFOG_PARSE_DATA_TypeDef iFogData;
    CanDataTypeDef			canInfo;
} navi_test_t;

typedef  struct
{
    uint8_t 			header[3];			//0xbd,0xdb,0x0b
    uint8_t 			status;				//状态字
    uint8_t 			expiredStatus;		//主机过期状态
    uint8_t				calibStatus;		//标定状态
    uint8_t 			gnssStatus;			//gnss状态
    uint8_t 			imuStatus;			//imu状态
    uint8_t				diffStatus;			//差分状态
    uint8_t 			odoStatus;			//odo状态
    uint8_t				xor;				//3~9字节异或和
    uint8_t				end;				//结束符

} MODULE_STA_Typedef;

//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct gdwrxdata912info {
	uint16_t		datalength;       		// //1 1	
	uint16_t     selftestingcode;        // //2 //2
  
	uint16_t     fpgaversion;            // //3 //3
	uint16_t		watchversion;       //     //4 4
	uint16_t     Xgears;                   //5 5
	float   Xflwheelspeed;            //6 6
	float   Xfrwheelspeed;            //7 8
	float   Xblwheelspeed;            //8 A
	float   Xbrwheelspeed;            //9 C
	uint16_t     Xcaninfocounter;          //10 E

	//int32_t     fogx;                    //11 F
	//int32_t     fogy;                    //12 11
	//int32_t     fogz;                    //13 13
	float     fogx;                    //11 F
	float     fogy;                    //12 11
	float     fogz;                    //13 13
		
	int16_t     fogtemperaturex;         //14 15
	int16_t     fogtemperaturey;         //15 16
	int16_t     fogtemperaturez;         //16 17 
	float   accelerometerx;          //17 18
	float   accelerometery;          //18 1A
	float   accelerometerz;          //19 1C
	int16_t     accelerometertemp;       //20 1E
	int16_t     reserve1e;               //21 1F
	int16_t     reserve1f;               //22 20
	int16_t     Reserve20;               //23 21
	int16_t     gnssweek;                //24 22 GNSS周
	uint32_t     millisecondofweek;      //25 23 GNSS秒-G
					   
	uint32_t     secondofweek;            //26 25 GNSS秒
	uint32_t     ppsdelay10ns;            //27 27
	int16_t     gpsstarnumber;           //28  29
	int16_t     rtkstatus;               //29  2A
	int16_t     speedstatus;             //30  2B
	int16_t     truenorthtrack[3];       //31  2C
	float	  northvelocity;   	//32 2F
	float   eastvelocity;            //33 31
	float   upvelocity;              //34 33
	int16_t     positionstatus;      //35 35
	int16_t     directionoflat;      //36 36
	double  latitude;                //37 37
	int16_t     directionoflon;      //38 4A
	double  longitude;               //39 4B
	double  altitude;                //40 4F
	int16_t     Headingstate;        //41 53
	uint32_t     baselength;         //42 54
	float   roll;                    //43 56
	float   pitch;                   //44 58
	float   yaw;                     //45 5A
	#if 0
	float   ECEF_X;                  //46
	float	ECEF_Y;                  //47
	float	ECEF_Z;                  //48
	float   geometricprecZ;          //49
	float	positionprecZ;           //50
	#else
	int16_t     gears;                   // 5B
	int16_t     caninfocounter;          // 5C
	float   flwheelspeed;            //     5D
	float   frwheelspeed;            //     5F
	float   blwheelspeed;            //     61
	float   brwheelspeed;            //     63
	#endif
	float	timeprecisionZ;          //51   65
	float	verticalprecZ;           //52   67
	float	horizontalprecZ;         //53   69
	float	northprecisionZ;         //54   6B
	float	eastprecisionZ;          //55   6D
	float	endheightangleZ;         //56   6F
	int16_t		checksum;        //57   71
        //int16_t	checksumA0;       //58 
	int16_t		frameindex;      //59   72
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	int16_t		checksumA;               //58
	//	
} gdwrxdata912_t;  

typedef struct gdwrxdata912info_ex {
	double     r_Gyro[3];                    
	double     Acc[3];                    
}gdwrxdata912_t_ex;

typedef struct
{
	unsigned short	head1;			//帧头BB00
	unsigned short	head2;			//帧头DBBD
	unsigned short  dataLen;		//后面数据包长度，不包含帧头、本字节和校验
    gdwrxdata912_t fpgaPreDodata;			//FPGA原始数据包-预处理后数据
	gdwrxdata912_t_ex fpgaPreDodata_ex;
    unsigned int fpgaItrCount;		//FPGA中断计数
    unsigned int fpgaLoopCount;		//FPGA处理循环计数
	unsigned short Status;			//当前处理状态
	unsigned short CheckSum;		//检验
} FpgadataPreDoSend_t;

typedef union _arraytodata {
	gdwrxdata912_t	wcdata;
	uint16_t	gddata[108];
} arraytodata_t;

#pragma pack()

COMMON_EXT IMU_PARSE_DATA_TypeDef imuParseData;
COMMON_EXT IFOG_PARSE_DATA_TypeDef iFogParseData;
COMMON_EXT IMU_DATA_TypeDef imu_info;
COMMON_EXT MODULE_STA_Typedef moduleStatus;

extern	RS422_FRAME_DEFx	grs422_frame;
extern	navi_test_t			grs422_frameD;
extern	data_streamT_t		gdatastream;

//写数据到DRAM
void frame_writeDram(void);
void frame_pack_and_send(void* pnav, void *gps);
void frame_init(void);
uint8_t frame_fill_imu(uint8_t* pData, uint16_t dataLen);
uint8_t frame_fill_ifog(uint8_t* pData, uint16_t dataLen);
void frame_form(void);
void frame_iPMV_pack_and_send(void* pnav, void *gps);
uint8_t xor_check(uint8_t *buf, uint16_t len);



#endif

