/********************************** (C) COPYRIGHT *******************************
* File Name          : UART.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : CH378芯片 硬件标准异步串口连接的硬件抽象层 V1.0
*                      提供I/O接口子程序
*******************************************************************************/



/*******************************************************************************/
/* 头文件包含 */
#include "CH378_HAL.h"											/* CH378硬件定义相关头文件 */
#include "stdio.h"
#include "systick.h"
#include "CH378INC.H"
#include "bsp_fmc.h"

#define	UART_INIT_BAUDRATE				9600					/* 默认通讯波特率9600bps,建议通过硬件引脚设定直接选择更高的CH378的默认通讯波特率 */
//#define	UART_WORK_BAUDRATE			57600					/* 正式通讯波特率57600bps */
//#define	UART_WORK_BAUDRATE			115200					/* 正式通讯波特率115200bps */
//#define	UART_WORK_BAUDRATE			230400					/* 正式通讯波特率230400bps */
//#define	UART_WORK_BAUDRATE			460800					/* 正式通讯波特率460800bps */
//#define	UART_WORK_BAUDRATE			921600					/* 正式通讯波特率921600bps */
//#define	UART_WORK_BAUDRATE			1843200					/* 正式通讯波特率1843200bps */
//#define	UART_WORK_BAUDRATE			3686400					/* 正式通讯波特率3686400bps(不正常) */
//#define	UART_WORK_BAUDRATE			7812500					/* 正式通讯波特率7812500bps(不正常) */

uint8_t ch378_uart_rxBuf[UART_FIFO_BUF_SIZE];
/*******************************************************************************
* Function Name  : USART_Config
* Description    : 串口初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void USART_Config( UINT32 baudrate )
{
	switch(baudrate)
	{
		case UART_INIT_BAUDRATE:
			Uart_TxInit(UART_TXPORT_COMPLEX_9,UART_BAUDRATE_9600BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
			Uart_RxInit(UART_RXPORT_COMPLEX_9,UART_BAUDRATE_9600BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
			break;
#ifdef UART_WORK_BAUDRATE
		case UART_WORK_BAUDRATE:
			Uart_TxInit(UART_TXPORT_COMPLEX_9,UART_BAUDRATE_460800BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
			Uart_RxInit(UART_RXPORT_COMPLEX_9,UART_BAUDRATE_460800BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
			break;
#endif
		default:
			break;
	}
}

/*******************************************************************************
* Function Name  : USART2_Send_Byte
* Description    : 串口2发送一个字节数据
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void USART_Send_Byte( UINT8 dat )
{
	Uart_SendMsg(UART_TXPORT_COMPLEX_9,0,1,(UCHAR*)&dat);
}

/*******************************************************************************
* Function Name  : CH378_Port_Init
* Description    : CH378端口初始化
*                  由于使用异步串口读写时序,所以进行初始化  
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_Port_Init( void ) 
{
	USART_Config( UART_INIT_BAUDRATE );
	
//	rcu_periph_clock_enable(RCU_GPIOG);
//	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, CH378_SPI_MISO_PIN|CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
//	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_SPI_MISO_PIN|CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	// INT Pin
	rcu_periph_clock_enable(RCU_GPIOA);
	gpio_mode_set(CH378_INT_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_INT_PIN);
	
	//CH378 RST IO
	rcu_periph_clock_enable(RCU_GPIOB);
	gpio_mode_set(CH378_RST_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP,CH378_RST_IO_PIN);
	gpio_output_options_set(CH378_RST_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CH378_RST_IO_PIN);
	
	//set default baud rate
	//MISO		MOSI		SCLK		default baud rate after reset
	//	1		1			1			9600
	//	1		1			1			57600
	//	1		0			1			115200
	//	1		0			0			460800
	//	0		1			1			1000000
	//	0		1			0			1843200
	//	0		0			1			3000000
	//	0	0	0			0			921600
//	CH378_SPI_SCK_LOW( );
//	CH378_SPI_SDI_LOW( );
//	gpio_bit_set(CH378_SPI_Port,CH378_SPI_MISO_PIN);
	
	CH378_RST_Low();
	delay_us(10);
	CH378_RST_High();
	delay_ms(50);
}

#ifdef UART_WORK_BAUDRATE
/*******************************************************************************
* Function Name  : Set_MCU_BaudRate
* Description    : 设置单片机波特率
*                  将单片机切换到正式通讯波特率 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void Set_MCU_BaudRate( void )
{
	USART_Config( UART_WORK_BAUDRATE );
}
#endif

/*******************************************************************************
* Function Name  : xWriteCH378Cmd
* Description    : 向CH378写命令
* Input          : mCmd---将要写入CH378的命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Cmd( UINT8 mCmd ) 
{
	USART_Send_Byte( SER_SYNC_CODE1 );							/* 启动操作的第1个串口同步码 */
	USART_Send_Byte( SER_SYNC_CODE2 );							/* 启动操作的第2个串口同步码 */
	USART_Send_Byte( mCmd );							 		/* 输出命令码 */
//	CH378_mDelayuS( 2 );  										/* 延时2uS确保读写周期大于2uS */
}

/*******************************************************************************
* Function Name  : xWriteCH378Data
* Description    : 向CH378写数据
* Input          : mData---将要写入CH378的数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Data( UINT8 mData ) 
{
	USART_Send_Byte( mData );									/* 串口输出 */
}

/*******************************************************************************
* Function Name  : xReadCH378Data
* Description    : 从CH378读数据
* Input          : None
* Output         : None
* Return         : 返回读取的数据
*******************************************************************************/
UINT8 xReadCH378Data( void )
{
	UINT32 i;
	UINT8 rData,rxLen = 0;
	rxLen = Uart_RecvMsg(UART_RXPORT_COMPLEX_9,UART_FIFO_BUF_SIZE,&rData);
	if(rxLen > 0)
		return rData;
	else
		return 0xFF;
//	for( i = 0; i < 0xFFFFF0; i ++ ) 
//	{  
//		/* 计数防止超时 */
//		rxLen = Uart_RecvMsg(UART_RXPORT_COMPLEX_9,UART_FIFO_BUF_SIZE,ch378_uart_rxBuf);
//		if(rxLen>0)
//		{
//			rxLen = 0;
//			rData = ch378_uart_rxBuf[0];
//			return rData;
//		}
//	}
//	return( 0xff );													/* 不应该发生的情况 */
}


/*******************************************************************************
* Function Name  : Check_Cmd_OpStatus
* Description    : 检查命令执行状态
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
UINT8 Check_Cmd_OpStatus( void ) 
{
	UINT8  res;	

	xWriteCH378Cmd( CMD11_READ_VAR8 );
	xWriteCH378Data( VAR8_CMD_OP_STATUS );							/* 获取上一次命令执行状态 */
	res = xReadCH378Data( );
	xEndCH378Cmd( );
	return( res );
}

/*******************************************************************************
* Function Name  : Query378Interrupt
* Description    : 查询CH378中断(INT#低电平)
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
UINT8 Query378Interrupt( void )
{
	UINT8  dat = 0;

	/* 如果连接了CH378的中断引脚则直接查询中断引脚 */
	/* 如果未连接CH378的中断引脚则查询串口中断状态码 */
#ifdef	CH378_INT_WIRE
	if( CH378_INT_PIN_WIRE( ) )
	{
		return( FALSE );
	}
	else
	{
		xReadCH378Data( );										/* 由于部分单片机有FIFO,所以必须将返回的中断状态读走 */
		return( TRUE );
	}
#else
	if( USART2->SR & USART_FLAG_RXNE ) 
	{  
		dat = ( UINT8 )( USART2->DR & (uint16_t)0x01FF );		/* 读取丢弃 */
		return( TRUE );
	}
	else 
	{
		return( FALSE );
	}
#endif
}

UINT8 CH378GetIntStatus( void );

/*******************************************************************************
* Function Name  : mInitCH378Host
* Description    : 初始化CH378
* Input          : None
* Output         : None
* Return         : 返回操作状态
*******************************************************************************/
UINT8 mInitCH378Host( void )
{
	UINT8  res;

	/* 检测CH378连接是否正常 */
	CH378_Port_Init( );											/* 接口硬件初始化 */
//	xWriteCH378Cmd( CMD01_GET_IC_VER );							/* 测试单片机与CH378之间的通讯接口 */
//	res = xReadCH378Data( );
//	printf( "Ver: %02X\n", (UINT16)res );
	xWriteCH378Cmd( CMD11_CHECK_EXIST );						/* 测试单片机与CH378之间的通讯接口 */
	xWriteCH378Data( 0x65 );
//	delay_us(5);
	res = xReadCH378Data( );
//	printf( "CHECK_EXIST: %02X\n", (UINT16)res );
	if( res != 0x9A )	return( ERR_USB_UNKNOWN );
	/* 通讯接口不正常,可能原因有: 接口连接异常,其它设备影响(片选不唯一),串口波特率,一直在复位,晶振不工作 */
	
#ifdef	UART_WORK_BAUDRATE
	/* 设置CH378串口通信波特率 */
	xWriteCH378Cmd( CMD31_SET_BAUDRATE );						/* 设置串口通讯波特率 */
	xWriteCH378Data( (UINT8)UART_WORK_BAUDRATE );
	xWriteCH378Data( (UINT8)( UART_WORK_BAUDRATE >> 8 ) );
	xWriteCH378Data( (UINT8)( UART_WORK_BAUDRATE >> 16 ) );
	xEndCH378Cmd( );											/* 异步串口方式不需要 */

	/* 设置单片机本身串口通信波特率 */
	CH378_mDelaymS( 3 );
	Set_MCU_BaudRate( );										/* 将单片机切换到正式通讯波特率 */

	/* 通过读取命令执行状态判断该命令是否执行成功 */
	if( Check_Cmd_OpStatus( ) != CMD_RET_SUCCESS ) return( ERR_USB_UNKNOWN );
	/* 通讯波特率切换失败,建议通过硬件复位CH378后重试 */
	
#endif

	/* 设置CH378工作模式 */
	xWriteCH378Cmd( CMD11_SET_USB_MODE );						/* 设备USB工作模式 */
	xWriteCH378Data( 0x04 );									/* 操作SD卡 */
//	xWriteCH378Data( 0x07 );									/* 操作USB存储设备 */

	/* 等待模式设置完毕,对于操作SD卡大概需要10mS左右时间,对于操作USB设备大概需要35mS左右时间 */	
	CH378_mDelaymS( 50 );
	res = xReadCH378Data( );
	xEndCH378Cmd( );											/* 异步串口方式不需要 */
	if( res == CMD_RET_SUCCESS ) 
	{
		return( ERR_SUCCESS );
	}
	else 
	{
		/* 通过查询上一命令执行状态判断该命令是否执行成功 */
		if( Check_Cmd_OpStatus( ) == CMD_RET_SUCCESS ) 
		{
			return( ERR_SUCCESS );
		}
		return( ERR_USB_UNKNOWN );								/* 设置模式错误 */
	}
}


