/**\
 * Copyright (c) 2021 Bosch Sensortec GmbH. All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 **/

/******************************************************************************/
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>

#include "bsp_sys.h"
#include "systick.h"
#include "bmp2_defs.h"
//#include "bsp_i2c.h"
#include "bsp_soft_i2c_master.h"
/******************************************************************************/
/*!                Static variable definition                                 */

/*! Variable that holds the I2C device address or SPI chip selection */
static uint8_t dev_addr;
BSP_SOFT_I2C_Type hSoftI2c;

/******************************************************************************/
/*!                User interface functions                                   */

/*!
 * I2C read function map to COINES platform
 */
 

int8_t i2c_write(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pTxBuf,  uint8_t nBytes)
{
	Soft_I2C_Start(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,dev_addr);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,reg_addr);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,*pTxBuf);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	Soft_I2C_Stop(&hSoftI2c);
	return 0;
}

int8_t i2c_read(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pRxBuf,uint16_t nBytes)
{
	uint16_t i = 0;
	Soft_I2C_Start(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,dev_addr);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,reg_addr);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	Soft_I2C_Start(&hSoftI2c);
	Soft_I2C_Send_Byte(&hSoftI2c,dev_addr+1);
	Soft_I2C_Wait_Ack(&hSoftI2c);
	for(i = 0; i < nBytes-1;i++)
	{
		*pRxBuf = Soft_I2C_Read_Byte(&hSoftI2c,1);
		pRxBuf++;
	}
	*pRxBuf = Soft_I2C_Read_Byte(&hSoftI2c,0);
	Soft_I2C_Stop(&hSoftI2c);
	return 0;
}


BMP2_INTF_RET_TYPE bmp2_i2c_read(uint8_t reg_addr, uint8_t *reg_data, uint32_t length, void *intf_ptr)
{
	dev_addr = *(uint8_t*)intf_ptr;

	return i2c_read(dev_addr, reg_addr, reg_data, (uint16_t)length);
}

/*!
 * I2C write function map to COINES platform
 */
BMP2_INTF_RET_TYPE bmp2_i2c_write(uint8_t reg_addr, const uint8_t *reg_data, uint32_t length, void *intf_ptr)
{
	dev_addr = *(uint8_t*)intf_ptr;

	return i2c_write(dev_addr, reg_addr, (uint8_t *)reg_data, (uint16_t)length);
}

/*!
 * SPI read function map to COINES platform
 */
//BMP2_INTF_RET_TYPE bmp2_spi_read(uint8_t reg_addr, uint8_t *reg_data, uint32_t length, void *intf_ptr)
//{
//	dev_addr = *(uint8_t*)intf_ptr;

//	return coines_read_spi(dev_addr, reg_addr, reg_data, (uint16_t)length);
//}

/*!
 * SPI write function map to COINES platform
 */
//BMP2_INTF_RET_TYPE bmp2_spi_write(uint8_t reg_addr, const uint8_t *reg_data, uint32_t length, void *intf_ptr)
//{
//	dev_addr = *(uint8_t*)intf_ptr;
//	

//	return coines_write_spi(dev_addr, reg_addr, (uint8_t *)reg_data, (uint16_t)length);
//}

/*!
 * Delay function map to COINES platform
 */
void bmp2_delay_us(uint32_t period, void *intf_ptr)
{
	delay_us(period);
}

/*!
 *  @brief Prints the execution status of the APIs.
 */
//void bmp2_error_codes_print_result(const char api_name[], int8_t rslt)
//{
//	if (rslt != BMP2_OK)
//	{
//		printf("%s\t", api_name);

//		switch (rslt)
//		{
//			case BMP2_E_NULL_PTR:
//				printf("Error [%d] : Null pointer error.", rslt);
//				printf(
//					"It occurs when the user tries to assign value (not address) to a pointer, which has been initialized to NULL.\r\n");
//				break;
//			case BMP2_E_COM_FAIL:
//				printf("Error [%d] : Communication failure error.", rslt);
//				printf(
//					"It occurs due to read/write operation failure and also due to power failure during communication\r\n");
//				break;
//			case BMP2_E_INVALID_LEN:
//				printf("Error [%d] : Invalid length error.", rslt);
//				printf("Occurs when length of data to be written is zero\n");
//				break;
//			case BMP2_E_DEV_NOT_FOUND:
//				printf("Error [%d] : Device not found error. It occurs when the device chip id is incorrectly read\r\n",
//					   rslt);
//				break;
//			case BMP2_E_UNCOMP_TEMP_RANGE:
//				printf("Error [%d] : Uncompensated temperature data not in valid range error.", rslt);
//				break;
//			case BMP2_E_UNCOMP_PRESS_RANGE:
//				printf("Error [%d] : Uncompensated pressure data not in valid range error.", rslt);
//				break;
//			case BMP2_E_UNCOMP_TEMP_AND_PRESS_RANGE:
//				printf(
//					"Error [%d] : Uncompensated pressure data and uncompensated temperature data are not in valid range error.",
//					rslt);
//				break;
//			default:
//				printf("Error [%d] : Unknown error code\r\n", rslt);
//				break;
//		}
//	}
//}

/*!
 *  @brief Function to select the interface between SPI and I2C.
 */
int8_t bmp2_interface_selection(struct bmp2_dev *dev, uint8_t intf)
{
	int8_t rslt = BMP2_OK;

	if (dev != NULL)
	{
		/* Bus configuration : I2C */
		if (intf == BMP2_I2C_INTF)
		{
			dev_addr = BMP2_I2C_ADDR_PRIM<<1;
			dev->read = bmp2_i2c_read;
			dev->write = bmp2_i2c_write;
			dev->intf = BMP2_I2C_INTF;
//			bsp_i2c_init();
			hSoftI2c.scl.gpiox = GPIOH;
			hSoftI2c.scl.pin = GPIO_PIN_4;
			hSoftI2c.sda.gpiox = GPIOH;
			hSoftI2c.sda.pin = GPIO_PIN_5;
			Soft_I2C_Master_Init(&hSoftI2c);
		}
		/* Bus configuration : SPI */
//		else if (intf == BMP2_SPI_INTF)
//		{

//			dev_addr = COINES_SHUTTLE_PIN_7;
//			dev->read = bmp2_spi_read;
//			dev->write = bmp2_spi_write;
//			dev->intf = BMP2_SPI_INTF;

//			coines_config_spi_bus(COINES_SPI_BUS_0, COINES_SPI_SPEED_7_5_MHZ, COINES_SPI_MODE0);
//		}

		/* Holds the I2C device addr or SPI chip selection */
		dev->intf_ptr = &dev_addr;

		/* Configure delay in microseconds */
		dev->delay_us = bmp2_delay_us;
		delay_ms(100);
	}
	else
	{
		rslt = BMP2_E_NULL_PTR;
	}
	return rslt;
}


