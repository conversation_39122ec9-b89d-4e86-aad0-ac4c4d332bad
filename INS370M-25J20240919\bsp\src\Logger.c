#include "Logger.h"

char g_charBuf[LOG_BUF_SIZE];

/****************************************************************************
* 函数名	:	generateCSVLogFileName
* 参数		:	char* fileName, 		文件名指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	根据内核文件名合成成一个文件名
****************************************************************************/
int generateCSVLogFileName(char* fileName)
{
	if(fileName == NULL)
		return 1;
	
	// 生成基于时间的文件名
	sprintf(fileName, "LOG_%08X.CSV", (unsigned int)0x12345678);
	
	return 0;
}

/****************************************************************************
* 函数名	:	writeCSVLog
* 参数		:	unsigned char* filename,	文件名指针
* 参数		:	LogBufTypeDef* pLog		需要写入数据结构体指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	将数据写入到文件中
****************************************************************************/
int writeCSVLog(unsigned char* filename, LogBufTypeDef* pLog)
{
	UINT8 s;
	UINT16 RealCount;
	
	if(filename == NULL || pLog == NULL || pLog->pbuf == NULL)
		return 1;
	
	// 检查磁盘连接
	s = CH378DiskConnect();
	if(s != USB_INT_SUCCESS)
		return 1;
	
	// 初始化磁盘
	s = CH378DiskReady();
	if(s != USB_INT_SUCCESS)
		return 1;
	
	// 设置文件名
	CH378SetFileName(filename);
	
	// 打开文件
	s = CH378SendCmdWaitInt(CMD0H_FILE_OPEN);
	if(s == ERR_MISS_FILE) {
		// 文件不存在，创建新文件
		s = CH378SendCmdWaitInt(CMD0H_FILE_CREATE);
		if(s != USB_INT_SUCCESS)
			return 1;
	} else if(s != USB_INT_SUCCESS) {
		return 1;
	}
	
	// 移动到文件末尾
	CH378WriteVar32(0x44, 0xFFFFFFFF);
	s = CH378SendCmdWaitInt(CMD4H_BYTE_LOCATE);
	if(s != USB_INT_SUCCESS) {
		CH378SendCmdDatWaitInt(CMD1H_FILE_CLOSE, 0);
		return 1;
	}
	
	// 写入数据到缓冲区
	CH378WriteOfsBlock((PUINT8)pLog->pbuf, 0, pLog->nSize);
	
	// 写入文件
	CH378WriteVar32(0x44, pLog->nSize);
	s = CH378SendCmdWaitInt(CMD2H_BYTE_WRITE);
	if(s != USB_INT_SUCCESS) {
		CH378SendCmdDatWaitInt(CMD1H_FILE_CLOSE, 0);
		return 1;
	}
	
	// 关闭文件
	s = CH378SendCmdDatWaitInt(CMD1H_FILE_CLOSE, 1);
	if(s != USB_INT_SUCCESS)
		return 1;
	
	return 0;
}

/****************************************************************************
* 函数名	:	synthesisLogBuf
* 参数		:	uint8_t* pBuf, 			接收到数据的指针
* 参数		:	uint32_t nSize, 		接收到数据的长度
* 参数		:	uint16_t type, 			需要发送的数据类型
* 参数		:	LogBufTypeDef* pLog		需要发送的数据结构体指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	将接收到数据转换成结构体, 该结构体里保存数据的字符串形式和发送缓冲区大小
****************************************************************************/
int synthesisLogBuf(uint8_t* pBuf, uint32_t nSize, uint16_t type, LogBufTypeDef* pLog)
{
	if(pBuf == NULL || pLog == NULL)
		return 1;
	
	// 清空缓冲区
	memset(g_charBuf, 0, LOG_BUF_SIZE);
	
	// 根据类型格式化数据
	switch(type) {
		case Log_Type_0:
			sprintf(g_charBuf, "Type0,");
			break;
		case Log_Type_1:
			sprintf(g_charBuf, "Type1,");
			break;
		default:
			sprintf(g_charBuf, "Unknown,");
			break;
	}
	
	// 添加原始数据的十六进制表示
	char temp[8];
	for(uint32_t i = 0; i < nSize && i < 32; i++) {
		sprintf(temp, "%02X,", pBuf[i]);
		strcat(g_charBuf, temp);
	}
	
	// 添加换行符
	strcat(g_charBuf, "\r\n");
	
	pLog->pbuf = g_charBuf;
	pLog->nSize = strlen(g_charBuf);
	
	return 0;
}

int parseFPGABuff(float* vAngle, float* vGyro, float* vAcc, float* lat, float* lon,float* height, \
					float* speed_nor,float* speed_est,float* speed_earth, uint8_t* state, float* time, uint32_t* week,\
						float*LonStd, float* LatStd, float* AltitudeStd, uint8_t* StarNum, \
					float* vn_std,float* ve_std, float* vd_std,float* rollstd,float* pitchstd,float* yawstd,float* temper,\
						uint16_t* wheelState,uint8_t* GPSSpeedState,uint16_t* ResolveState)
{
	// 解析FPGA数据的实现
	// 这里提供一个基本的实现框架
	if(vAngle) *vAngle = 0.0f;
	if(vGyro) *vGyro = 0.0f;
	if(vAcc) *vAcc = 0.0f;
	if(lat) *lat = 0.0f;
	if(lon) *lon = 0.0f;
	if(height) *height = 0.0f;
	if(speed_nor) *speed_nor = 0.0f;
	if(speed_est) *speed_est = 0.0f;
	if(speed_earth) *speed_earth = 0.0f;
	if(state) *state = 0;
	if(time) *time = 0.0f;
	if(week) *week = 0;
	if(LonStd) *LonStd = 0.0f;
	if(LatStd) *LatStd = 0.0f;
	if(AltitudeStd) *AltitudeStd = 0.0f;
	if(StarNum) *StarNum = 0;
	if(vn_std) *vn_std = 0.0f;
	if(ve_std) *ve_std = 0.0f;
	if(vd_std) *vd_std = 0.0f;
	if(rollstd) *rollstd = 0.0f;
	if(pitchstd) *pitchstd = 0.0f;
	if(yawstd) *yawstd = 0.0f;
	if(temper) *temper = 0.0f;
	if(wheelState) *wheelState = 0;
	if(GPSSpeedState) *GPSSpeedState = 0;
	if(ResolveState) *ResolveState = 0;
	
	return 0;
}

int writeCSVFileHead(void)
{
	LogBufTypeDef logBuf;
	char header[] = "Time,Angle,Gyro,Acc,Lat,Lon,Height,Speed\r\n";
	
	logBuf.pbuf = header;
	logBuf.nSize = strlen(header);
	
	return writeCSVLog((unsigned char*)"HEADER.CSV", &logBuf);
}
