#ifndef ___bsp_SOFT_I2C_h___
#define ___bsp_SOFT_I2C_h___

#include "gd32f4xx.h"
#include "bsp_sys.h"
#include "soft_i2c.h"


void Soft_I2C_SDA_Output(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_SDA_Input(BSP_SOFT_I2C_Type* soft_i2c);



void Soft_I2C_Master_Init(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_Start(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_Stop(BSP_SOFT_I2C_Type* soft_i2c);
u8 Soft_I2C_Wait_Ack(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_Ack(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_NAck(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_Send_Byte(BSP_SOFT_I2C_Type* soft_i2c,u8 txd);
u8 Soft_I2C_Read_Byte(BSP_SOFT_I2C_Type* soft_i2c,unsigned char ack);
//void delay_init(void);
//void delay_ms(u16 nms);
void delay_us(u32 nus);

#endif // !___bsp_SOFT_I2C_h___
