Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to bsp_fmc.o(i.DRam_Read) for DRam_Read
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to logger.o(i.synthesisLogBuf) for synthesisLogBuf
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to logger.o(i.writeCSVLog) for writeCSVLog
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.bss) for hINSFPGAData
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for g_LEDIndicatorState
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_init.o(.data) for g_usb_ready
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_init.o(.bss) for g_logFileName
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_periodic_sec_cnt
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to ins_init.o(.bss) for g_logFileName
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_it.o(.data) for SDCnt
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to bsp_tim.o(.data) for time_base_periodic_cnt
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to ins_init.o(.data) for g_CAN_Timeout_Start_flag
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to ins_data.o(.data) for g_LEDIndicatorState
    gd32f4xx_it.o(i.uart4sendmsg) refers to main.o(i.UartIrqSendMsg) for UartIrqSendMsg
    main.o(i.SDUartIrqInit) refers to bsp_uart.o(i.bsp_systick_init) for bsp_systick_init
    main.o(i.UartIrqInit) refers to bsp_uart.o(i.bsp_systick_init01) for bsp_systick_init01
    main.o(i.UartIrqSendMsg) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.UartIrqSendMsg) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.main) refers to navi.o(i.SysInit) for SysInit
    main.o(i.main) refers to datado.o(i.DeviceInit) for DeviceInit
    main.o(i.main) refers to fpgad.o(i.get_fpgadata) for get_fpgadata
    main.o(i.main) refers to instestingentry.o(i.AlgorithmDo) for AlgorithmDo
    main.o(i.main) refers to ins_output.o(i.INS912_Output) for INS912_Output
    main.o(i.main) refers to datado.o(i.loopDoOther) for loopDoOther
    main.o(i.main) refers to ins_init.o(.data) for fpga_syn
    main.o(i.main) refers to ins_data.o(.bss) for gnavout
    systick.o(i.delay_init) refers to gd32f4xx_misc.o(i.systick_clksource_set) for systick_clksource_set
    systick.o(i.delay_ms) refers to systick.o(i.delay_ms_impl) for delay_ms_impl
    systick.o(i.delay_xms) refers to systick.o(i.delay_ms_impl) for delay_ms_impl
    ins_data.o(i.caninfupdate) refers to fpgad.o(.bss) for gcanInfo
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2time) for gpst2time
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.bdt2gpst) for bdt2gpst
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2utc) for gpst2utc
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.bdt2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.bdt2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.bdt2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.bdt2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.bdt2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.bdt2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.bdt2time) refers to time_unify.o(.data) for bdt0
    time_unify.o(i.epoch2time) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.epoch2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.epoch2time) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.epoch2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.epoch2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.epoch2time) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.gpst2bdt) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gpst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gpst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gpst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gpst2time) refers to time_unify.o(.data) for gpst0
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.gpst2utc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.gpst2utc) refers to time_unify.o(.data) for leaps
    time_unify.o(i.gst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gst2time) refers to time_unify.o(.data) for gst0
    time_unify.o(i.sow2Date) refers to time_unify.o(i.gst2time) for gst2time
    time_unify.o(i.sow2Date) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.sow2Date) refers to printfa.o(i.__0sprintf) for __2sprintf
    time_unify.o(i.str2time) refers to scanf_fp.o(.text) for _scanf_real
    time_unify.o(i.str2time) refers to strlen.o(.text) for strlen
    time_unify.o(i.str2time) refers to __0sscanf.o(.text) for __0sscanf
    time_unify.o(i.str2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.str2time) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.str2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2bdt) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2bdt) refers to time_unify.o(.data) for bdt0
    time_unify.o(i.time2epoch) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2epoch) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2epoch) refers to time_unify.o(.data) for mday
    time_unify.o(i.time2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gpst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gpst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gpst) refers to time_unify.o(.data) for gpst0
    time_unify.o(i.time2gst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gst) refers to time_unify.o(.data) for gst0
    time_unify.o(i.time2sec) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2sec) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.time2sec) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2sec) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2str) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2str) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    time_unify.o(i.time2str) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.time2str) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.time2str) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.time2str) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2str) refers to printfa.o(i.__0sprintf) for __2sprintf
    time_unify.o(i.timeadd) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.timeadd) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.timeadd) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.timediff) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.timediff) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.utc2gmst) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.time2sec) for time2sec
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gmst) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.utc2gmst) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.utc2gmst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.utc2gmst) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    time_unify.o(i.utc2gmst) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gpst) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gpst) refers to time_unify.o(.data) for leaps
    fpgad.o(i.fpgadata_syn_count_do) refers to strcpy.o(.text) for strcpy
    fpgad.o(i.fpgadata_syn_count_do) refers to ins_init.o(.data) for fpga_loop_count
    fpgad.o(i.fpgadata_syn_count_do) refers to fpgad.o(.data) for fpga_syn_btw
    fpgad.o(i.fpgadata_syn_count_do) refers to fpgad.o(.bss) for infoArr
    fpgad.o(i.fpgadata_syn_count_do) refers to datado.o(.data) for NaviCompute_do_count
    fpgad.o(i.get_fpgadata) refers to fpgad.o(i.fpgadata_syn_count_do) for fpgadata_syn_count_do
    fpgad.o(i.get_fpgadata) refers to fpgad.o(i.get_fpgadata_before) for get_fpgadata_before
    fpgad.o(i.get_fpgadata) refers to fpgad.o(i.get_fpgadata_do) for get_fpgadata_do
    fpgad.o(i.get_fpgadata) refers to fpgad.o(i.get_fpgadata_after_otherDataDo) for get_fpgadata_after_otherDataDo
    fpgad.o(i.get_fpgadata_after_otherDataDo) refers to dfltui.o(.text) for __aeabi_ui2d
    fpgad.o(i.get_fpgadata_after_otherDataDo) refers to dmul.o(.text) for __aeabi_dmul
    fpgad.o(i.get_fpgadata_after_otherDataDo) refers to dfixui.o(.text) for __aeabi_d2uiz
    fpgad.o(i.get_fpgadata_after_otherDataDo) refers to fpgad.o(.bss) for gpagedata
    fpgad.o(i.get_fpgadata_do) refers to fpgad.o(i.check_exmc_initialized) for check_exmc_initialized
    fpgad.o(i.get_fpgadata_do) refers to printfa.o(i.__0printf) for __2printf
    fpgad.o(i.get_fpgadata_do) refers to memcpya.o(.text) for __aeabi_memcpy
    fpgad.o(i.get_fpgadata_do) refers to fpgad.o(.data) for gfpgasenddatalen
    fpgad.o(i.get_fpgadata_do) refers to fpgad.o(.bss) for gfpgadata
    fpgad.o(i.get_fpgadata_setADLX355MemsData) refers to memcpya.o(.text) for __aeabi_memcpy
    fpgad.o(i.get_fpgadata_setADLX355MemsData) refers to adxl355.o(.bss) for adlxdata
    fpgad.o(i.get_fpgadata_setADLX355MemsData) refers to fpgad.o(.bss) for gfpgadata
    fpgad.o(i.get_fpgadata_setADLX355MemsData) refers to fpgad.o(.data) for gfpgasenddatalen
    fpgad.o(i.test_gyc_FPGATo422) refers to memcpya.o(.text) for __aeabi_memcpy
    fpgad.o(i.test_gyc_FPGATo422) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    fpgad.o(i.test_gyc_FPGATo422) refers to fpgad.o(.bss) for gpagedata
    fpgad.o(i.test_gyc_FPGATo422) refers to instestingentry.o(.bss) for gins912data_cash
    clock.o(i.ins_tick_get) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_increase) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_set) refers to clock.o(.data) for ins_tick
    clock.o(i.ins_tick_timeout) refers to clock.o(i.ins_tick_get) for ins_tick_get
    data_shift.o(i.ProcessIMUDataUserAxis) refers to computerframeparse.o(.bss) for hSetting
    gdwatch.o(i.initializationdriversettings) refers to memseta.o(.text) for __aeabi_memclr
    gdwatch.o(i.initializationdriversettings) refers to strcpy.o(.text) for strcpy
    gdwatch.o(i.initializationdriversettings) refers to gdwatch.o(.bss) for gdriversettings
    gdwatch.o(i.initializationdriversettings) refers to frame_analysis.o(.bss) for ggpsorgdata
    gdwatch.o(i.mcusendtopcdriversdata) refers to memcpya.o(.text) for __aeabi_memcpy
    gdwatch.o(i.mcusendtopcdriversdata) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    gdwatch.o(i.mcusendtopcdriversdata) refers to gdwatch.o(.bss) for gdriverdatalist
    ins_output.o(i.INS912_Output) refers to d2f.o(.text) for __aeabi_d2f
    ins_output.o(i.INS912_Output) refers to ins_output.o(i.output_normal_do) for output_normal_do
    ins_output.o(i.INS912_Output) refers to ins_output.o(i.output_fpgatxt_do) for output_fpgatxt_do
    ins_output.o(i.INS912_Output) refers to ins_output.o(i.output_gdw_do) for output_gdw_do
    ins_output.o(i.INS912_Output) refers to ins_output.o(i.output_fpga_void) for output_fpga_void
    ins_output.o(i.INS912_Output) refers to ins_init.o(.bss) for stSetPara
    ins_output.o(i.INS912_Output) refers to ins_output.o(.data) for tCnt
    ins_output.o(i.INS912_Output) refers to ins_data.o(.bss) for gnavout
    ins_output.o(i.INS912_Output) refers to gdwatch.o(.bss) for galgrithomresultTx
    ins_output.o(i.INS912_Output) refers to main.o(.data) for g_StartUpdateFirm
    ins_output.o(i.output_fpga_void) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_output.o(i.output_fpga_void) refers to ins_data.o(i.caninfupdate) for caninfupdate
    ins_output.o(i.output_fpga_void) refers to gdwatch.o(i.mcusendtopcdriversdata) for mcusendtopcdriversdata
    ins_output.o(i.output_fpga_void) refers to gdwatch.o(.bss) for galgrithomresultTx
    ins_output.o(i.output_fpga_void) refers to fpgad.o(.bss) for gpagedata
    ins_output.o(i.output_fpga_void) refers to gdwatch.o(.data) for gdriverspacket
    ins_output.o(i.output_fpgatxt_do) refers to printfa.o(i.__0sprintf) for __2sprintf
    ins_output.o(i.output_fpgatxt_do) refers to strlen.o(.text) for strlen
    ins_output.o(i.output_fpgatxt_do) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_output.o(i.output_fpgatxt_do) refers to memseta.o(.text) for __aeabi_memclr
    ins_output.o(i.output_fpgatxt_do) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_output.o(i.output_fpgatxt_do) refers to strcat.o(.text) for strcat
    ins_output.o(i.output_fpgatxt_do) refers to fpgad.o(.data) for gfpgasenddatalen
    ins_output.o(i.output_fpgatxt_do) refers to ins_output.o(.bss) for fpgatesttxt
    ins_output.o(i.output_fpgatxt_do) refers to fpgad.o(.bss) for gfpgadata
    ins_output.o(i.output_fpgatxt_do) refers to gdwatch.o(.bss) for galgrithomresultTx
    ins_output.o(i.output_gdw_do) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_output.o(i.output_gdw_do) refers to gdwatch.o(i.mcusendtopcdriversdata) for mcusendtopcdriversdata
    ins_output.o(i.output_gdw_do) refers to fpgad.o(.bss) for gpagedata
    ins_output.o(i.output_gdw_do) refers to gdwatch.o(.data) for gdriverspacket
    ins_output.o(i.output_gdw_do) refers to gdwatch.o(.bss) for galgrithomresultTx
    ins_output.o(i.output_normal_do) refers to computerframeparse.o(i.protocol_send) for protocol_send
    ins_output.o(i.output_normal_do) refers to ins_init.o(.data) for SetSdOperateType
    ins_init.o(i.Drv_FlashErase) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    ins_init.o(i.Drv_FlashRead) refers to bsp_fmc.o(i.fmc_read_8bit_data) for fmc_read_8bit_data
    ins_init.o(i.Drv_FlashWrite) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    ins_init.o(i.Exti_Init) refers to bsp_exti.o(i.bsp_exti_init) for bsp_exti_init
    ins_init.o(i.FlashTest) refers to ins_init.o(i.Drv_FlashErase) for Drv_FlashErase
    ins_init.o(i.FlashTest) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.FlashTest) refers to ins_init.o(i.Drv_FlashWrite) for Drv_FlashWrite
    ins_init.o(i.FlashTest) refers to ins_init.o(i.Drv_FlashRead) for Drv_FlashRead
    ins_init.o(i.FlashTest) refers to ins_init.o(.bss) for ReadTestData
    ins_init.o(i.FlashTest) refers to ins_init.o(.data) for WriteTestData
    ins_init.o(i.GetChipID) refers to computerframeparse.o(.bss) for hSetting
    ins_init.o(i.GetChipID) refers to computerframeparse.o(.data) for hDefaultSetting
    ins_init.o(i.INS_Init) refers to gdwatch.o(i.initializationdriversettings) for initializationdriversettings
    ins_init.o(i.INS_Init) refers to bsp_gpio.o(i.bsp_gpio_init) for bsp_gpio_init
    ins_init.o(i.INS_Init) refers to bsp_tim.o(i.bsp_tim_init) for bsp_tim_init
    ins_init.o(i.INS_Init) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    ins_init.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ins_init.o(i.INS_Init) refers to bsp_fmc.o(i.exmc_asynchronous_sram_init) for exmc_asynchronous_sram_init
    ins_init.o(i.INS_Init) refers to ins_init.o(i.Exti_Init) for Exti_Init
    ins_init.o(i.INS_Init) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.INS_Init) refers to ins_init.o(i.ReadParaFromFlash) for ReadParaFromFlash
    ins_init.o(i.INS_Init) refers to ins_init.o(i.gd_eval_com_init_basic) for gd_eval_com_init_basic
    ins_init.o(i.INS_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    ins_init.o(i.INS_Init) refers to ins_init.o(i.gd_eval_com_init) for gd_eval_com_init
    ins_init.o(i.INS_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    ins_init.o(i.INS_Init) refers to ins_init.o(i.gd_eval_com_init6) for gd_eval_com_init6
    ins_init.o(i.INS_Init) refers to ins_data.o(.bss) for hINSData
    ins_init.o(i.INS_Init) refers to gdwatch.o(.bss) for gdriverdatalist
    ins_init.o(i.INS_Init) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.INS_Init) refers to datado.o(.data) for gprotocol_send_baudrate6
    ins_init.o(i.INS_Init) refers to ins_data.o(.data) for g_LEDIndicatorState
    ins_init.o(i.InitParaToAlgorithm) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    ins_init.o(i.InitParaToAlgorithm) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    ins_init.o(i.InitParaToAlgorithm) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.InitParaToAlgorithm) refers to computerframeparse.o(.bss) for hSetting
    ins_init.o(i.LEDIndicator) refers to ins_init.o(.data) for call_count
    ins_init.o(i.ParaUpdateHandle) refers to ins_init.o(i.Drv_FlashErase) for Drv_FlashErase
    ins_init.o(i.ParaUpdateHandle) refers to ins_init.o(i.Drv_FlashWrite) for Drv_FlashWrite
    ins_init.o(i.ParaUpdateHandle) refers to ins_init.o(.data) for uiLastBaoInDex
    ins_init.o(i.ReadPara) refers to ins_init.o(i.ReadPara_0) for ReadPara_0
    ins_init.o(i.ReadPara) refers to ins_init.o(i.ReadPara_1) for ReadPara_1
    ins_init.o(i.ReadPara) refers to ins_init.o(i.ReadPara_2) for ReadPara_2
    ins_init.o(i.ReadPara) refers to ins_init.o(i.ReadPara_3) for ReadPara_3
    ins_init.o(i.ReadPara) refers to ins_init.o(i.ReadPara_4) for ReadPara_4
    ins_init.o(i.ReadPara0_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara0_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara0_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara1_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara1_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara1_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara2_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara2_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara2_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara3_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara3_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara3_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara4_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara4_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara4_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadParaFromFlash) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadParaFromFlash) refers to ins_init.o(i.Drv_FlashRead) for Drv_FlashRead
    ins_init.o(i.ReadParaFromFlash) refers to memcpya.o(.text) for __aeabi_memcpy4
    ins_init.o(i.ReadParaFromFlash) refers to ins_init.o(i.InitParaToAlgorithm) for InitParaToAlgorithm
    ins_init.o(i.ReadParaFromFlash) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.ReadParaFromFlash) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.ReadPara_0) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara_0) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara_0) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara_0) refers to ins_init.o(i.ReadPara0_SetHead) for ReadPara0_SetHead
    ins_init.o(i.ReadPara_0) refers to ins_init.o(i.ReadPara0_SetEnd) for ReadPara0_SetEnd
    ins_init.o(i.ReadPara_0) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.ReadPara_0) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.ReadPara_1) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara_1) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara_1) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara_1) refers to ins_init.o(i.ReadPara1_SetHead) for ReadPara1_SetHead
    ins_init.o(i.ReadPara_1) refers to ins_init.o(i.ReadPara1_SetEnd) for ReadPara1_SetEnd
    ins_init.o(i.ReadPara_1) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.ReadPara_1) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.ReadPara_2) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara_2) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara_2) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara_2) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.ReadPara_2) refers to ins_init.o(i.ReadPara2_SetHead) for ReadPara2_SetHead
    ins_init.o(i.ReadPara_2) refers to ins_init.o(i.ReadPara2_SetEnd) for ReadPara2_SetEnd
    ins_init.o(i.ReadPara_2) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.ReadPara_2) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.ReadPara_3) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara_3) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara_3) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara_3) refers to ins_init.o(i.ReadPara3_SetHead) for ReadPara3_SetHead
    ins_init.o(i.ReadPara_3) refers to ins_init.o(i.ReadPara3_SetEnd) for ReadPara3_SetEnd
    ins_init.o(i.ReadPara_3) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.ReadPara_3) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.ReadPara_4) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.ReadPara_4) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.ReadPara_4) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.ReadPara_4) refers to ins_init.o(i.ReadPara4_SetHead) for ReadPara4_SetHead
    ins_init.o(i.ReadPara_4) refers to ins_init.o(i.ReadPara4_SetEnd) for ReadPara4_SetEnd
    ins_init.o(i.ReadPara_4) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.ReadPara_4) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.RestoreFactory) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.RestoreFactory) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.RestoreFactory) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.RestoreFactory) refers to ins_init.o(i.Drv_FlashErase) for Drv_FlashErase
    ins_init.o(i.RestoreFactory) refers to ins_init.o(i.Drv_FlashWrite) for Drv_FlashWrite
    ins_init.o(i.RestoreFactory) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.RestoreFactory) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.RestoreFactory) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.RestoreFactory) refers to systick.o(i.delay_ms) for delay_ms
    ins_init.o(i.RestoreFactory) refers to ins_init.o(i.Drv_SystemReset) for Drv_SystemReset
    ins_init.o(i.RestoreFactory) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SaveParaToFlash) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SaveParaToFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SaveParaToFlash) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SaveParaToFlash) refers to ins_init.o(i.Drv_FlashErase) for Drv_FlashErase
    ins_init.o(i.SaveParaToFlash) refers to ins_init.o(i.Drv_FlashWrite) for Drv_FlashWrite
    ins_init.o(i.SaveParaToFlash) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SaveParaToFlash) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SaveParaToFlash) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SaveParaToFlash) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SendPara_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SendPara_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SendPara_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaAll) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaAll) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaAll) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaAll) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaAll) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaAll) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaAll) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaAngle) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaAngle) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaAngle) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaAngle) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaAngle) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaAngle) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaAngle) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaBaud) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaBaud) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaBaud) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.SetParaBaud) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaBaud) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaBaud) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaBaud) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaBaud) refers to systick.o(i.delay_ms) for delay_ms
    ins_init.o(i.SetParaBaud) refers to main.o(i.UartIrqInit) for UartIrqInit
    ins_init.o(i.SetParaBaud) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaCalibration) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaCalibration) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaCalibration) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaCalibration) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaCalibration) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaCalibration) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaCalibration) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaCoord) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaCoord) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaCoord) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaCoord) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.SetParaCoord) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaCoord) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaCoord) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaCoord) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaDataOutType) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaDataOutType) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaDataOutType) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaDataOutType) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaDataOutType) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaDataOutType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaDataOutType) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaDataOutType) refers to ins_init.o(.data) for g_DataOutTypeFlag
    ins_init.o(i.SetParaDebugMode) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaDebugMode) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaDebugMode) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaDebugMode) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaDebugMode) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaDebugMode) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaDebugMode) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaDeviation) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaDeviation) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaDeviation) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaDeviation) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaDeviation) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaDeviation) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaDeviation) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaFactorAcc) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaFactorAcc) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaFactorAcc) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaFactorAcc) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaFactorAcc) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaFactorAcc) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaFactorAcc) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaFactorGyro) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaFactorGyro) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaFactorGyro) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaFactorGyro) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaFactorGyro) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaFactorGyro) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaFactorGyro) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaFilter) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaFilter) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaFilter) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaFilter) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaFilter) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaFilter) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaFilter) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaFrequency) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaFrequency) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaFrequency) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaFrequency) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaFrequency) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaFrequency) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaFrequency) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaFrequency) refers to computerframeparse.o(.bss) for hSetting
    ins_init.o(i.SetParaGnss) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaGnss) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaGnss) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaGnss) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaGnss) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaGnss) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaGnss) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaGnssInitValue) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaGnssInitValue) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaGnssInitValue) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaGnssInitValue) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaGnssInitValue) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaGnssInitValue) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaGnssInitValue) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaGpsType) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaGpsType) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaGpsType) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaGpsType) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaGpsType) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaGpsType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaGpsType) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaGyroType) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaGyroType) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaGyroType) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaGyroType) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaGyroType) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaGyroType) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaGyroType) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaKalmanQ) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaKalmanQ) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaKalmanQ) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaKalmanQ) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaKalmanQ) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaKalmanQ) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaKalmanQ) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaKalmanR) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaKalmanR) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaKalmanR) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaKalmanR) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaKalmanR) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaKalmanR) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaKalmanR) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaSdHandle) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaSdHandle) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaSdHandle) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaSdHandle) refers to ins_init.o(i.SdFileOperateTypeSet) for SdFileOperateTypeSet
    ins_init.o(i.SetParaSdHandle) refers to ins_init.o(i.UpdateStop_SetHead) for UpdateStop_SetHead
    ins_init.o(i.SetParaSdHandle) refers to ins_init.o(i.UpdateStop_SetEnd) for UpdateStop_SetEnd
    ins_init.o(i.SetParaSdHandle) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaSdHandle) refers to ins_init.o(.data) for SetSdFlieType
    ins_init.o(i.SetParaTime) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaTime) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaTime) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaTime) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaTime) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaTime) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaTime) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.SetParaUpdateEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaUpdateEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaUpdateEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaUpdateEnd) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.SetParaUpdateEnd) refers to ins_init.o(i.UpdateEnd_SetHead) for UpdateEnd_SetHead
    ins_init.o(i.SetParaUpdateEnd) refers to ins_init.o(i.UpdateEnd_SetEnd) for UpdateEnd_SetEnd
    ins_init.o(i.SetParaUpdateEnd) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaUpdateEnd) refers to ins_init.o(i.Drv_SystemReset) for Drv_SystemReset
    ins_init.o(i.SetParaUpdateEnd) refers to main.o(.data) for g_StartUpdateFirm
    ins_init.o(i.SetParaUpdateEnd) refers to ins_init.o(.data) for g_ucSystemResetFlag
    ins_init.o(i.SetParaUpdateSend) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaUpdateSend) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaUpdateSend) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaUpdateSend) refers to ins_init.o(i.ParaUpdateHandle) for ParaUpdateHandle
    ins_init.o(i.SetParaUpdateSend) refers to ins_init.o(i.UpdateSend_SetHead) for UpdateSend_SetHead
    ins_init.o(i.SetParaUpdateSend) refers to ins_init.o(i.UpdateSend_SetEnd) for UpdateSend_SetEnd
    ins_init.o(i.SetParaUpdateSend) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaUpdateSend) refers to ins_init.o(.data) for g_UpdateBackFlag
    ins_init.o(i.SetParaUpdateStart) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaUpdateStart) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaUpdateStart) refers to printfa.o(i.__0printf) for __2printf
    ins_init.o(i.SetParaUpdateStart) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaUpdateStart) refers to systick.o(i.delay_ms) for delay_ms
    ins_init.o(i.SetParaUpdateStart) refers to ins_init.o(i.UpdateStart_SetHead) for UpdateStart_SetHead
    ins_init.o(i.SetParaUpdateStart) refers to ins_init.o(i.UpdateStart_SetEnd) for UpdateStart_SetEnd
    ins_init.o(i.SetParaUpdateStart) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaUpdateStart) refers to main.o(.data) for g_StartUpdateFirm
    ins_init.o(i.SetParaUpdateStart) refers to ins_init.o(.data) for g_UpdateSuccessful
    ins_init.o(i.SetParaUpdateStop) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaUpdateStop) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaUpdateStop) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaUpdateStop) refers to ins_init.o(i.UpdateStop_SetHead) for UpdateStop_SetHead
    ins_init.o(i.SetParaUpdateStop) refers to ins_init.o(i.UpdateStop_SetEnd) for UpdateStop_SetEnd
    ins_init.o(i.SetParaUpdateStop) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaUpdateStop) refers to main.o(.data) for g_StartUpdateFirm
    ins_init.o(i.SetParaVector) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.SetParaVector) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.SetParaVector) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.SetParaVector) refers to ins_init.o(i.SendPara_SetHead) for SendPara_SetHead
    ins_init.o(i.SetParaVector) refers to ins_init.o(i.SendPara_SetEnd) for SendPara_SetEnd
    ins_init.o(i.SetParaVector) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    ins_init.o(i.SetParaVector) refers to ins_init.o(.bss) for stSetPara
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaBaud) for SetParaBaud
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaFrequency) for SetParaFrequency
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaGnss) for SetParaGnss
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaAngle) for SetParaAngle
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaVector) for SetParaVector
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaDeviation) for SetParaDeviation
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaGnssInitValue) for SetParaGnssInitValue
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaCoord) for SetParaCoord
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaTime) for SetParaTime
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SaveParaToFlash) for SaveParaToFlash
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.RestoreFactory) for RestoreFactory
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaAll) for SetParaAll
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.ReadPara) for ReadPara
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaGpsType) for SetParaGpsType
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaDataOutType) for SetParaDataOutType
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaDebugMode) for SetParaDebugMode
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaGyroType) for SetParaGyroType
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaCalibration) for SetParaCalibration
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaKalmanQ) for SetParaKalmanQ
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaKalmanR) for SetParaKalmanR
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaFilter) for SetParaFilter
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaFactorGyro) for SetParaFactorGyro
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaFactorAcc) for SetParaFactorAcc
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaUpdateStart) for SetParaUpdateStart
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaUpdateSend) for SetParaUpdateSend
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaUpdateEnd) for SetParaUpdateEnd
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaUpdateStop) for SetParaUpdateStop
    ins_init.o(i.UartDmaRecSetPara) refers to ins_init.o(i.SetParaSdHandle) for SetParaSdHandle
    ins_init.o(i.UpdateEnd_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.UpdateEnd_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.UpdateEnd_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.UpdateFileHandle) refers to ins_init.o(i.Drv_FlashErase) for Drv_FlashErase
    ins_init.o(i.UpdateFileHandle) refers to ins_init.o(i.Drv_FlashWrite) for Drv_FlashWrite
    ins_init.o(i.UpdateFileHandle) refers to ins_init.o(.data) for uiLastBaoInDex
    ins_init.o(i.UpdateSend_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.UpdateSend_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.UpdateSend_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.UpdateStart_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.UpdateStart_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.UpdateStart_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.UpdateStop_SetEnd) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.UpdateStop_SetEnd) refers to memcpya.o(.text) for __aeabi_memcpy
    ins_init.o(i.UpdateStop_SetEnd) refers to app_tool.o(i.crc_verify_8bit) for crc_verify_8bit
    ins_init.o(i.calcGPRMC_TRA) refers to dflti.o(.text) for __aeabi_i2d
    ins_init.o(i.calcGPRMC_TRA) refers to dmul.o(.text) for __aeabi_dmul
    ins_init.o(i.calcGPRMC_TRA) refers to d2f.o(.text) for __aeabi_d2f
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    ins_init.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    ins_init.o(i.gd_eval_com_init6) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    ins_init.o(i.gd_eval_com_init_basic) refers to main.o(i.UartIrqInit) for UartIrqInit
    ins_init.o(i.gd_eval_com_init_basic) refers to main.o(i.SDUartIrqInit) for SDUartIrqInit
    ins_init.o(i.get_16bit_D32) refers to ins_init.o(.data) for m16_uMemory
    ins_init.o(i.get_16bit_D64) refers to ins_init.o(.data) for m16_uMemory
    ins_init.o(i.get_16bit_Int32) refers to ins_init.o(.data) for m16_uMemory
    ins_init.o(i.loggingLogFile) refers to memseta.o(.text) for __aeabi_memclr4
    ins_init.o(i.loggingLogFile) refers to logger.o(i.generateCSVLogFileName) for generateCSVLogFileName
    ins_init.o(i.loggingLogFile) refers to logger.o(i.writeCSVLog) for writeCSVLog
    ins_init.o(i.norflash_init) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    api_ch392.o(i.SPI4_MasterRecvByte) refers to api_ch392.o(i.SPI4_WriteRead) for SPI4_WriteRead
    api_ch392.o(i.SPI4_MasterTransByte) refers to api_ch392.o(i.SPI4_WriteRead) for SPI4_WriteRead
    api_ch392.o(i.xReadCH392Data) refers to api_ch392.o(i.SPI4_MasterRecvByte) for SPI4_MasterRecvByte
    api_ch392.o(i.xWriteCH392Data) refers to api_ch392.o(i.SPI4_MasterTransByte) for SPI4_MasterTransByte
    api_ch392.o(i.xWriteCH392Data) refers to systick.o(i.delay_ms) for delay_ms
    datado.o(i.Algorithm_before_otherDataDo) refers to ins_data.o(i.caninfupdate) for caninfupdate
    datado.o(i.DeviceInit) refers to ins_init.o(i.INS_Init) for INS_Init
    datado.o(i.DeviceInit) refers to datado.o(i.SendVersionInfo) for SendVersionInfo
    datado.o(i.DeviceInit) refers to datado.o(.data) for gprotocol_send_baudrate
    datado.o(i.DeviceInit) refers to ins_init.o(.data) for fpga_syn_count
    datado.o(i.SendVersionInfo) refers to strcpy.o(.text) for strcpy
    datado.o(i.SendVersionInfo) refers to strlen.o(.text) for strlen
    datado.o(i.SendVersionInfo) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    datado.o(i.loopDoOther) refers to datado.o(.data) for counter
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_temperature) for compensate_temperature
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_pressure) for compensate_pressure
    bmp2.o(i.bmp2_compute_meas_time) refers to memcpya.o(.text) for __aeabi_memcpy4
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(.constdata) for .constdata
    bmp2.o(i.bmp2_get_config) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_power_mode) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.parse_sensor_data) for parse_sensor_data
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_compensate_data) for bmp2_compensate_data
    bmp2.o(i.bmp2_get_status) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.get_calib_param) for get_calib_param
    bmp2.o(i.bmp2_set_config) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_power_mode) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_set_regs) refers to bmp2.o(i.interleave_data) for interleave_data
    bmp2.o(i.bmp2_soft_reset) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.compensate_pressure) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_pressure) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_pressure) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_pressure) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_pressure) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_pressure) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_pressure) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.compensate_temperature) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_temperature) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_temperature) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_temperature) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_temperature) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_temperature) refers to dfixi.o(.text) for __aeabi_d2iz
    bmp2.o(i.compensate_temperature) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_temperature) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_soft_reset) for bmp2_soft_reset
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.set_os_mode) for set_os_mode
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.get_calib_param) refers to memseta.o(.text) for __aeabi_memclr4
    bmp2.o(i.get_calib_param) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.parse_sensor_data) refers to bmp2.o(i.st_check_boundaries) for st_check_boundaries
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_status) for bmp2_get_status
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_sensor_data) for bmp2_get_sensor_data
    bmp280.o(i.bmp280_init) refers to common.o(i.bmp2_interface_selection) for bmp2_interface_selection
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_init) for bmp2_init
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_get_config) for bmp2_get_config
    bmp280.o(i.bmp280_init) refers to bmp280.o(.bss) for bmpDev
    bmp280.o(i.bmp280_init) refers to bmp280.o(.data) for bmpCfg
    bsp_adc.o(i.adc_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_threshold_config) for adc_watchdog_threshold_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable) for adc_watchdog_single_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable) for adc_watchdog_group_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_interrupt_enable) for adc_interrupt_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    bsp_adc.o(i.adc_config) refers to systick.o(i.delay_ms) for delay_ms
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_enable) for rcu_bkp_reset_enable
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to bsp_can.o(.data) for hCAN0
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to ins_init.o(.data) for gcan0_rx_syn
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to bsp_can.o(.bss) for gCanRxBuf
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to ins_data.o(.bss) for hINSData
    bsp_can.o(i.CAN0_RX0_IRQHandler) refers to fpgad.o(.bss) for gcanInfo
    bsp_can.o(i.CAN1_RX0_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN1_RX0_IRQHandler) refers to bsp_can.o(.data) for hCAN1
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_deinit) for can_deinit
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_init) for can_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_filter_init) for can_filter_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_interrupt_enable) for can_interrupt_enable
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_struct_para_init) for can_struct_para_init
    bsp_can.o(i.bsp_can_transmit) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_message_transmit) for can_message_transmit
    bsp_can.o(i.can_transmit_acc) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_acc) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_angle) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_angle) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_gyro) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_gyro) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_h) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_h) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_pos) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_pos) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_speed) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_speed) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_status) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_status) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_std_heading) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_std_heading) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.can_transmit_temperature) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_temperature) refers to ins_data.o(.bss) for hINSCANData
    bsp_can.o(i.range) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bsp_can.o(i.range) refers to dadd.o(.text) for __aeabi_dsub
    bsp_can.o(i.stdDev) refers to dadd.o(.text) for __aeabi_dadd
    bsp_can.o(i.stdDev) refers to dfltui.o(.text) for __aeabi_ui2d
    bsp_can.o(i.stdDev) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_can.o(i.stdDev) refers to dmul.o(.text) for __aeabi_dmul
    bsp_can.o(i.stdDev) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    bsp_can.o(i.uart4sendmsg_canout) refers to printfa.o(i.__0sprintf) for __2sprintf
    bsp_can.o(i.uart4sendmsg_canout) refers to strlen.o(.text) for strlen
    bsp_can.o(i.uart4sendmsg_canout) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    bsp_can.o(i.uart4sendmsg_canout) refers to ins_init.o(.data) for gcan0_rx_syn
    bsp_can.o(i.uart4sendmsg_canout) refers to bsp_can.o(.conststring) for .conststring
    bsp_can.o(i.uart4sendmsg_canout) refers to gdwatch.o(.data) for gbilldebuguart4
    bsp_can.o(i.variance) refers to dadd.o(.text) for __aeabi_dadd
    bsp_can.o(i.variance) refers to dfltui.o(.text) for __aeabi_ui2d
    bsp_can.o(i.variance) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_can.o(i.variance) refers to dmul.o(.text) for __aeabi_dmul
    bsp_exti.o(i.EXTI3_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    bsp_exti.o(i.EXTI3_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    bsp_exti.o(i.EXTI3_IRQHandler) refers to ins_init.o(.data) for fpga_syn
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(i.bsp_exti_config) for bsp_exti_config
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(.data) for hEXTI
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.EndWrite) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(.data) for g_NeedWrite
    bsp_flash.o(i.InitFlashAddr) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.ReadFlash) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.ReadFlashByAddr) for ReadFlashByAddr
    bsp_flash.o(i.WriteFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.data) for g_Addr
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.bss) for g_FlashBuf
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.bss) for g_FlashBuf
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.data) for g_AddrBase
    bsp_fmc.o(i.Uart_SendMsg) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_init) for exmc_norsram_init
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_enable) for exmc_norsram_enable
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_init) for exmc_sdram_init
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_flag_get) for exmc_flag_get
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_command_config) for exmc_sdram_command_config
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to systick.o(i.delay_ms) for delay_ms
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set) for exmc_sdram_refresh_count_set
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_sector_info_get) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_selftest) refers to bsp_fmc.o(i.fmc_read_32bit_data) for fmc_read_32bit_data
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_16bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_32bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_8bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    bsp_fmc.o(i.fmc_write_8bit_data1) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fwdgt.o(i.bsp_fwdgt_feed) refers to gd32f4xx_fwdgt.o(i.fwdgt_counter_reload) for fwdgt_counter_reload
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_config) for fwdgt_config
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_enable) for fwdgt_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_get) for rtc_flag_get
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(i.rtc_show_timestamp) for rtc_show_timestamp
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(.data) for pTimestamep
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_pre_config) for rtc_pre_config
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_timestamp_enable) for rtc_timestamp_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_interrupt_enable) for rtc_interrupt_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to bsp_rtc.o(i.yearDay) for yearDay
    bsp_rtc.o(i.getRTCWeekSecond) refers to mktime.o(.text) for mktime
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_subsecond_get) for rtc_subsecond_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to f2d.o(.text) for __aeabi_f2d
    bsp_rtc.o(i.getRTCWeekSecond) refers to time_unify.o(i.time2gpst) for time2gpst
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    bsp_rtc.o(i.rtc_pre_config) refers to bsp_rtc.o(.data) for prescaler_s
    bsp_rtc.o(i.rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    bsp_rtc.o(i.rtc_show_timestamp) refers to memseta.o(.text) for __aeabi_memclr
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_get) for rtc_timestamp_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get) for rtc_timestamp_subsecond_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to printfa.o(i.__0sprintf) for __2sprintf
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2time) for gpst2time
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2utc) for gpst2utc
    bsp_rtc.o(i.timeSync) refers to localtime_w.o(.text) for localtime
    bsp_rtc.o(i.timeSync) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_rtc.o(i.timeSync) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.yearDay) refers to bsp_rtc.o(i.isLeapYear) for isLeapYear
    bsp_rtc.o(.data) refers to bsp_rtc.o(.bss) for gTimeStamp
    bsp_tim.o(i.bsp_tim_init) refers to printfa.o(i.__0printf) for __2printf
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    bsp_uart.o(i.USART4_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    bsp_uart.o(i.USART4_IRQHandler) refers to bsp_uart.o(.data) for grxlen
    bsp_uart.o(i.USART4_IRQHandler) refers to bsp_uart.o(.bss) for grxbuffer
    bsp_uart.o(i.USART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    bsp_uart.o(i.USART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    bsp_uart.o(i.USART6_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    bsp_uart.o(i.bsp_systick_init01) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    ch378_hal.o(i.CH378_PORT_INIT) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch378_hal.o(i.CH378_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch378_hal.o(i.CH378_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch378_hal.o(i.CH378_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_hal.o(i.CH378_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_hal.o(i.CH378_mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch378_hal.o(i.CH378_mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch378_hal.o(i.Query378Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch378_hal.o(i.mInitCH378Host) refers to ch378_hal.o(i.CH378_PORT_INIT) for CH378_PORT_INIT
    ch378_hal.o(i.mInitCH378Host) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_hal.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_hal.o(i.mInitCH378Host) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    ch378_hal.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    ch378_hal.o(i.mInitCH378Host) refers to ch378_hal.o(i.xReadCH378Data) for xReadCH378Data
    ch378_hal.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    ch378_hal.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_hal.o(i.mInitCH378Host) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    ch378_hal.o(i.mStopIfError) refers to printfa.o(i.__0printf) for __2printf
    ch378_hal.o(i.xReadCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_hal.o(i.xReadCH378Data) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch378_hal.o(i.xReadCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_hal.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_hal.o(i.xWriteCH378Cmd) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    ch378_hal.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_hal.o(i.xWriteCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_hal.o(i.xWriteCH378Data) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    ch378_hal.o(i.xWriteCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDCheckExist) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetVer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetVer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDInitCH395) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395CMDReset) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDReset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetPHY) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395CMDSleep) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetCmdStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395cmd.o(i.CH395GetCmdStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_crc_off) for spi_crc_off
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.CH395_RST) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.Query395Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch395spi.o(i.mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch395spi.o(i.mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch395spi.o(i.xReadCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.xWriteCH395Cmd) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395spi.o(i.xWriteCH395Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.xWriteCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    common.o(i.bmp2_delay_us) refers to systick.o(i.delay_us) for delay_us
    common.o(i.bmp2_i2c_read) refers to common.o(i.i2c_read) for i2c_read
    common.o(i.bmp2_i2c_read) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_i2c_write) refers to common.o(i.i2c_write) for i2c_write
    common.o(i.bmp2_i2c_write) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_interface_selection) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) for Soft_I2C_Master_Init
    common.o(i.bmp2_interface_selection) refers to systick.o(i.delay_ms) for delay_ms
    common.o(i.bmp2_interface_selection) refers to common.o(.data) for dev_addr
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_read) for bmp2_i2c_read
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_write) for bmp2_i2c_write
    common.o(i.bmp2_interface_selection) refers to common.o(.bss) for hSoftI2c
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_delay_us) for bmp2_delay_us
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Start) for Soft_I2C_Start
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) for Soft_I2C_Send_Byte
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) for Soft_I2C_Wait_Ack
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) for Soft_I2C_Read_Byte
    common.o(i.i2c_read) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    common.o(i.i2c_read) refers to common.o(.bss) for hSoftI2c
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Start) for Soft_I2C_Start
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) for Soft_I2C_Send_Byte
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) for Soft_I2C_Wait_Ack
    common.o(i.i2c_write) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    common.o(i.i2c_write) refers to common.o(.bss) for hSoftI2c
    file_sys.o(i.CH378DiskConnect) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskReady) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskStatus) refers to file_sys.o(i.CH378ReadVar8) for CH378ReadVar8
    file_sys.o(i.CH378GetFileSize) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetFileSize) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetFileSize) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378GetFileSize) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378GetFileSize) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378GetIntStatus) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetIntStatus) refers to ch378_hal.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378GetTrueLen) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetTrueLen) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetTrueLen) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378Read32bitDat) refers to ch378_hal.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378Read32bitDat) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378Read32bitDat) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadVar32) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar32) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar32) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378ReadVar8) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar8) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar8) refers to ch378_hal.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SendCmdWaitInt) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SetFileName) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileName) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileName) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SetFileSize) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileSize) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileSize) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileSize) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileSize) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar32) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar32) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar32) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar8) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar8) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.Wait378Interrupt) refers to ch378_hal.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.Wait378Interrupt) refers to ch378_spi_hw.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.Wait378Interrupt) refers to file_sys.o(i.CH378GetIntStatus) for CH378GetIntStatus
    file_sys.o(i.Wait378Interrupt) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    logger.o(i.generateCSVLogFileName) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.synthesisLogBuf) refers to memseta.o(.text) for __aeabi_memclr
    logger.o(i.synthesisLogBuf) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.synthesisLogBuf) refers to strcat.o(.text) for strcat
    logger.o(i.synthesisLogBuf) refers to strlen.o(.text) for strlen
    logger.o(i.synthesisLogBuf) refers to logger.o(.bss) for g_charBuf
    logger.o(i.writeCSVFileHead) refers to memcpya.o(.text) for __aeabi_memcpy4
    logger.o(i.writeCSVFileHead) refers to strlen.o(.text) for strlen
    logger.o(i.writeCSVFileHead) refers to logger.o(i.writeCSVLog) for writeCSVLog
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378DiskConnect) for CH378DiskConnect
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378DiskReady) for CH378DiskReady
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378WriteVar32) for CH378WriteVar32
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_crc_off) for spi_crc_off
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.Query378Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch378_spi_hw.o(i.SPI_Receive) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Receive) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch378_spi_hw.o(i.SPI_Transmit) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Transmit) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.CH378_Port_Init) for CH378_Port_Init
    ch378_spi_hw.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.mInitCH378Host) refers to systick.o(i.delay_us) for delay_us
    ch378_spi_hw.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.mInitCH378Host) refers to systick.o(i.delay_ms) for delay_ms
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_hal.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_hal.o(i.xWriteCH378Data) for xWriteCH378Data
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_hal.o(i.xReadCH378Data) for xReadCH378Data
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    ch378_spi_hw.o(i.xReadCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to systick.o(i.delay_us) for delay_us
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Data) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.xWriteCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_NAck) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) for Soft_I2C_SDA_Input
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_NAck) for Soft_I2C_NAck
    bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Ack) for Soft_I2C_Ack
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output) for Soft_I2C_SDA_Output
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to systick.o(i.delay_us) for delay_us
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input) for Soft_I2C_SDA_Input
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to bsp_soft_i2c_master.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    drv_spi.o(i.gd32_spi_cs_control) refers to drv_spi.o(i.gd32_spi_get_device) for gd32_spi_get_device
    drv_spi.o(i.gd32_spi_cs_control) refers to drv_spi.o(i.gd32_spi_cs_set) for gd32_spi_cs_set
    drv_spi.o(i.gd32_spi_cs_set) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    drv_spi.o(i.gd32_spi_cs_set) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    drv_spi.o(i.gd32_spi_device_init) refers to drv_spi.o(i.gd32_spi_init) for gd32_spi_init
    drv_spi.o(i.gd32_spi_device_init) refers to drv_spi.o(.data) for spi_devices
    drv_spi.o(i.gd32_spi_get_device) refers to drv_spi.o(.data) for spi_devices
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    drv_spi.o(i.gd32_spi_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    drv_spi.o(i.gd32_spi_read_byte) refers to drv_spi.o(i.gd32_spi_transfer_byte) for gd32_spi_transfer_byte
    drv_spi.o(i.gd32_spi_transfer) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    drv_spi.o(i.gd32_spi_transfer) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    drv_spi.o(i.gd32_spi_transfer) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    drv_spi.o(i.gd32_spi_transfer_buffer) refers to drv_spi.o(i.gd32_spi_get_device) for gd32_spi_get_device
    drv_spi.o(i.gd32_spi_transfer_buffer) refers to drv_spi.o(i.gd32_spi_transfer) for gd32_spi_transfer
    drv_spi.o(i.gd32_spi_transfer_byte) refers to drv_spi.o(i.gd32_spi_get_device) for gd32_spi_get_device
    drv_spi.o(i.gd32_spi_transfer_byte) refers to drv_spi.o(i.gd32_spi_transfer) for gd32_spi_transfer
    drv_spi.o(i.gd32_spi_write_byte) refers to drv_spi.o(i.gd32_spi_transfer_byte) for gd32_spi_transfer_byte
    drv_gpio.o(i.EXTI0_IRQHandler) refers to drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) for GD32_GPIO_EXTI_IRQHandler
    drv_gpio.o(i.EXTI1_IRQHandler) refers to drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) for GD32_GPIO_EXTI_IRQHandler
    drv_gpio.o(i.EXTI2_IRQHandler) refers to drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) for GD32_GPIO_EXTI_IRQHandler
    drv_gpio.o(i.EXTI4_IRQHandler) refers to drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) for GD32_GPIO_EXTI_IRQHandler
    drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler) refers to drv_gpio.o(.data) for pin_irq_hdr_tab
    drv_gpio.o(i.gd32_pin_attach_irq) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_attach_irq) refers to drv_gpio.o(i.bit2bitno) for bit2bitno
    drv_gpio.o(i.gd32_pin_attach_irq) refers to drv_gpio.o(.data) for pin_irq_hdr_tab
    drv_gpio.o(i.gd32_pin_detach_irq) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_detach_irq) refers to drv_gpio.o(i.bit2bitno) for bit2bitno
    drv_gpio.o(i.gd32_pin_detach_irq) refers to drv_gpio.o(.data) for pin_irq_hdr_tab
    drv_gpio.o(i.gd32_pin_irq_enable) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_irq_enable) refers to drv_gpio.o(i.bit2bitno) for bit2bitno
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    drv_gpio.o(i.gd32_pin_irq_enable) refers to gd32f4xx_misc.o(i.nvic_irq_disable) for nvic_irq_disable
    drv_gpio.o(i.gd32_pin_irq_enable) refers to drv_gpio.o(.data) for pin_irq_hdr_tab
    drv_gpio.o(i.gd32_pin_irq_enable) refers to drv_gpio.o(.constdata) for pin_irq_map
    drv_gpio.o(i.gd32_pin_mode) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_mode) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    drv_gpio.o(i.gd32_pin_mode) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    drv_gpio.o(i.gd32_pin_mode) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    drv_gpio.o(i.gd32_pin_read) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_read) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    drv_gpio.o(i.gd32_pin_write) refers to drv_gpio.o(i.get_pin) for get_pin
    drv_gpio.o(i.gd32_pin_write) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    drv_gpio.o(i.get_pin) refers to drv_gpio.o(.constdata) for pins
    drv_gpio.o(i.pin_irq_install) refers to drv_gpio.o(i.gd32_pin_mode) for gd32_pin_mode
    drv_gpio.o(i.pin_irq_install) refers to drv_gpio.o(i.gd32_pin_attach_irq) for gd32_pin_attach_irq
    drv_gpio.o(i.pin_irq_install) refers to drv_gpio.o(i.gd32_pin_irq_enable) for gd32_pin_irq_enable
    data_convert.o(i.GetDouble) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    data_convert.o(i.GetNumber) refers to atoi.o(.text) for atoi
    data_convert.o(i.dec2HexStr) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.dtoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.dtoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.dtoc) refers to data_convert.o(.constdata) for .constdata
    data_convert.o(i.ftoa) refers to cdcmple.o(.text) for __aeabi_cdcmple
    data_convert.o(i.ftoa) refers to dadd.o(.text) for __aeabi_dsub
    data_convert.o(i.ftoa) refers to dfixi.o(.text) for __aeabi_d2iz
    data_convert.o(i.ftoa) refers to dflti.o(.text) for __aeabi_i2d
    data_convert.o(i.ftoa) refers to d2f.o(.text) for __aeabi_d2f
    data_convert.o(i.itoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.itoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.itoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.itoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.str2bin) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.str2bin) refers to strncpy.o(.text) for strncpy
    data_convert.o(i.str2bin) refers to data_convert.o(i.strbin2u16) for strbin2u16
    data_convert.o(i.str2bin) refers to strlen.o(.text) for strlen
    data_convert.o(i.str2bin) refers to malloc.o(i.free) for free
    data_convert.o(i.strInStrCount) refers to strstr.o(.text) for strstr
    data_convert.o(i.strReplace) refers to strlen.o(.text) for strlen
    data_convert.o(i.strReplace) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.strReplace) refers to strncmp.o(.text) for strncmp
    data_convert.o(i.strReplace) refers to strcat.o(.text) for strcat
    data_convert.o(i.strReplace) refers to strncat.o(.text) for strncat
    data_convert.o(i.strReplace) refers to strcpy.o(.text) for strcpy
    data_convert.o(i.strReplace) refers to malloc.o(i.free) for free
    data_convert.o(i.strReverse) refers to strlen.o(.text) for strlen
    data_convert.o(i.strSplit) refers to strtok.o(.text) for strtok
    data_convert.o(i.strbin2u16) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetBytesInBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_HasDataUp) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_ReadUpBuffer) refers to segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) for SEGGER_RTT_ReadUpBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for _aTerminalId
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for _ActiveTerminal
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) for SEGGER_RTT_WriteDownBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for _SEGGER_RTT
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.constdata) for _aInitStr
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for _aTerminalId
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for _aV2C
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    private_math.o(i.SQR) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.dotn) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.dotn) refers to dadd.o(.text) for __aeabi_dadd
    private_math.o(i.ecef2geo) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.ecef2geo) refers to private_math.o(i.dotn) for dotn
    private_math.o(i.ecef2geo) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.ecef2geo) refers to dadd.o(.text) for __aeabi_dadd
    private_math.o(i.ecef2geo) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    private_math.o(i.ecef2geo) refers to ddiv.o(.text) for __aeabi_ddiv
    private_math.o(i.ecef2geo) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    private_math.o(i.ecef2geo) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    private_math.o(i.ecef2geo) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    private_math.o(i.ecef2geo) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    private_math.o(i.ecef2ned) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.ecef2ned) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.ecef2ned) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    private_math.o(i.ecef2ned) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    private_math.o(i.ecef2ned) refers to dadd.o(.text) for __aeabi_dadd
    private_math.o(i.geo2ecef) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.geo2ecef) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.geo2ecef) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    private_math.o(i.geo2ecef) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    private_math.o(i.geo2ecef) refers to dadd.o(.text) for __aeabi_drsub
    private_math.o(i.geo2ecef) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    private_math.o(i.geo2ecef) refers to ddiv.o(.text) for __aeabi_ddiv
    private_math.o(i.geo2ned) refers to private_math.o(i.geo2ecef) for geo2ecef
    private_math.o(i.geo2ned) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.geo2ned) refers to dadd.o(.text) for __aeabi_dsub
    private_math.o(i.geo2ned) refers to private_math.o(i.ecef2ned) for ecef2ned
    private_math.o(i.iggii) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    private_math.o(i.iggii) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    private_math.o(i.iggii) refers to ddiv.o(.text) for __aeabi_ddiv
    private_math.o(i.iggii) refers to cdcmple.o(.text) for __aeabi_cdcmple
    private_math.o(i.ned2ecef) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.ned2ecef) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.ned2ecef) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    private_math.o(i.ned2ecef) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    private_math.o(i.ned2ecef) refers to dadd.o(.text) for __aeabi_dadd
    private_math.o(i.ned2geo) refers to private_math.o(i.geo2ecef) for geo2ecef
    private_math.o(i.ned2geo) refers to private_math.o(i.ned2ecef) for ned2ecef
    private_math.o(i.ned2geo) refers to dadd.o(.text) for __aeabi_dadd
    private_math.o(i.ned2geo) refers to private_math.o(i.ecef2geo) for ecef2geo
    private_math.o(i.state_constrain) refers to cdcmple.o(.text) for __aeabi_cdcmple
    private_math.o(i.state_constrain) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    private_math.o(i.v3_cross) refers to memseta.o(.text) for __aeabi_memclr4
    private_math.o(i.v3_cross) refers to dmul.o(.text) for __aeabi_dmul
    private_math.o(i.v3_cross) refers to dadd.o(.text) for __aeabi_dsub
    readpaoche.o(i.fmc2sinsraw) refers to readpaoche.o(i.myget_16bit_I32) for myget_16bit_I32
    readpaoche.o(i.fmc2sinsraw) refers to dflti.o(.text) for __aeabi_i2d
    readpaoche.o(i.fmc2sinsraw) refers to ddiv.o(.text) for __aeabi_ddiv
    readpaoche.o(i.fmc2sinsraw) refers to dadd.o(.text) for __aeabi_dadd
    readpaoche.o(i.fmc2sinsraw) refers to readpaoche.o(i.myget_8bit_I16) for myget_8bit_I16
    readpaoche.o(i.fmc2sinsraw) refers to dmul.o(.text) for __aeabi_dmul
    readpaoche.o(i.fmc2sinsraw) refers to readpaoche.o(.data) for rcv_state
    readpaoche.o(i.fmc2sinsraw) refers to dfltui.o(.text) for __aeabi_ui2d
    readpaoche.o(i.fmc2sinsraw) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    readpaoche.o(i.fmc2sinsraw) refers to readpaoche.o(i.myget_16bit_D32) for myget_16bit_D32
    readpaoche.o(i.fmc2sinsraw) refers to f2d.o(.text) for __aeabi_f2d
    readpaoche.o(i.fmc2sinsraw) refers to readpaoche.o(i.myget_16bit_D64) for myget_16bit_D64
    navi.o(i.AttiToCnb) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.AttiToCnb) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.AttiToCnb) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.AttiToCnb) refers to dadd.o(.text) for __aeabi_dsub
    navi.o(i.AttiToCpb) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.AttiToCpb) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.AttiToCpb) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.AttiToCpb) refers to dadd.o(.text) for __aeabi_dsub
    navi.o(i.CnbToAtti) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    navi.o(i.CnbToAtti) refers to cdcmple.o(.text) for __aeabi_cdcmple
    navi.o(i.CnbToAtti) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.CnbToAtti) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.CnbToAtti) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    navi.o(i.CnbToAtti) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.CnbToAtti) refers to dadd.o(.text) for __aeabi_dsub
    navi.o(i.CnbToAtti) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    navi.o(i.CnbToQ) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    navi.o(i.CnbToQ) refers to cdcmple.o(.text) for __aeabi_cdcmple
    navi.o(i.CnbToQ) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.CnbToQ) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    navi.o(i.CnbToQ) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.CnbToQ) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.ComputeAttiRate) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.ComputeAttiRate) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.ComputeAttiRate) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    navi.o(i.ComputeAttiRate) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.ComputeAttiRate) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeAttiRate) refers to dadd.o(.text) for __aeabi_dsub
    navi.o(i.ComputeDeg_Ex) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeDelSenbb) refers to memseta.o(.text) for __aeabi_memclr4
    navi.o(i.ComputeDelSenbb) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeDelSenbb) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    navi.o(i.ComputeDelSenbb) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeG) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeG) refers to dadd.o(.text) for __aeabi_drsub
    navi.o(i.ComputeG) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.ComputeLeverArmSn) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    navi.o(i.ComputeLeverArmSn) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    navi.o(i.ComputeLeverArmVn) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    navi.o(i.ComputeLeverArmVn) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    navi.o(i.ComputeLeverArmVn) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    navi.o(i.ComputePos) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputePos) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputePos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.ComputePos) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.ComputePos) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.ComputePos) refers to cdcmple.o(.text) for __aeabi_cdcmple
    navi.o(i.ComputeQ) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeQ) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeQ) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    navi.o(i.ComputeQ) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.ComputeQ) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    navi.o(i.ComputeQ) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.ComputeQ) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.ComputeQ) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.ComputeQ) refers to matvecmath.o(i.Qua_Mul) for Qua_Mul
    navi.o(i.ComputeRmRn) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.ComputeRmRn) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeRmRn) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeRmRn) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.ComputeVb) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeVb) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeVibn) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeVibn) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    navi.o(i.ComputeVibn) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeVibn) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    navi.o(i.ComputeVibn) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    navi.o(i.ComputeVn) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeVn) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeVn) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    navi.o(i.ComputeVn) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.ComputeVn) refers to main.o(.bss) for g_SysVar
    navi.o(i.ComputeVp) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeVp) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeWenn) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeWenn) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    navi.o(i.ComputeWien) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navi.o(i.ComputeWien) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.ComputeWien) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navi.o(i.ComputeWnbb) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.ComputeWnbb) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    navi.o(i.DampedVp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    navi.o(i.DampedVp) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.DampedVp) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.DynamicNavi_Init) refers to navi.o(i.CnbToAtti) for CnbToAtti
    navi.o(i.DynamicNavi_Init) refers to navi.o(i.AttiToCnb) for AttiToCnb
    navi.o(i.DynamicNavi_Init) refers to navi.o(i.CnbToQ) for CnbToQ
    navi.o(i.DynamicNavi_Init) refers to navi.o(i.ComputeDeg_Ex) for ComputeDeg_Ex
    navi.o(i.DynamicNavi_Init) refers to navi.o(i.TransHeading0to360) for TransHeading0to360
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeWien) for ComputeWien
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeRmRn) for ComputeRmRn
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeWenn) for ComputeWenn
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeWnbb) for ComputeWnbb
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeDelSenbb) for ComputeDelSenbb
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeQ) for ComputeQ
    navi.o(i.NaviCompute) refers to navi.o(i.QToCnb) for QToCnb
    navi.o(i.NaviCompute) refers to navi.o(i.CnbToAtti) for CnbToAtti
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeAttiRate) for ComputeAttiRate
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeG) for ComputeG
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeVibn) for ComputeVibn
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeVn) for ComputeVn
    navi.o(i.NaviCompute) refers to navi.o(i.ComputePos) for ComputePos
    navi.o(i.NaviCompute) refers to navi.o(i.ComputeDeg_Ex) for ComputeDeg_Ex
    navi.o(i.NaviCompute) refers to navi.o(i.TransHeading0to360) for TransHeading0to360
    navi.o(i.NaviCompute) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.NaviCompute) refers to ddiv.o(.text) for __aeabi_ddiv
    navi.o(i.NaviCompute) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    navi.o(i.NaviCompute) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.NaviCompute) refers to kalman.o(i.ErrCorrect_1_Navi_Time) for ErrCorrect_1_Navi_Time
    navi.o(i.NaviCompute) refers to main.o(.bss) for g_SysVar
    navi.o(i.Navi_Init) refers to memseta.o(.text) for __aeabi_memclr4
    navi.o(i.Navi_Init) refers to navi.o(i.CnbToAtti) for CnbToAtti
    navi.o(i.Navi_Init) refers to navi.o(i.ComputeDeg_Ex) for ComputeDeg_Ex
    navi.o(i.Navi_Init) refers to navi.o(i.TransHeading0to360) for TransHeading0to360
    navi.o(i.QToCnb) refers to dmul.o(.text) for __aeabi_dmul
    navi.o(i.QToCnb) refers to dadd.o(.text) for __aeabi_dadd
    navi.o(i.SysInit) refers to memseta.o(.text) for __aeabi_memclr4
    navi.o(i.SysInit) refers to navi.o(i.SysVarDefaultSet) for SysVarDefaultSet
    navi.o(i.SysInit) refers to kalman.o(i.Kalman_Init) for Kalman_Init
    navi.o(i.SysInit) refers to main.o(.bss) for g_SysVar
    navi.o(i.SysVarDefaultSet) refers to main.o(.bss) for g_SysVar
    navi.o(i.TransHeading0to360) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    navi.o(i.TransHeading0to360) refers to dadd.o(.text) for __aeabi_dsub
    align.o(i.ComputeCen) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    align.o(i.ComputeCen) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    align.o(i.ComputeCen) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeCib0i) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    align.o(i.ComputeCib0i) refers to matvecmath.o(i.Mat_Inv) for Mat_Inv
    align.o(i.ComputeCib0i) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    align.o(i.ComputeCie) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeCie) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    align.o(i.ComputeCie) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    align.o(i.ComputeVG) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeVG) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.ComputeVG) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    align.o(i.ComputeVG) refers to ddiv.o(.text) for __aeabi_ddiv
    align.o(i.ComputeVG) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    align.o(i.ComputeVG) refers to cdcmple.o(.text) for __aeabi_cdcmple
    align.o(i.ComputeVG) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    align.o(i.ComputeVG) refers to navi.o(i.AttiToCnb) for AttiToCnb
    align.o(i.ComputeVG) refers to navi.o(i.CnbToQ) for CnbToQ
    align.o(i.ComputeVGDelSenbb) refers to memseta.o(.text) for __aeabi_memclr4
    align.o(i.ComputeVGDelSenbb) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeVGDelSenbb) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.ComputeVGDelSenbb) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    align.o(i.ComputeVGSmooth) refers to memcpya.o(.text) for __aeabi_memcpy4
    align.o(i.ComputeVGSmooth) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    align.o(i.ComputeVGSmooth) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeVGSmooth) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.ComputeVGSmooth) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    align.o(i.ComputeVGSmooth) refers to ddiv.o(.text) for __aeabi_ddiv
    align.o(i.ComputeVGSmooth) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    align.o(i.ComputeVGSmooth) refers to align.o(i.ComputeVGDelSenbb) for ComputeVGDelSenbb
    align.o(i.ComputeVGSmooth) refers to navi.o(i.ComputeQ) for ComputeQ
    align.o(i.ComputeVGSmooth) refers to navi.o(i.QToCnb) for QToCnb
    align.o(i.ComputeVGSmooth) refers to navi.o(i.CnbToAtti) for CnbToAtti
    align.o(i.ComputeVGSmooth) refers to align.o(.constdata) for .constdata
    align.o(i.ComputeVi) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    align.o(i.ComputeVi) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    align.o(i.ComputeVi) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeVi) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.ComputeVib0) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    align.o(i.ComputeVib0) refers to dmul.o(.text) for __aeabi_dmul
    align.o(i.ComputeVib0) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.FinishInertialSysAlign) refers to align.o(i.ComputeCib0i) for ComputeCib0i
    align.o(i.FinishInertialSysAlign) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    align.o(i.FinishInertialSysAlign) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    align.o(i.FinishInertialSysAlign) refers to navi.o(i.CnbToAtti) for CnbToAtti
    align.o(i.FinishInertialSysAlign) refers to navi.o(i.AttiToCnb) for AttiToCnb
    align.o(i.FinishInertialSysAlign) refers to navi.o(i.CnbToQ) for CnbToQ
    align.o(i.InertialSysAlignCompute) refers to align.o(i.ComputeCie) for ComputeCie
    align.o(i.InertialSysAlignCompute) refers to navi.o(i.ComputeDelSenbb) for ComputeDelSenbb
    align.o(i.InertialSysAlignCompute) refers to navi.o(i.ComputeQ) for ComputeQ
    align.o(i.InertialSysAlignCompute) refers to navi.o(i.QToCnb) for QToCnb
    align.o(i.InertialSysAlignCompute) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    align.o(i.InertialSysAlignCompute) refers to navi.o(i.ComputeG) for ComputeG
    align.o(i.InertialSysAlignCompute) refers to align.o(i.ComputeVi) for ComputeVi
    align.o(i.InertialSysAlignCompute) refers to align.o(i.ComputeVib0) for ComputeVib0
    align.o(i.InertialSysAlignCompute) refers to dadd.o(.text) for __aeabi_dadd
    align.o(i.InertialSysAlignCompute) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    align.o(i.InertialSysAlign_Init) refers to memseta.o(.text) for __aeabi_memclr4
    align.o(i.InertialSysAlign_Init) refers to align.o(i.ComputeCen) for ComputeCen
    anntempcompen.o(i.ANN_Predict) refers to memseta.o(.text) for __aeabi_memclr4
    anntempcompen.o(i.ANN_Predict) refers to dadd.o(.text) for __aeabi_dsub
    anntempcompen.o(i.ANN_Predict) refers to ddiv.o(.text) for __aeabi_ddiv
    anntempcompen.o(i.ANN_Predict) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    anntempcompen.o(i.ANN_Predict) refers to matvecmath.o(i.Relu) for Relu
    anntempcompen.o(i.ANN_Predict) refers to matvecmath.o(i.MultiDim_Vec_Dot) for MultiDim_Vec_Dot
    anntempcompen.o(i.ANN_Predict) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputeFk) refers to dfltui.o(.text) for __aeabi_ui2d
    kalman.o(i.ComputeFk) refers to ddiv.o(.text) for __aeabi_ddiv
    kalman.o(i.ComputeFk) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputeFk) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeFk) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    kalman.o(i.ComputeFk) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputeFk_2) refers to dfltui.o(.text) for __aeabi_ui2d
    kalman.o(i.ComputeFk_2) refers to ddiv.o(.text) for __aeabi_ddiv
    kalman.o(i.ComputeFk_2) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputeFk_2) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeFk_2) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    kalman.o(i.ComputeFk_2) refers to memseta.o(.text) for __aeabi_memclr4
    kalman.o(i.ComputeFk_2) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputeFk_2) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputeFn) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    kalman.o(i.ComputeFn) refers to ddiv.o(.text) for __aeabi_ddiv
    kalman.o(i.ComputeFn) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    kalman.o(i.ComputeFn) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputeFn) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeFn) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputeKk) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    kalman.o(i.ComputeKk) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputeKk) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeKk) refers to matvecmath.o(i.Mat_Inv) for Mat_Inv
    kalman.o(i.ComputeKk) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputeKkTest) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    kalman.o(i.ComputeKkTest) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputeKkTest) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeKkTest) refers to matvecmath.o(i.Mat_Inv) for Mat_Inv
    kalman.o(i.ComputeKkTest) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputePk) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputePk) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputePk) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputePk) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputePkPosDef) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputePkPosDef) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    kalman.o(i.ComputePkPosDef) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputePkPosDef) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputePkPosDef) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputePkk_1_Step1) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    kalman.o(i.ComputePkk_1_Step1) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputePkk_1_Step2) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputePkk_1_Step2) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeXk) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ComputeXk) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.ComputeXk) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ComputeXkk_1) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ComputeXkk_1) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.ComputeZk) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.ComputeZk) refers to cdcmple.o(.text) for __aeabi_cdcmple
    kalman.o(i.ComputeZk) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    kalman.o(i.CorrectAccBias) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.CorrectAccScaleFacErr) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.CorrectAtti) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.CorrectAtti) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.CorrectAtti) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    kalman.o(i.CorrectAtti) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    kalman.o(i.CorrectAtti) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    kalman.o(i.CorrectAtti) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    kalman.o(i.CorrectAtti) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    kalman.o(i.CorrectAtti) refers to ddiv.o(.text) for __aeabi_ddiv
    kalman.o(i.CorrectAtti) refers to matvecmath.o(i.Qua_Mul) for Qua_Mul
    kalman.o(i.CorrectGyroBias) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.CorrectGyroScaleFacErr) refers to dadd.o(.text) for __aeabi_dadd
    kalman.o(i.CorrectPos) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.CorrectVn) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to ddiv.o(.text) for __aeabi_ddiv
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to dadd.o(.text) for __aeabi_drsub
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to kalman.o(i.CorrectVn) for CorrectVn
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ErrCorrect_1_Navi_Time) refers to kalman.o(i.CorrectAtti) for CorrectAtti
    kalman.o(i.ErrCorrect_1_Navi_Time_For_INS) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.ErrCorrect_1_Navi_Time_For_INS) refers to kalman.o(i.CorrectVn) for CorrectVn
    kalman.o(i.ErrCorrect_1_Navi_Time_For_INS) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    kalman.o(i.ErrCorrect_1_Navi_Time_For_INS) refers to kalman.o(i.CorrectAtti) for CorrectAtti
    kalman.o(i.ErrCorrect_1_Navi_Time_For_INS) refers to main.o(.bss) for g_Kalman
    kalman.o(i.ErrStore_1s) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.ErrStore_1s) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    kalman.o(i.ErrStore_1s) refers to cdcmple.o(.text) for __aeabi_cdcmple
    kalman.o(i.ErrStore_1s) refers to dmul.o(.text) for __aeabi_dmul
    kalman.o(i.ErrStore_1s) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    kalman.o(i.ErrStore_1s) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    kalman.o(i.ErrStore_1s) refers to main.o(.bss) for g_Kalman
    kalman.o(i.KalCompute) refers to kalman.o(i.Rk_Init) for Rk_Init
    kalman.o(i.KalCompute) refers to kalman.o(i.Hk_Init) for Hk_Init
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputePkk_1_Step1) for ComputePkk_1_Step1
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputePkk_1_Step2) for ComputePkk_1_Step2
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputeKk) for ComputeKk
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputePk) for ComputePk
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputeZk) for ComputeZk
    kalman.o(i.KalCompute) refers to kalman.o(i.ComputeXk) for ComputeXk
    kalman.o(i.KalPredict) refers to kalman.o(i.ComputeXkk_1) for ComputeXkk_1
    kalman.o(i.Kalman_Init) refers to memseta.o(.text) for __aeabi_memclr4
    kalman.o(i.Kalman_Init) refers to kalman.o(i.Pk_Init) for Pk_Init
    kalman.o(i.Kalman_Init) refers to kalman.o(i.Qk_Init) for Qk_Init
    kalman.o(i.Kalman_Init) refers to kalman.o(i.Rk_Init) for Rk_Init
    kalman.o(i.Kalman_Init) refers to kalman.o(i.Hk_Init) for Hk_Init
    kalman.o(i.Kalman_StartUp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    kalman.o(i.Kalman_StartUp) refers to dadd.o(.text) for __aeabi_dsub
    kalman.o(i.Kalman_StartUp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    kalman.o(i.Kalman_StartUp) refers to navi.o(i.AttiToCnb) for AttiToCnb
    kalman.o(i.Kalman_StartUp) refers to navi.o(i.CnbToQ) for CnbToQ
    kalman.o(i.Kalman_StartUp) refers to kalman.o(i.Kalman_Init) for Kalman_Init
    kalman.o(i.Kalman_StartUp) refers to kalman.o(i.Rk_Init) for Rk_Init
    kalman.o(i.Kalman_StartUp) refers to kalman.o(i.Hk_Init) for Hk_Init
    kalman.o(i.Kalman_StartUp) refers to main.o(.bss) for g_Kalman
    kalman.o(i.Rk_Init) refers to main.o(.bss) for g_Kalman
    matvecmath.o(i.DPARAMax2) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matvecmath.o(i.DPARAMin2) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matvecmath.o(i.DoubleMax3) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matvecmath.o(i.IIR_Filter) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.IIR_Filter) refers to dadd.o(.text) for __aeabi_dadd
    matvecmath.o(i.LinerLeastSquareFit) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    matvecmath.o(i.LinerLeastSquareFit) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    matvecmath.o(i.Mat_Inv) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    matvecmath.o(i.Mat_Inv) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matvecmath.o(i.Mat_Inv) refers to ddiv.o(.text) for __aeabi_ddiv
    matvecmath.o(i.Mat_Inv) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Mat_Inv) refers to dadd.o(.text) for __aeabi_drsub
    matvecmath.o(i.Mat_Mul) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Mat_Mul) refers to dadd.o(.text) for __aeabi_dadd
    matvecmath.o(i.MultiDim_Vec_Dot) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.MultiDim_Vec_Dot) refers to dadd.o(.text) for __aeabi_dadd
    matvecmath.o(i.Ord2LeastSquareFit) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Ord2LeastSquareFit) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    matvecmath.o(i.Ord2LeastSquareFit) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    matvecmath.o(i.Ord3LeastSquareFit) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Ord3LeastSquareFit) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    matvecmath.o(i.Ord3LeastSquareFit) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    matvecmath.o(i.Ord4LeastSquareFit) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Ord4LeastSquareFit) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    matvecmath.o(i.Ord4LeastSquareFit) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    matvecmath.o(i.PolyVal) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.PolyVal) refers to dadd.o(.text) for __aeabi_dadd
    matvecmath.o(i.Qua_Mul) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Qua_Mul) refers to dadd.o(.text) for __aeabi_dsub
    matvecmath.o(i.Relu) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matvecmath.o(i.Vec_Cross) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Vec_Cross) refers to dadd.o(.text) for __aeabi_dsub
    matvecmath.o(i.Vec_Dot) refers to dmul.o(.text) for __aeabi_dmul
    matvecmath.o(i.Vec_Dot) refers to dadd.o(.text) for __aeabi_dadd
    dynamic_align.o(i.ComputeSi) refers to dmul.o(.text) for __aeabi_dmul
    dynamic_align.o(i.ComputeSi) refers to dadd.o(.text) for __aeabi_dadd
    dynamic_align.o(i.ComputeSi) refers to matvecmath.o(i.Vec_Cross) for Vec_Cross
    dynamic_align.o(i.ComputeSi) refers to ddiv.o(.text) for __aeabi_ddiv
    dynamic_align.o(i.ComputeSi) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    dynamic_align.o(i.ComputeSi) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    dynamic_align.o(i.ComputeSib0) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    dynamic_align.o(i.ComputeSib0) refers to dmul.o(.text) for __aeabi_dmul
    dynamic_align.o(i.ComputeSib0) refers to dadd.o(.text) for __aeabi_dadd
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to dynamic_align.o(i.UpdateAlignPosAndVn) for UpdateAlignPosAndVn
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to align.o(i.ComputeCen) for ComputeCen
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeG) for ComputeG
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeWien) for ComputeWien
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeRmRn) for ComputeRmRn
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeWenn) for ComputeWenn
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to dynamic_align.o(i.ComputeSi) for ComputeSi
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to align.o(i.ComputeCie) for ComputeCie
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeDelSenbb) for ComputeDelSenbb
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.ComputeQ) for ComputeQ
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to navi.o(i.QToCnb) for QToCnb
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to dynamic_align.o(i.ComputeSib0) for ComputeSib0
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to dadd.o(.text) for __aeabi_dadd
    dynamic_align.o(i.DynamicInertialSysAlignCompute) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    dynamic_align.o(i.DynamicInertialSysAlign_Init) refers to memseta.o(.text) for __aeabi_memclr4
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to align.o(i.ComputeCib0i) for ComputeCib0i
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to navi.o(i.CnbToAtti) for CnbToAtti
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to navi.o(i.AttiToCnb) for AttiToCnb
    dynamic_align.o(i.FinishDynamicInertialSysAlign) refers to navi.o(i.CnbToQ) for CnbToQ
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to align.o(i.ComputeCib0i) for ComputeCib0i
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to matvecmath.o(i.Mat_Mul) for Mat_Mul
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to matvecmath.o(i.Mat_Tr) for Mat_Tr
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to navi.o(i.CnbToAtti) for CnbToAtti
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to navi.o(i.AttiToCnb) for AttiToCnb
    dynamic_align.o(i.VGDynamicInertialSysAlign) refers to navi.o(i.CnbToQ) for CnbToQ
    read_and_check_gnss_data.o(i.GNSSAndHeadDataTest) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    read_and_check_gnss_data.o(i.GNSSAndHeadDataTest) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    read_and_check_gnss_data.o(i.Read_And_Check_GNSS_Data) refers to read_and_check_gnss_data.o(i.SaveGNSSData) for SaveGNSSData
    read_and_check_gnss_data.o(i.Read_And_Check_GNSS_Data) refers to read_and_check_gnss_data.o(i.GNSSAndHeadDataTest) for GNSSAndHeadDataTest
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to dmul.o(.text) for __aeabi_dmul
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to navi.o(i.ComputeLeverArmSn) for ComputeLeverArmSn
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to dadd.o(.text) for __aeabi_drsub
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to cdcmple.o(.text) for __aeabi_cdcmple
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to memseta.o(.text) for __aeabi_memclr4
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to navi.o(i.ComputeLeverArmVn) for ComputeLeverArmVn
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    read_and_check_gnss_data.o(i.SaveGNSSData) refers to main.o(.bss) for g_Navi
    computerframeparse.o(i.comm_axis_read) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to memcpya.o(.text) for __aeabi_memcpy
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_cali_ehco_rsp) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_calib) refers to computerframeparse.o(.data) for calibFlag
    computerframeparse.o(i.comm_fm_update) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_fm_update) refers to computerframeparse.o(.data) for hDefaultSetting
    computerframeparse.o(i.comm_fm_update) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_get_baseline) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_para_ehco_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_para_ehco_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_para_ehco_rsp) refers to computerframeparse.o(.bss) for frame
    computerframeparse.o(i.comm_param_clrbits) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_param_setbits) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_readAccelCaliPtr) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_readGyroCaliPtr) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_read_currentFreq) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_read_dev_type_rsp) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_read_ver_rsp) refers to strlen.o(.text) for strlen
    computerframeparse.o(i.comm_read_ver_rsp) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_read_ver_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_read_ver_rsp) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_resume_defaultPara) refers to memcpya.o(.text) for __aeabi_memcpy4
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(i.comm_set_customPara) for comm_set_customPara
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(.data) for hDefaultSetting
    computerframeparse.o(i.comm_resume_defaultPara) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_saveCaliData) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_saveCaliData) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(.data) for imu_type_save_addr
    computerframeparse.o(i.comm_saveCaliData) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.comm_send_end_frame) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.comm_set_customPara) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_set_customPara) refers to bsp_fmc.o(i.fmc_write_8bit_data) for fmc_write_8bit_data
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(.data) for save_addr
    computerframeparse.o(i.comm_set_customPara) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.fmc_read_8bit_data) for fmc_read_8bit_data
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.hash32) for hash32
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.fmc_erase_sector_by_address) for fmc_erase_sector_by_address
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_resume_defaultPara) for comm_resume_defaultPara
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_fm_update) for comm_fm_update
    computerframeparse.o(i.comm_store_init) refers to memseta.o(.text) for __aeabi_memclr4
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(i.adj_paraSyn) for adj_paraSyn
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.Uart_TxInit) for Uart_TxInit
    computerframeparse.o(i.comm_store_init) refers to bsp_fmc.o(i.Uart_RxInit) for Uart_RxInit
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(.bss) for hSetting
    computerframeparse.o(i.comm_store_init) refers to computerframeparse.o(.data) for save_addr
    computerframeparse.o(i.comm_test_switch) refers to strncmp.o(.text) for strncmp
    computerframeparse.o(i.comm_test_switch) refers to computerframeparse.o(.data) for calibFlag
    computerframeparse.o(i.comm_write_rsp) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_test_switch) for comm_test_switch
    computerframeparse.o(i.frameParse) refers to memcmp.o(.text) for memcmp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_output_disable) for comm_output_disable
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_cali_ehco_rsp) for comm_cali_ehco_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_write_rsp) for comm_write_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_saveCaliData) for comm_saveCaliData
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_send_end_frame) for comm_send_end_frame
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_set_customPara) for comm_set_customPara
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_resume_defaultPara) for comm_resume_defaultPara
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_para_ehco_rsp) for comm_para_ehco_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_read_ver_rsp) for comm_read_ver_rsp
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_read_dev_type_rsp) for comm_read_dev_type_rsp
    computerframeparse.o(i.frameParse) refers to data_convert.o(i.hex2Float) for hex2Float
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_param_setbits) for comm_param_setbits
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(i.comm_nav_para_syn) for comm_nav_para_syn
    computerframeparse.o(i.frameParse) refers to computerframeparse.o(.bss) for caliData
    computerframeparse.o(i.miscell_handle) refers to computerframeparse.o(i.wheel_is_running) for wheel_is_running
    computerframeparse.o(i.miscell_handle) refers to computerframeparse.o(i.adj_paraSave) for adj_paraSave
    computerframeparse.o(i.navi_test_statusRd) refers to computerframeparse.o(.data) for naviTestFlg
    computerframeparse.o(i.protocol_send) refers to frame_analysis.o(i.frame_pack_and_send) for frame_pack_and_send
    protocol.o(i.ParseStrCmd) refers to strstr.o(.text) for strstr
    protocol.o(i.ParseStrCmd) refers to ins_output.o(.data) for gins912outputmode
    frame_analysis.o(i.frame_fill_ifog) refers to frame_analysis.o(i.xor_check) for xor_check
    frame_analysis.o(i.frame_init) refers to memseta.o(.text) for __aeabi_memclr
    frame_analysis.o(i.frame_init) refers to frame_analysis.o(.bss) for rs422_frame
    frame_analysis.o(i.frame_pack_and_send) refers to ddiv.o(.text) for __aeabi_ddiv
    frame_analysis.o(i.frame_pack_and_send) refers to dmul.o(.text) for __aeabi_dmul
    frame_analysis.o(i.frame_pack_and_send) refers to dfixi.o(.text) for __aeabi_d2iz
    frame_analysis.o(i.frame_pack_and_send) refers to computerframeparse.o(i.comm_axis_read) for comm_axis_read
    frame_analysis.o(i.frame_pack_and_send) refers to ins_data.o(.bss) for gnavout
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.bss) for ggpsorgdata
    frame_analysis.o(i.frame_pack_and_send) refers to instestingentry.o(.bss) for StrMiddleWare
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.data) for axisInfo
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(.constdata) for axisTab
    frame_analysis.o(i.frame_pack_and_send) refers to log.o(i.__hardfp_log) for __hardfp_log
    frame_analysis.o(i.frame_pack_and_send) refers to dfixui.o(.text) for __aeabi_d2uiz
    frame_analysis.o(i.frame_pack_and_send) refers to dflti.o(.text) for __aeabi_i2d
    frame_analysis.o(i.frame_pack_and_send) refers to computerframeparse.o(i.comm_read_currentFreq) for comm_read_currentFreq
    frame_analysis.o(i.frame_pack_and_send) refers to frame_analysis.o(i.xor_check) for xor_check
    frame_analysis.o(i.frame_pack_and_send) refers to bsp_fmc.o(i.Uart_SendMsg) for Uart_SendMsg
    instestingentry.o(i.ACC_gyroreset_r_TAFEAG16_buf) refers to instestingentry.o(i.FPGATo422_00BB_send) for FPGATo422_00BB_send
    instestingentry.o(i.AlgorithmAct) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    instestingentry.o(i.AlgorithmAct) refers to align.o(i.InertialSysAlign_Init) for InertialSysAlign_Init
    instestingentry.o(i.AlgorithmAct) refers to dynamic_align.o(i.DynamicInertialSysAlign_Init) for DynamicInertialSysAlign_Init
    instestingentry.o(i.AlgorithmAct) refers to align.o(i.InertialSysAlignCompute) for InertialSysAlignCompute
    instestingentry.o(i.AlgorithmAct) refers to align.o(i.FinishInertialSysAlign) for FinishInertialSysAlign
    instestingentry.o(i.AlgorithmAct) refers to navi.o(i.Navi_Init) for Navi_Init
    instestingentry.o(i.AlgorithmAct) refers to dynamic_align.o(i.DynamicInertialSysAlignCompute) for DynamicInertialSysAlignCompute
    instestingentry.o(i.AlgorithmAct) refers to dynamic_align.o(i.FinishDynamicInertialSysAlign) for FinishDynamicInertialSysAlign
    instestingentry.o(i.AlgorithmAct) refers to navi.o(i.DynamicNavi_Init) for DynamicNavi_Init
    instestingentry.o(i.AlgorithmAct) refers to navi.o(i.NaviCompute) for NaviCompute
    instestingentry.o(i.AlgorithmAct) refers to kalman.o(i.ComputeFn) for ComputeFn
    instestingentry.o(i.AlgorithmAct) refers to dadd.o(.text) for __aeabi_dadd
    instestingentry.o(i.AlgorithmAct) refers to kalman.o(i.KalCompute) for KalCompute
    instestingentry.o(i.AlgorithmAct) refers to kalman.o(i.KalPredict) for KalPredict
    instestingentry.o(i.AlgorithmAct) refers to kalman.o(i.ErrStore_1s) for ErrStore_1s
    instestingentry.o(i.AlgorithmAct) refers to datado.o(.data) for NaviCompute_do_count
    instestingentry.o(i.AlgorithmAct) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.AlgorithmAct) refers to instestingentry.o(.bss) for paochedata
    instestingentry.o(i.AlgorithmDo) refers to datado.o(i.Algorithm_before_otherDataDo) for Algorithm_before_otherDataDo
    instestingentry.o(i.AlgorithmDo) refers to instestingentry.o(i.INS912AlgorithmEntry) for INS912AlgorithmEntry
    instestingentry.o(i.AlgorithmDo) refers to instestingentry.o(.bss) for canin
    instestingentry.o(i.AlgorithmDo) refers to ins_data.o(.bss) for gnavout
    instestingentry.o(i.AlgorithmDo) refers to fpgad.o(.bss) for gfpgadata
    instestingentry.o(i.AnalyticCoordinateAxis) refers to f2d.o(.text) for __aeabi_f2d
    instestingentry.o(i.AnalyticCoordinateAxis) refers to ddiv.o(.text) for __aeabi_ddiv
    instestingentry.o(i.AnalyticCoordinateAxis) refers to dflti.o(.text) for __aeabi_i2d
    instestingentry.o(i.AnalyticCoordinateAxis) refers to dmul.o(.text) for __aeabi_dmul
    instestingentry.o(i.AnalyticCoordinateAxis) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.AnalyticCoordinateAxis) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.AnalyticCoordinateAxis) refers to instestingentry.o(.constdata) for SetDir
    instestingentry.o(i.AnalyticCoordinateAxis) refers to fpgad.o(.bss) for gpagedata
    instestingentry.o(i.AnalyticCoordinateAxis) refers to instestingentry.o(.bss) for gins912data
    instestingentry.o(i.FPGATo422_00BB_send) refers to memcpya.o(.text) for __aeabi_memcpy
    instestingentry.o(i.FPGATo422_00BB_send) refers to ddiv.o(.text) for __aeabi_ddiv
    instestingentry.o(i.FPGATo422_00BB_send) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.FPGATo422_00BB_send) refers to app_tool.o(i.app_accum_verify_8bit) for app_accum_verify_8bit
    instestingentry.o(i.FPGATo422_00BB_send) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    instestingentry.o(i.FPGATo422_00BB_send) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.FPGATo422_00BB_send) refers to instestingentry.o(.data) for tCnt
    instestingentry.o(i.FPGATo422_00BB_send) refers to instestingentry.o(.bss) for gfpgadataPredoSend
    instestingentry.o(i.FPGATo422_00BB_send) refers to ins_init.o(.data) for fpga_syn_count
    instestingentry.o(i.FPGATo422_00BB_send) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.FPGATo422_00BB_send) refers to main.o(.data) for g_StartUpdateFirm
    instestingentry.o(i.FPGATo422_11BB_send) refers to memcpya.o(.text) for __aeabi_memcpy
    instestingentry.o(i.FPGATo422_11BB_send) refers to app_tool.o(i.app_accum_verify_8bit) for app_accum_verify_8bit
    instestingentry.o(i.FPGATo422_11BB_send) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    instestingentry.o(i.FPGATo422_11BB_send) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.FPGATo422_11BB_send) refers to instestingentry.o(.data) for tCnt
    instestingentry.o(i.FPGATo422_11BB_send) refers to fpgad.o(.bss) for gfpgadataSend
    instestingentry.o(i.FPGATo422_11BB_send) refers to ins_init.o(.data) for fpga_syn_count
    instestingentry.o(i.FPGATo422_11BB_send) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.FPGATo422_11BB_send) refers to instestingentry.o(.bss) for r_TAFEAG8_buf
    instestingentry.o(i.GNSS_Last_TIME) refers to dadd.o(.text) for __aeabi_dadd
    instestingentry.o(i.GNSS_Last_TIME) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.GNSS_Lost_Time) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    instestingentry.o(i.GNSS_Lost_Time) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.GNSS_Valid_PPSStart) refers to kalman.o(i.SaveINSData) for SaveINSData
    instestingentry.o(i.GNSS_Valid_PPSStart) refers to kalman.o(i.ComputeFk) for ComputeFk
    instestingentry.o(i.GNSS_Valid_PPSStart) refers to kalman.o(i.ComputeXkk_1) for ComputeXkk_1
    instestingentry.o(i.GNSS_Valid_PPSStart) refers to main.o(.bss) for g_GNSSData_In_Use
    instestingentry.o(i.GNSS_Valid_PPSStart) refers to instestingentry.o(.bss) for paochedata
    instestingentry.o(i.INS912AlgorithmEntry) refers to memcpya.o(.text) for __aeabi_memcpy
    instestingentry.o(i.INS912AlgorithmEntry) refers to instestingentry.o(i.fpgadata_Predo) for fpgadata_Predo
    instestingentry.o(i.INS912AlgorithmEntry) refers to instestingentry.o(i.AlgorithmAct) for AlgorithmAct
    instestingentry.o(i.INS912AlgorithmEntry) refers to instestingentry.o(i.pnavout_set) for pnavout_set
    instestingentry.o(i.INS912AlgorithmEntry) refers to instestingentry.o(.bss) for ginputdata
    instestingentry.o(i.SDTo422_00BB_send) refers to memcpya.o(.text) for __aeabi_memcpy
    instestingentry.o(i.SDTo422_00BB_send) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.SDTo422_00BB_send) refers to app_tool.o(i.app_accum_verify_8bit) for app_accum_verify_8bit
    instestingentry.o(i.SDTo422_00BB_send) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    instestingentry.o(i.SDTo422_00BB_send) refers to instestingentry.o(.bss) for gfpgadataPredoSend
    instestingentry.o(i.SDTo422_00BB_send) refers to ins_init.o(.data) for fpga_syn_count
    instestingentry.o(i.SDTo422_00BB_send) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.Virtual_PPS_insert_5hz) refers to kalman.o(i.SaveINSData) for SaveINSData
    instestingentry.o(i.Virtual_PPS_insert_5hz) refers to kalman.o(i.ComputeFk) for ComputeFk
    instestingentry.o(i.Virtual_PPS_insert_5hz) refers to kalman.o(i.ComputeXkk_1) for ComputeXkk_1
    instestingentry.o(i.Virtual_PPS_insert_5hz) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.fpgadata_Predo_chen) for fpgadata_Predo_chen
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.GNSS_Last_TIME) for GNSS_Last_TIME
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.GNSS_Valid_PPSStart) for GNSS_Valid_PPSStart
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.Virtual_PPS_insert_5hz) for Virtual_PPS_insert_5hz
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.gnss_check_bind) for gnss_check_bind
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.GNSS_Lost_Time) for GNSS_Lost_Time
    instestingentry.o(i.fpgadata_Predo) refers to instestingentry.o(i.ACC_gyroreset_r_TAFEAG16_buf) for ACC_gyroreset_r_TAFEAG16_buf
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) for fpgadata_Predo_chen_preAlgParm_370
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) for fpgadata_Predo_chen_OutDataSet
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_algParmCash) for fpgadata_Predo_chen_algParmCash
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_gyro) for fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) for fpgadata_Predo_chen_SetAlgParm_gyro
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_acc) for fpgadata_Predo_chen_SetAlgParm_setRParm_acc
    instestingentry.o(i.fpgadata_Predo_chen) refers to instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) for fpgadata_Predo_chen_SetAlgParm_acc
    instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) refers to memseta.o(.text) for __aeabi_memclr4
    instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) refers to readpaoche.o(i.fmc2sinsraw) for fmc2sinsraw
    instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) refers to instestingentry.o(.bss) for gins912data
    instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) refers to frame_analysis.o(.bss) for ggpsorgdata
    instestingentry.o(i.fpgadata_Predo_chen_OutDataSet) refers to fpgad.o(.bss) for gpagedata
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) refers to f2d.o(.text) for __aeabi_f2d
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) refers to dadd.o(.text) for __aeabi_dsub
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) refers to dmul.o(.text) for __aeabi_dmul
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc) refers to instestingentry.o(.bss) for gins912data
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) refers to f2d.o(.text) for __aeabi_f2d
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) refers to dadd.o(.text) for __aeabi_dsub
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) refers to dmul.o(.text) for __aeabi_dmul
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro) refers to instestingentry.o(.bss) for gins912data
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_acc) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_acc) refers to instestingentry.o(.bss) for Aw
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_gyro) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_gyro) refers to instestingentry.o(.bss) for Gw
    instestingentry.o(i.fpgadata_Predo_chen_algParmCash) refers to instestingentry.o(.bss) for r_Gyro
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to instestingentry.o(i.AnalyticCoordinateAxis) for AnalyticCoordinateAxis
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to f2d.o(.text) for __aeabi_f2d
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to dmul.o(.text) for __aeabi_dmul
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to instestingentry.o(.bss) for r_fog
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370) refers to fpgad.o(.bss) for gpagedata
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to dflti.o(.text) for __aeabi_i2d
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to ddiv.o(.text) for __aeabi_ddiv
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to dmul.o(.text) for __aeabi_dmul
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to ins_init.o(.bss) for stSetPara
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to fpgad.o(.bss) for gpagedata
    instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_imu634) refers to instestingentry.o(.bss) for gins912data
    instestingentry.o(i.gnss_check_bind) refers to read_and_check_gnss_data.o(i.Read_And_Check_GNSS_Data) for Read_And_Check_GNSS_Data
    instestingentry.o(i.gnss_check_bind) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    instestingentry.o(i.gnss_check_bind) refers to navi.o(i.BindDefaultSet_by_GNSS) for BindDefaultSet_by_GNSS
    instestingentry.o(i.gnss_check_bind) refers to kalman.o(i.Kalman_StartUp) for Kalman_StartUp
    instestingentry.o(i.gnss_check_bind) refers to instestingentry.o(.bss) for paochedata
    instestingentry.o(i.gnss_check_bind) refers to main.o(.bss) for g_SysVar
    instestingentry.o(i.pnavout_set) refers to d2f.o(.text) for __aeabi_d2f
    instestingentry.o(i.pnavout_set) refers to main.o(.bss) for g_Navi
    instestingentry.o(i.test_gyc_chen_algdataTo422) refers to memcpya.o(.text) for __aeabi_memcpy
    instestingentry.o(i.test_gyc_chen_algdataTo422) refers to gd32f4xx_it.o(i.uart4sendmsg) for uart4sendmsg
    instestingentry.o(i.test_gyc_chen_algdataTo422) refers to instestingentry.o(.bss) for gins912data
    pjt_board.o(i.app_ins422rx_rst) refers to memseta.o(.text) for __aeabi_memclr
    pjt_board.o(i.app_ins422rx_rst) refers to pjt_board.o(.data) for g_ins422_rxtkn
    pjt_board.o(i.app_ins422rx_rst) refers to pjt_board.o(.bss) for g_ins422_rxbuff
    sensor_misc.o(i.app_hd6089data_covert) refers to sensor_misc.o(i.complement2original) for complement2original
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) for TAMPER_STAMP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to drv_gpio.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to drv_gpio.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to drv_gpio.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_exti.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to drv_gpio.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN0_RX0_IRQHandler) for CAN0_RX0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI5_9_IRQHandler) for EXTI5_9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) for TIMER0_UP_TIMER9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.__hardfp_floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    fmod.o(i.__hardfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem.o(.text) for _drem
    fmod.o(i.__hardfp_fmod) refers to dadd.o(.text) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to errno.o(i.__set_errno) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.__fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log.o(i.__hardfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to errno.o(i.__set_errno) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to dmul.o(.text) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to dadd.o(.text) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflti.o(.text) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to ddiv.o(.text) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(.text) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to dadd.o(.text) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflti.o(.text) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    strtod.o(i.__hardfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__hardfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.__softfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__softfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.strtod) refers to strtod.o(.text) for __strtod_int
    tan.o(i.__hardfp_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.__hardfp_tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__hardfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__hardfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.__softfp_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.__softfp_tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan.o(i.tan) refers to errno.o(i.__set_errno) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____hardfp_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.____hardfp_tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.____hardfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____hardfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____hardfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    localtime.o(.text) refers to localtime.o(.bss) for .bss
    localtime.o(.text) refers to localtime.o(.constdata) for .constdata
    mktime.o(.text) refers to localtime_i.o(.text) for _localtime
    mktime.o(.text) refers to mktime.o(.constdata) for .constdata
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to ins_init.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to ins_init.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to ins_init.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to ins_init.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to ins_init.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to ins_init.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to ins_init.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to ins_init.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to ins_init.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to ins_init.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to ins_init.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to ins_init.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to ins_init.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to ins_init.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to ins_init.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to ins_init.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to ins_init.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to ins_init.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to ins_init.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to ins_init.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to ins_init.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to ins_init.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to ins_init.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to ins_init.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to ins_init.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to ins_init.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to ins_init.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to ins_init.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to ins_init.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to ins_init.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to ins_init.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to ins_init.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to ins_init.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to ins_init.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to ins_init.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to ins_init.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to ins_init.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to ins_init.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to ins_init.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to ins_init.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to ins_init.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to ins_init.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to ins_init.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to ins_init.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfixi.o(.text) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to dadd.o(.text) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to dmul.o(.text) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflti.o(.text) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(.text) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to fabs.o(i.fabs) for fabs
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to dadd.o(.text) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflti.o(.text) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to fabs.o(i.fabs) for fabs
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    localtime_w.o(.text) refers to localtime_i.o(.text) for _localtime
    localtime_w.o(.text) refers to localtime_w.o(.bss) for .bss
    localtime_i.o(.text) refers to localtime_i.o(.constdata) for .constdata
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    drem.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp

