/***********************************************************************************************************************************/
/*ALIGN.C                                                                                                                        */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                                          */
/*                                                                                                                                 */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*                                                                                                                               */
/*                                                                                                                                   */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include "DATASTRUCT.h"
#include<string.h>
#include "FUNCTION.h"




/****************************************************************************************************/
/*DynamicInertialSysAlign_Init                                                                    */
/*                                                                     */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                                */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void DynamicInertialSysAlign_Init(DPARA r_Pos[3], DPARA Vn[3],p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)
{
    //register IPARA i = 0;

    memset(lp_DynamicInertialSysAlign, 0, sizeof(DynamicInertialSysAlign));   //g_Navi

    //
    lp_DynamicInertialSysAlign -> r_Lati = r_Pos[0];  //,rad
    lp_DynamicInertialSysAlign -> r_Logi = r_Pos[1];  //rad
    lp_DynamicInertialSysAlign -> Height = r_Pos[2];  //m
    //
    lp_DynamicInertialSysAlign -> Vn[0] = Vn[0];
    lp_DynamicInertialSysAlign -> Vn[1] = Vn[1];
    lp_DynamicInertialSysAlign -> Vn[2] = Vn[2];

    //Qbib0Cbib0Cib0b
    lp_DynamicInertialSysAlign -> Qbib0[0] = 1.0;
    lp_DynamicInertialSysAlign -> Qbib0[1] = 0.0;
    lp_DynamicInertialSysAlign -> Qbib0[2] = 0.0;
    lp_DynamicInertialSysAlign -> Qbib0[3] = 0.0;

    //t1
    lp_DynamicInertialSysAlign -> T1 = TIME_FAST_ALIGN / 2.0;
    //T1NO
    lp_DynamicInertialSysAlign -> isT1Record = NO;
    //Cen
    //ComputeCen(lp_DynamicInertialSysAlign -> r_Lati,lp_DynamicInertialSysAlign -> r_Logi,lp_DynamicInertialSysAlign -> Cen);
    lp_DynamicInertialSysAlign->isAlignInit = YES;
}




/*****************************************************************************************************************************
//  UpdateAlignPosAndVn
//  
//  
//  
//  
//-----------------------------------------------------------------------------------------------------------------------------
//     
///
//-----------------------------------------------------------------------------------------------------------------------------
//     
///
*****************************************************************************************************************************/
void UpdateAlignPosAndVn(DPARA r_Pos[3], DPARA Vn[3], p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)
{

    //
    lp_DynamicInertialSysAlign->r_Lati = r_Pos[0];  //,rad
    lp_DynamicInertialSysAlign->r_Logi = r_Pos[1];  //rad
    lp_DynamicInertialSysAlign->Height = r_Pos[2];  //m
    //

    lp_DynamicInertialSysAlign->LastVn[0] = lp_DynamicInertialSysAlign->Vn[0];
    lp_DynamicInertialSysAlign->LastVn[1] = lp_DynamicInertialSysAlign->Vn[1];
    lp_DynamicInertialSysAlign->LastVn[2] = lp_DynamicInertialSysAlign->Vn[2];

    lp_DynamicInertialSysAlign->Vn[0] = Vn[0];
    lp_DynamicInertialSysAlign->Vn[1] = Vn[1];
    lp_DynamicInertialSysAlign->Vn[2] = Vn[2];
}





/****************************************************************************************************/
/*DynamicInertialSysAlignCompute                                                                  */
/*                                                          */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                                */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void DynamicInertialSysAlignCompute(DPARA const Gyro[3],DPARA const LastGyro[3],DPARA const Acc[3],DPARA const LastAcc[3],DPARA r_Pos[3], DPARA Vn[3], p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)
{
    register IPARA i;
    //BOOL isAlignNormal;
    //
    //
    for (i = 0 ; i < 3; i++)
    {
        lp_DynamicInertialSysAlign -> r_Wibb[0][i] = LastGyro[i];
        lp_DynamicInertialSysAlign -> r_Wibb[1][i] = Gyro[i];
        lp_DynamicInertialSysAlign -> Fibb[i] = Acc[i];
    }
    //

    UpdateAlignPosAndVn(r_Pos,Vn,lp_DynamicInertialSysAlign);
    ComputeCen(lp_DynamicInertialSysAlign->r_Lati, lp_DynamicInertialSysAlign->r_Logi, lp_DynamicInertialSysAlign->Cen);
    ComputeG(lp_DynamicInertialSysAlign->r_Lati, lp_DynamicInertialSysAlign->Height, &lp_DynamicInertialSysAlign->Gn);//navi.cComputeG
    ComputeWien(lp_DynamicInertialSysAlign ->r_Lati, lp_DynamicInertialSysAlign -> r_Wien);//
    ComputeRmRn(lp_DynamicInertialSysAlign -> r_Lati,lp_DynamicInertialSysAlign -> Height, &lp_DynamicInertialSysAlign->Rm, &lp_DynamicInertialSysAlign->Rn, &lp_DynamicInertialSysAlign->invRm, &lp_DynamicInertialSysAlign->invRn);//
    ComputeWenn(lp_DynamicInertialSysAlign->r_Lati, lp_DynamicInertialSysAlign->Vn, lp_DynamicInertialSysAlign->invRm, lp_DynamicInertialSysAlign->invRn, lp_DynamicInertialSysAlign->r_Wenn);//
    ComputeSi(lp_DynamicInertialSysAlign);


    ComputeCie(lp_DynamicInertialSysAlign -> AlignTime , lp_DynamicInertialSysAlign -> Cie);

    ComputeDelSenbb(lp_DynamicInertialSysAlign -> r_Wibb,lp_DynamicInertialSysAlign -> r_DelSenbb_1,lp_DynamicInertialSysAlign -> r_DelSenbb_2,lp_DynamicInertialSysAlign -> r_DelSenbb);

    ComputeQ(lp_DynamicInertialSysAlign -> r_DelSenbb, lp_DynamicInertialSysAlign -> Qbib0);//navi.ccomputeQQbib0

    QToCnb(lp_DynamicInertialSysAlign -> Qbib0,lp_DynamicInertialSysAlign -> Cib0b);//navi.cQToCnbCib0b

    Mat_Tr(lp_DynamicInertialSysAlign -> Cib0b, lp_DynamicInertialSysAlign -> Cbib0, 3, 3);//Cbib0


    ComputeSib0(lp_DynamicInertialSysAlign);

    lp_DynamicInertialSysAlign -> AlignTime += TIME_NAVI;
    lp_DynamicInertialSysAlign -> AlignCount ++;
    //t1SiSib0
    if((lp_DynamicInertialSysAlign -> AlignTime >= lp_DynamicInertialSysAlign -> T1) && (lp_DynamicInertialSysAlign -> isT1Record == NO))
    {
        for(i = 0 ; i < 3 ; i++)
        {
            lp_DynamicInertialSysAlign -> Si_T1[i] = lp_DynamicInertialSysAlign -> Si[i];
            lp_DynamicInertialSysAlign -> Sib0_T1[i] = lp_DynamicInertialSysAlign -> Sib0[i];
        }
        lp_DynamicInertialSysAlign -> isT1Record = YES;
    }
    //return isAlignNormal;
}




/****************************************************************************************************/
/*ComputeSi                                                                                       */
/*iSi                                                                   */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                                */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void ComputeSi(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)
{
    IPARA i;
    MATR Cei[9];
    MATR Cne[9];
    ACCELER Fn[3];
    ACCELER Fe[3];
    ACCELER Fi[3];
    ACCELER Coriolis_Acc[3];
    ANGRATE r_temp[3];
    VEL Vn_Error[3];
    //Fn[0] = 0.0;
    //Fn[1] = lp_DynamicInertialSysAlign -> Gn;
    //Fn[2] = 0.0;//
    for(i = 0;i<3;i++)
    {
        r_temp[i] = 2 * lp_DynamicInertialSysAlign-> r_Wien[i] + lp_DynamicInertialSysAlign -> r_Wenn[i];
    }
    Vec_Cross(r_temp, lp_DynamicInertialSysAlign->Vn, Coriolis_Acc);
    for (i = 0; i < 3; i++)
    {
        Vn_Error[i] = (lp_DynamicInertialSysAlign->Vn[i] - lp_DynamicInertialSysAlign->LastVn[i]) / TIME_NAVI;
    }
    for (i = 0; i < 3; i++)
    {
        Fn[i] = Vn_Error[i] + Coriolis_Acc[i];
        if (i == 1)
        {
            Fn[i] += lp_DynamicInertialSysAlign->Gn;
        }
    }
    Mat_Tr(lp_DynamicInertialSysAlign -> Cie, Cei, 3, 3);//
    Mat_Tr(lp_DynamicInertialSysAlign -> Cen, Cne, 3, 3);//
    Mat_Mul(Cne, Fn, Fe, 3, 3, 1);
    Mat_Mul(Cei, Fe, Fi, 3, 3, 1);

    for(i = 0; i < 3; i++)
    {
        lp_DynamicInertialSysAlign -> Vi[i] += Fi[i] * TIME_NAVI;
        lp_DynamicInertialSysAlign-> Si[i] += lp_DynamicInertialSysAlign -> Vi[i] * TIME_NAVI;
    }
}





/****************************************************************************************************/
/*ComputeSib0                                                                                     */
/*iSib0                                                                */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                                */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void ComputeSib0(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)
{
    IPARA i;

    ACCELER Fib0[3];

    Mat_Mul(lp_DynamicInertialSysAlign -> Cbib0, lp_DynamicInertialSysAlign -> Fibb, Fib0, 3, 3, 1);

    for(i = 0; i < 3; i++)
    {
        lp_DynamicInertialSysAlign ->Vib0[i] += Fib0[i] * TIME_NAVI;
        lp_DynamicInertialSysAlign->Sib0[i] += lp_DynamicInertialSysAlign->Vib0[i] * TIME_NAVI;
    } 
}




/****************************************************************************************************/
/*FinishDynamicInertialSysAlign                                                                   */
/*iVib0                                                                    */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                                */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void FinishDynamicInertialSysAlign(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)         
{
    MATR Cbi[9];
    MATR Cbe[9];
    MATR Cbn[9];

    ComputeCib0i(lp_DynamicInertialSysAlign -> Si,lp_DynamicInertialSysAlign -> Sib0,lp_DynamicInertialSysAlign -> Si_T1,lp_DynamicInertialSysAlign -> Sib0_T1,lp_DynamicInertialSysAlign -> Cib0i);
    //Cbn
    Mat_Mul(lp_DynamicInertialSysAlign -> Cib0i, lp_DynamicInertialSysAlign -> Cbib0, Cbi,3, 3, 3);  
    Mat_Mul(lp_DynamicInertialSysAlign -> Cie, Cbi, Cbe,3, 3, 3); 
    Mat_Mul(lp_DynamicInertialSysAlign -> Cen, Cbe, Cbn,3, 3, 3);
    //Cnb
    Mat_Tr(Cbn, lp_DynamicInertialSysAlign -> AlignCnb, 3, 3);
    CnbToAtti(lp_DynamicInertialSysAlign -> AlignCnb, lp_DynamicInertialSysAlign -> r_AlignAtti);//navi.cCnbToAtti
    AttiToCnb(lp_DynamicInertialSysAlign -> r_AlignAtti, lp_DynamicInertialSysAlign -> AlignCnb);//Cnbnavi.cAttiToCnb
    CnbToQ(lp_DynamicInertialSysAlign -> AlignCnb,lp_DynamicInertialSysAlign -> AlignQ);//navi.cCnbToQ
    lp_DynamicInertialSysAlign -> isAlign_Finish = YES;
}




/*****************************************************************************************************************************
//  VGDynamicInertialSysAlign
//  
//  
//  
//  
//-----------------------------------------------------------------------------------------------------------------------------
//     
///
//-----------------------------------------------------------------------------------------------------------------------------
//     
///
*****************************************************************************************************************************/
void VGDynamicInertialSysAlign(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign)         
{
    MATR Cbi[9];
    MATR Cbe[9];
    MATR Cbn[9];

    ComputeCib0i(lp_DynamicInertialSysAlign -> Si,lp_DynamicInertialSysAlign -> Sib0,lp_DynamicInertialSysAlign -> Si_T1,lp_DynamicInertialSysAlign -> Sib0_T1,lp_DynamicInertialSysAlign -> Cib0i);
    //Cbn
    Mat_Mul(lp_DynamicInertialSysAlign -> Cib0i, lp_DynamicInertialSysAlign -> Cbib0, Cbi,3, 3, 3);  
    Mat_Mul(lp_DynamicInertialSysAlign -> Cie, Cbi, Cbe,3, 3, 3); 
    Mat_Mul(lp_DynamicInertialSysAlign -> Cen, Cbe, Cbn,3, 3, 3);
    //Cnb
    Mat_Tr(Cbn, lp_DynamicInertialSysAlign -> AlignCnb, 3, 3);
    CnbToAtti(lp_DynamicInertialSysAlign -> AlignCnb, lp_DynamicInertialSysAlign -> r_AlignAtti);//navi.cCnbToAtti
    AttiToCnb(lp_DynamicInertialSysAlign -> r_AlignAtti, lp_DynamicInertialSysAlign -> AlignCnb);//Cnbnavi.cAttiToCnb
    CnbToQ(lp_DynamicInertialSysAlign -> AlignCnb,lp_DynamicInertialSysAlign -> AlignQ);//navi.cCnbToQ
}



