.\objects\private_math.o: ..\INAV\private_math.c
.\objects\private_math.o: ..\Source\inc\appmain.h
.\objects\private_math.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\private_math.o: ..\Library\CMSIS\core_cm4.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\private_math.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\private_math.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\private_math.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\private_math.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\private_math.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx_libopt.h
.\objects\private_math.o: ..\Protocol\RTE_Components.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\private_math.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\private_math.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\private_math.o: ..\Source\inc\systick.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\private_math.o: ..\Source\inc\main.h
.\objects\private_math.o: ..\bsp\inc\bsp_gpio.h
.\objects\private_math.o: ..\bsp\inc\bsp_flash.h
.\objects\private_math.o: ..\Source\inc\INS_Data.h
.\objects\private_math.o: ..\Library\CMSIS\arm_math.h
.\objects\private_math.o: ..\Library\CMSIS\core_cm4.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\private_math.o: ..\Source\inc\gnss.h
.\objects\private_math.o: ..\Common\inc\data_convert.h
.\objects\private_math.o: ..\Source\inc\tlhtype.h
.\objects\private_math.o: ..\Source\inc\can_data.h
.\objects\private_math.o: ..\Source\inc\imu_data.h
.\objects\private_math.o: ..\Source\inc\INS_sys.h
.\objects\private_math.o: ..\Source\inc\appmain.h
.\objects\private_math.o: ..\Source\inc\INS912AlgorithmEntry.h
.\objects\private_math.o: ..\Source\inc\deviceconfig.h
.\objects\private_math.o: ..\Protocol\frame_analysis.h
.\objects\private_math.o: ..\Protocol\protocol.h
.\objects\private_math.o: ..\Protocol\config.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\private_math.o: ..\Source\inc\board.h
.\objects\private_math.o: ..\Protocol\frame_analysis.h
.\objects\private_math.o: ..\Protocol\insdef.h
.\objects\private_math.o: ..\bsp\inc\bsp_sys.h
.\objects\private_math.o: ..\Library\CMSIS\core_cm4.h
.\objects\private_math.o: ..\bsp\inc\bsp_rtc.h
.\objects\private_math.o: ..\Source\inc\Time_unify.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\private_math.o: ..\bsp\inc\bsp_can.h
.\objects\private_math.o: ..\bsp\inc\bsp_fwdgt.h
.\objects\private_math.o: ..\bsp\inc\bsp_fmc.h
.\objects\private_math.o: ..\bsp\inc\bsp_exti.h
.\objects\private_math.o: ..\bsp\inc\bmp280.h
.\objects\private_math.o: ..\bsp\inc\bmp2.h
.\objects\private_math.o: ..\bsp\inc\bmp2_defs.h
.\objects\private_math.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\private_math.o: ..\bsp\inc\common.h
.\objects\private_math.o: ..\bsp\inc\logger.h
.\objects\private_math.o: ..\bsp\inc\FILE_SYS.h
