/**
 * Ciastkolog.pl (https://github.com/ciastkolog)
 * 
*/
/**
 * The MIT License (MIT)
 * Copyright (c) 2016 sheinz (https://github.com/sheinz)
 */
#ifndef __BMP280_H__
#define __BMP280_H__

#include "bsp_sys.h"
#include <stdint.h>
#include <stdbool.h>

#if CHIP_USED == USE_CHIP_GD32
#include "gd32f4xx.h"
#else
#include "stm32f0xx_hal.h"
#endif

#include "bmp2.h"
#include "common.h"

extern struct bmp2_dev bmpDev;
extern struct bmp2_config bmpCfg;
extern struct bmp2_data BMP280Data;

uint32_t bmp280_init(void);
int8_t bmp280_get_data(uint32_t period, struct bmp2_dev *dev,struct bmp2_data* comp_data);

#endif  // __BMP280_H__
