.\objects\pjt_board.o: ..\Source\Edwoy\pjt_board.c
.\objects\pjt_board.o: ..\Source\Edwoy\pjt_glb_head.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\pjt_board.o: ..\Source\inc\deviceconfig.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\pjt_board.o: ..\Source\Edwoy\types.h
.\objects\pjt_board.o: ..\Source\Edwoy\convert.h
.\objects\pjt_board.o: ..\Protocol\config.h
.\objects\pjt_board.o: ..\Source\Edwoy\pjt_board.h
.\objects\pjt_board.o: ..\Source\inc\systick.h
.\objects\pjt_board.o: ..\Source\Edwoy\app_tool.h
.\objects\pjt_board.o: ..\Source\inc\main.h
.\objects\pjt_board.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\pjt_board.o: ..\Library\CMSIS\core_cm4.h
.\objects\pjt_board.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\pjt_board.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\pjt_board.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\pjt_board.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\pjt_board.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx_libopt.h
.\objects\pjt_board.o: ..\Protocol\RTE_Components.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\pjt_board.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\pjt_board.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\pjt_board.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
