.\objects\gdwatch.o: ..\Source\src\gdwatch.c
.\objects\gdwatch.o: ..\Source\src\gdtypedefine.h
.\objects\gdwatch.o: ..\Source\src\appdefine.h
.\objects\gdwatch.o: ..\Source\inc\fpgad.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\gdwatch.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\gdwatch.o: ..\Protocol\frame_analysis.h
.\objects\gdwatch.o: ..\Source\inc\INS_Data.h
.\objects\gdwatch.o: ..\Library\CMSIS\arm_math.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\gdwatch.o: ..\Source\inc\gnss.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\gdwatch.o: ..\Common\inc\data_convert.h
.\objects\gdwatch.o: ..\Protocol\frame_analysis.h
.\objects\gdwatch.o: ..\Source\inc\tlhtype.h
.\objects\gdwatch.o: ..\Source\inc\can_data.h
.\objects\gdwatch.o: ..\Source\inc\imu_data.h
.\objects\gdwatch.o: ..\Source\inc\INS_sys.h
.\objects\gdwatch.o: ..\NAV\nav_type.h
.\objects\gdwatch.o: ..\NAV\nav_const.h
.\objects\gdwatch.o: ..\NAV\algorithm.h
.\objects\gdwatch.o: ..\Protocol\insdef.h
