/*
 * File      : drv_spi.h
 * This file is part of RT-Thread RTOS
 * COPYRIGHT (C) 2015, RT-Thread Development Team
 *
 * The license and distribution terms for this file may be
 * found in the file LICENSE in this distribution or at
 * http://www.rt-thread.org/license/LICENSE
 *
 * Change Logs:
 * Date           Author            Notes
 * 2017-10-20     ZYH            the first version
 * 2018-04-23     misonyo        port to gd32f4xx
 * 2022-05-25     zwb        	   cut to gd32f4xx
 * 2024-12-XX     AI Assistant   create drv_spi for INS370M project
 */

#ifndef DRV_SPI_H__
#define DRV_SPI_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"
#include "insdef.h"

/* SPI device configuration macros */
#define BSP_USING_SPI

#ifdef BSP_USING_SPI
/* Enable specific SPI devices as needed */
#define BSP_USING_SPI0
#define BSP_USING_SPI1
#define BSP_USING_SPI2
#define BSP_USING_SPI3
#define BSP_USING_SPI4
#define BSP_USING_SPI5
#endif

/* SPI device indices */
#define SPI_DEVICE_0    0
#define SPI_DEVICE_1    1
#define SPI_DEVICE_2    2
#define SPI_DEVICE_3    3
#define SPI_DEVICE_4    4
#define SPI_DEVICE_5    5

/* SPI CS control states */
#define SPI_CS_LOW      0
#define SPI_CS_HIGH     1

/* Forward declaration */
struct gd32_spi_device;

/* Function prototypes */

/**
 * @brief Initialize all configured SPI devices
 * @return 0 on success, -1 on error
 */
int gd32_spi_device_init(void);

/**
 * @brief Get SPI device by index
 * @param index: SPI device index (0-5)
 * @return pointer to SPI device structure, NULL if not found
 */
struct gd32_spi_device *gd32_spi_get_device(int index);

/**
 * @brief SPI transfer function for external use
 * @param index: SPI device index (0-5)
 * @param tx_data: data to transmit (8-bit)
 * @return received data (8-bit), 0xFF on error
 */
uint8_t gd32_spi_transfer_byte(int index, uint8_t tx_data);

/**
 * @brief Set SPI CS pin state
 * @param index: SPI device index (0-5)
 * @param state: SPI_CS_LOW (0) for low, SPI_CS_HIGH (1) for high
 */
void gd32_spi_cs_control(int index, uint8_t state);

/**
 * @brief SPI multi-byte transfer function
 * @param index: SPI device index (0-5)
 * @param tx_buffer: pointer to transmit buffer
 * @param rx_buffer: pointer to receive buffer (can be NULL)
 * @param length: number of bytes to transfer
 * @return 0 on success, -1 on error
 */
int gd32_spi_transfer_buffer(int index, uint8_t *tx_buffer, uint8_t *rx_buffer, uint32_t length);

/**
 * @brief SPI write only function
 * @param index: SPI device index (0-5)
 * @param data: data to write
 */
void gd32_spi_write_byte(int index, uint8_t data);

/**
 * @brief SPI read only function
 * @param index: SPI device index (0-5)
 * @return received data
 */
uint8_t gd32_spi_read_byte(int index);

/* Compatibility macros for existing code */
#define spi_init_device(index)          gd32_spi_device_init()
#define spi_transfer(index, data)       gd32_spi_transfer_byte(index, data)
#define spi_cs_low(index)               gd32_spi_cs_control(index, SPI_CS_LOW)
#define spi_cs_high(index)              gd32_spi_cs_control(index, SPI_CS_HIGH)
#define spi_write(index, data)          gd32_spi_write_byte(index, data)
#define spi_read(index)                 gd32_spi_read_byte(index)

#ifdef __cplusplus
}
#endif

#endif /* DRV_SPI_H__ */
