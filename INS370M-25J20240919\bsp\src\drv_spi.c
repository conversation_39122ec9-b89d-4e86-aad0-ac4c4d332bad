/*
 * File      : drv_spi.c
 * This file is part of RT-Thread RTOS
 * COPYRIGHT (C) 2015, RT-Thread Development Team
 *
 * The license and distribution terms for this file may be
 * found in the file LICENSE in this distribution or at
 * http://www.rt-thread.org/license/LICENSE
 *
 * Change Logs:
 * Date           Author            Notes
 * 2017-10-20     ZYH            the first version
 * 2018-04-23     misonyo        port to gd32f4xx
 * 2022-05-25     zwb        	   cut to gd32f4xx
 * 2024-12-XX     AI Assistant   create drv_spi for INS370M project
 */

#include "drv_spi.h"

#ifdef BSP_USING_SPI

/* SPI device structure */
struct gd32_spi_device
{
    uint32_t spi_periph;
    rcu_periph_enum spi_clk;
    rcu_periph_enum gpio_clk;
    uint32_t gpio_port;
    uint32_t sck_pin;
    uint32_t miso_pin;
    uint32_t mosi_pin;
    uint32_t cs_pin;
    uint8_t af;
};

/* SPI device configuration */
static struct gd32_spi_device spi_devices[] = {
#ifdef BSP_USING_SPI0
    {
        .spi_periph = SPI0,
        .spi_clk = RCU_SPI0,
        .gpio_clk = RCU_GPIOA,
        .gpio_port = GPIOA,
        .sck_pin = GPIO_PIN_5,
        .miso_pin = GPIO_PIN_6,
        .mosi_pin = GPIO_PIN_7,
        .cs_pin = GPIO_PIN_4,
        .af = GPIO_AF_5,
    },
#endif
#ifdef BSP_USING_SPI1
    {
        .spi_periph = SPI1,
        .spi_clk = RCU_SPI1,
        .gpio_clk = RCU_GPIOB,
        .gpio_port = GPIOB,
        .sck_pin = GPIO_PIN_13,
        .miso_pin = GPIO_PIN_14,
        .mosi_pin = GPIO_PIN_15,
        .cs_pin = GPIO_PIN_12,
        .af = GPIO_AF_5,
    },
#endif
#ifdef BSP_USING_SPI2
    {
        .spi_periph = SPI2,
        .spi_clk = RCU_SPI2,
        .gpio_clk = RCU_GPIOC,
        .gpio_port = GPIOC,
        .sck_pin = GPIO_PIN_10,
        .miso_pin = GPIO_PIN_11,
        .mosi_pin = GPIO_PIN_12,
        .cs_pin = GPIO_PIN_9,
        .af = GPIO_AF_6,
    },
#endif
#ifdef BSP_USING_SPI3
    {
        .spi_periph = SPI3,
        .spi_clk = RCU_SPI3,
        .gpio_clk = RCU_GPIOE,
        .gpio_port = GPIOE,
        .sck_pin = GPIO_PIN_12,
        .miso_pin = GPIO_PIN_13,
        .mosi_pin = GPIO_PIN_14,
        .cs_pin = GPIO_PIN_11,
        .af = GPIO_AF_5,
    },
#endif
#ifdef BSP_USING_SPI4
    {
        .spi_periph = SPI4,
        .spi_clk = RCU_SPI4,
        .gpio_clk = RCU_GPIOF,
        .gpio_port = GPIOF,
        .sck_pin = GPIO_PIN_7,
        .miso_pin = GPIO_PIN_8,
        .mosi_pin = GPIO_PIN_9,
        .cs_pin = GPIO_PIN_6,
        .af = GPIO_AF_5,
    },
#endif
#ifdef BSP_USING_SPI5
    {
        .spi_periph = SPI5,
        .spi_clk = RCU_SPI5,
        .gpio_clk = RCU_GPIOG,
        .gpio_port = GPIOG,
        .sck_pin = GPIO_PIN_13,
        .miso_pin = GPIO_PIN_12,
        .mosi_pin = GPIO_PIN_14,
        .cs_pin = GPIO_PIN_8,
        .af = GPIO_AF_5,
    },
#endif
};

/**
 * @brief Initialize SPI device
 * @param spi_device: pointer to SPI device structure
 * @return 0 on success, -1 on error
 */
static int gd32_spi_init(struct gd32_spi_device *spi_device)
{
    spi_parameter_struct spi_init_struct;
    
    /* Enable SPI and GPIO clocks */
    rcu_periph_clock_enable(spi_device->spi_clk);
    rcu_periph_clock_enable(spi_device->gpio_clk);
    
    /* Configure GPIO pins */
    gpio_af_set(spi_device->gpio_port, spi_device->af, 
                spi_device->sck_pin | spi_device->miso_pin | spi_device->mosi_pin);
    
    /* Configure SCK, MISO, MOSI pins */
    gpio_mode_set(spi_device->gpio_port, GPIO_MODE_AF, GPIO_PUPD_NONE,
                  spi_device->sck_pin | spi_device->miso_pin | spi_device->mosi_pin);
    gpio_output_options_set(spi_device->gpio_port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,
                           spi_device->sck_pin | spi_device->miso_pin | spi_device->mosi_pin);
    
    /* Configure CS pin as GPIO output */
    gpio_mode_set(spi_device->gpio_port, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, spi_device->cs_pin);
    gpio_output_options_set(spi_device->gpio_port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, spi_device->cs_pin);
    
    /* Set CS high initially */
    gpio_bit_set(spi_device->gpio_port, spi_device->cs_pin);
    
    /* Configure SPI parameters */
    spi_init_struct.device_mode = SPI_MASTER;
    spi_init_struct.trans_mode = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.frame_size = SPI_FRAMESIZE_8BIT;
    spi_init_struct.endian = SPI_ENDIAN_MSB;
    spi_init_struct.nss = SPI_NSS_SOFT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_LOW_PH_1EDGE;
    spi_init_struct.prescale = SPI_PSC_8;
    
    /* Initialize SPI */
    spi_init(spi_device->spi_periph, &spi_init_struct);
    
    /* Enable SPI */
    spi_enable(spi_device->spi_periph);
    
    return 0;
}

/**
 * @brief SPI transmit and receive data
 * @param spi_device: pointer to SPI device structure
 * @param tx_data: data to transmit
 * @return received data
 */
static uint8_t gd32_spi_transfer(struct gd32_spi_device *spi_device, uint8_t tx_data)
{
    /* Wait for transmit buffer empty */
    while(RESET == spi_i2s_flag_get(spi_device->spi_periph, SPI_FLAG_TBE));
    
    /* Send data */
    spi_i2s_data_transmit(spi_device->spi_periph, tx_data);
    
    /* Wait for receive buffer not empty */
    while(RESET == spi_i2s_flag_get(spi_device->spi_periph, SPI_FLAG_RBNE));
    
    /* Return received data */
    return spi_i2s_data_receive(spi_device->spi_periph);
}

/**
 * @brief Set CS pin state
 * @param spi_device: pointer to SPI device structure
 * @param state: 0 for low, 1 for high
 */
static void gd32_spi_cs_set(struct gd32_spi_device *spi_device, uint8_t state)
{
    if (state) {
        gpio_bit_set(spi_device->gpio_port, spi_device->cs_pin);
    } else {
        gpio_bit_reset(spi_device->gpio_port, spi_device->cs_pin);
    }
}

/**
 * @brief Initialize all configured SPI devices
 * @return 0 on success
 */
int gd32_spi_device_init(void)
{
    int i;
    int result = 0;
    
    for (i = 0; i < sizeof(spi_devices) / sizeof(spi_devices[0]); i++) {
        result = gd32_spi_init(&spi_devices[i]);
        if (result != 0) {
            break;
        }
    }
    
    return result;
}

/**
 * @brief Get SPI device by index
 * @param index: SPI device index
 * @return pointer to SPI device structure, NULL if not found
 */
struct gd32_spi_device *gd32_spi_get_device(int index)
{
    if (index >= 0 && index < sizeof(spi_devices) / sizeof(spi_devices[0])) {
        return &spi_devices[index];
    }
    return NULL;
}

/**
 * @brief SPI transfer function for external use
 * @param index: SPI device index
 * @param tx_data: data to transmit
 * @return received data, 0xFF on error
 */
uint8_t gd32_spi_transfer_byte(int index, uint8_t tx_data)
{
    struct gd32_spi_device *device = gd32_spi_get_device(index);
    if (device) {
        return gd32_spi_transfer(device, tx_data);
    }
    return 0xFF;
}

/**
 * @brief Set SPI CS pin state
 * @param index: SPI device index
 * @param state: 0 for low, 1 for high
 */
void gd32_spi_cs_control(int index, uint8_t state)
{
    struct gd32_spi_device *device = gd32_spi_get_device(index);
    if (device) {
        gd32_spi_cs_set(device, state);
    }
}

/**
 * @brief SPI multi-byte transfer function
 * @param index: SPI device index
 * @param tx_buffer: pointer to transmit buffer
 * @param rx_buffer: pointer to receive buffer (can be NULL)
 * @param length: number of bytes to transfer
 * @return 0 on success, -1 on error
 */
int gd32_spi_transfer_buffer(int index, uint8_t *tx_buffer, uint8_t *rx_buffer, uint32_t length)
{
    struct gd32_spi_device *device = gd32_spi_get_device(index);
    uint32_t i;

    if (!device || !tx_buffer || length == 0) {
        return -1;
    }

    for (i = 0; i < length; i++) {
        uint8_t rx_data = gd32_spi_transfer(device, tx_buffer[i]);
        if (rx_buffer) {
            rx_buffer[i] = rx_data;
        }
    }

    return 0;
}

/**
 * @brief SPI write only function
 * @param index: SPI device index
 * @param data: data to write
 */
void gd32_spi_write_byte(int index, uint8_t data)
{
    gd32_spi_transfer_byte(index, data);
}

/**
 * @brief SPI read only function
 * @param index: SPI device index
 * @return received data
 */
uint8_t gd32_spi_read_byte(int index)
{
    return gd32_spi_transfer_byte(index, 0xFF);
}

#endif /* BSP_USING_SPI */
