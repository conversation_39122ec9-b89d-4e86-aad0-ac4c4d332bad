.\objects\navi.o: ..\INAV\navi.c
.\objects\navi.o: ..\Source\inc\appmain.h
.\objects\navi.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\navi.o: ..\Library\CMSIS\core_cm4.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\navi.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\navi.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\navi.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\navi.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\navi.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx_libopt.h
.\objects\navi.o: ..\Protocol\RTE_Components.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\navi.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\navi.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\navi.o: ..\Source\inc\systick.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\navi.o: ..\Source\inc\main.h
.\objects\navi.o: ..\bsp\inc\bsp_gpio.h
.\objects\navi.o: ..\bsp\inc\bsp_flash.h
.\objects\navi.o: ..\Source\inc\INS_Data.h
.\objects\navi.o: ..\Library\CMSIS\arm_math.h
.\objects\navi.o: ..\Library\CMSIS\core_cm4.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\navi.o: ..\Source\inc\gnss.h
.\objects\navi.o: ..\Common\inc\data_convert.h
.\objects\navi.o: ..\Source\inc\tlhtype.h
.\objects\navi.o: ..\Source\inc\can_data.h
.\objects\navi.o: ..\Source\inc\imu_data.h
.\objects\navi.o: ..\Source\inc\INS_sys.h
.\objects\navi.o: ..\Source\inc\appmain.h
.\objects\navi.o: ..\Source\inc\INS912AlgorithmEntry.h
.\objects\navi.o: ..\Source\inc\deviceconfig.h
.\objects\navi.o: ..\Protocol\frame_analysis.h
.\objects\navi.o: ..\Protocol\protocol.h
.\objects\navi.o: ..\Protocol\config.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\navi.o: ..\Source\inc\board.h
.\objects\navi.o: ..\Protocol\frame_analysis.h
.\objects\navi.o: ..\Protocol\insdef.h
.\objects\navi.o: ..\bsp\inc\bsp_sys.h
.\objects\navi.o: ..\Library\CMSIS\core_cm4.h
.\objects\navi.o: ..\bsp\inc\bsp_rtc.h
.\objects\navi.o: ..\Source\inc\Time_unify.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\navi.o: ..\bsp\inc\bsp_can.h
.\objects\navi.o: ..\bsp\inc\bsp_fwdgt.h
.\objects\navi.o: ..\bsp\inc\CH395SPI.H
.\objects\navi.o: ..\bsp\inc\CH395INC.H
.\objects\navi.o: ..\bsp\inc\CH395CMD.H
.\objects\navi.o: ..\bsp\inc\bsp_fmc.h
.\objects\navi.o: ..\bsp\inc\bsp_exti.h
.\objects\navi.o: ..\bsp\inc\bmp280.h
.\objects\navi.o: ..\bsp\inc\bmp2.h
.\objects\navi.o: ..\bsp\inc\bmp2_defs.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\navi.o: ..\bsp\inc\common.h
.\objects\navi.o: ..\bsp\inc\CH378_HAL.h
.\objects\navi.o: ..\bsp\inc\CH378INC.H
.\objects\navi.o: ..\bsp\inc\logger.h
.\objects\navi.o: ..\bsp\inc\CH378_HAL.h
.\objects\navi.o: ..\bsp\inc\FILE_SYS.h
.\objects\navi.o: ..\bsp\inc\CH378_HAL.H
.\objects\navi.o: ..\bsp\inc\bsp_tim.h
.\objects\navi.o: ..\Source\inc\fpgad.h
.\objects\navi.o: ..\Source\inc\appdefine.h
.\objects\navi.o: ..\Protocol\computerFrameParse.h
.\objects\navi.o: ..\Source\inc\gdtypedefine.h
.\objects\navi.o: ..\Protocol\InsTestingEntry.h
.\objects\navi.o: ..\Source\inc\gdtypedefine.h
.\objects\navi.o: ..\Source\inc\datado.h
.\objects\navi.o: ..\Source\inc\SetParaBao.h
.\objects\navi.o: ..\Source\inc\FirmwareUpdateFile.h
.\objects\navi.o: ..\INAV\DATASTRUCT.h
.\objects\navi.o: ..\INAV\CONST.h
.\objects\navi.o: ..\INAV\TYPEDEFINE.h
.\objects\navi.o: ..\INAV\FUNCTION.h
.\objects\navi.o: ..\INAV\ins.h
.\objects\navi.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\ctype.h
.\objects\navi.o: ..\INAV\EXTERNGLOBALDATA.h
