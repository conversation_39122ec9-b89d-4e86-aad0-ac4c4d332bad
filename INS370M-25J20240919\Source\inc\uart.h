/*!
    \file    uart.h
    \brief   uart header file for GD32F4xx
*/

#ifndef UART_H
#define UART_H

#include "gd32f4xx.h"
#include "bsp_uart.h"

/* UART compatibility definitions for GD32F4xx platform */

/* Function declarations */
void uart_init(void);
void uart_send_data(uint32_t uart_periph, uint8_t data);
uint8_t uart_receive_data(uint32_t uart_periph);

/* Compatibility macros */
#define UART_INIT()                 uart_init()

#endif /* UART_H */
