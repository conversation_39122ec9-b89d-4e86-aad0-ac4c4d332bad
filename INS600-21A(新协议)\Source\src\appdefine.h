/*!
    \file  appdefine.h
    \brief the header file of main 
*/
#ifndef __APPDEFINE_H
#define __APPDEFINE_H


#define	c_systemrunmode_cantooling	0


//#define c_output_normal			1
//#define	c_output_testfog		0
#define	c_output_enable_can		0
#define	c_txnavoutdata			1

#define	c_outputmode_normal		0x01
#define	c_outputmode_gdw		0x02
#define	c_outputmode_testing	0x04
#define	c_outputmode_testfog	0x08
#define	c_outputmode_runalg		0x10


#define	c_systemflag_rtkoff		0x01
#define	c_systemflag_algoff		0x02

extern	int goutputmode;
extern	int	gsystemflag;
#endif /* __APPDEFINE_H */


