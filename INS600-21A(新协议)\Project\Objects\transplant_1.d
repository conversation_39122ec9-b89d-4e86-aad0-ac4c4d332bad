.\objects\transplant_1.o: ..\Protocol\transplant.c
.\objects\transplant_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\transplant_1.o: ..\Source\inc\main.h
.\objects\transplant_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\transplant_1.o: ..\Library\CMSIS\core_cm4.h
.\objects\transplant_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\transplant_1.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\transplant_1.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\transplant_1.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\transplant_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\transplant_1.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\transplant_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\transplant_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\transplant_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\transplant_1.o: ..\Protocol\transplant.h
.\objects\transplant_1.o: ..\Protocol\drv_rtc.h
.\objects\transplant_1.o: ..\Protocol\insdef.h
