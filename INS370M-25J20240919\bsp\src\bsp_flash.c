#include "bsp_flash.h"

#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "main.h"

//#define STM_SECTOR_SIZE 			2048
//#define STM_USER_ADDR					STM_SECTOR_SIZE * 48

//#define FLASH_BUFF_SIZE  	512U	//bill 2023-06-25
#define FLASH_BUFF_SIZE  	1024U	//bill 2023-06-25
uint32_t g_Addr = ADDR_FMC_SECTOR_6;

uint16_t g_FlashBuf[FLASH_BUFF_SIZE];
uint32_t g_AddrBase;
uint8_t g_NeedWrite = 0;
uint8_t g_StartWrite = 0;

void InitFlashAddr(uint32_t nOffset)
{
	g_Addr = ADDR_FMC_SECTOR_6 + nOffset; 
}

void WriteOld(void)
{
	int i = 0;
	if(fmc_sector_erase(CTL_SECTOR_NUMBER_6) != FMC_READY)
	{
		//error
	}
	
	for( i = 0; i < FLASH_BUFF_SIZE; i++, g_AddrBase += 2)
	{
		fmc_halfword_program(g_AddrBase,g_FlashBuf[i]);
	}
	g_NeedWrite = 0;
}

void EndWrite(void)
{
	if(g_NeedWrite == 1)
	{
		WriteOld();	
	}
	fmc_lock();
	g_StartWrite = 0;
}

void WriteFlash(uint8_t *buff, uint16_t size)
{
	uint32_t addr_base;
	uint16_t len;
	uint16_t i;
	if(g_Addr < FLASH_BASE ||(g_Addr >= ADDR_FMC_SECTOR_6+16*1024))
		return;
	else
	{
		if(g_StartWrite == 0)
		{
			g_StartWrite = 1;
			g_NeedWrite = 0;
			fmc_unlock();
			/* clear pending flags */
			fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
		}

		while(size)
		{
			addr_base = ADDR_FMC_SECTOR_6;
			
			if(g_NeedWrite == 1 && g_AddrBase != addr_base)
			{
				WriteOld();
			}
			
			len=size;
			if(size < FLASH_BUFF_SIZE)
			{
				if(g_NeedWrite == 0)
					ReadFlashByAddr(addr_base, (uint8_t *)g_FlashBuf, FLASH_BUFF_SIZE*2);
				
				//memcpy((uint8_t *)(&g_FlashBuf[0]) + g_Addr - addr_base, buff, len);	//bill 
				memcpy((uint8_t *)(&g_FlashBuf[0]) + 0, buff, len);
				
				g_NeedWrite = 1;
				g_AddrBase = addr_base;
				g_Addr += len;
				//break;
			}
			
			//memcpy((uint8_t *)(&g_FlashBuf[0]) + g_Addr - addr_base, buff, len);	//bill 
			memcpy((uint8_t *)(&g_FlashBuf[0]) + 0, buff, len);
			if(fmc_sector_erase(CTL_SECTOR_NUMBER_6) != FMC_READY)
			{ 
				i = 0;
			}
			for(i=0;i<FLASH_BUFF_SIZE;)
			{
				if (fmc_halfword_program(addr_base,g_FlashBuf[i]) == FMC_READY)
				{
					addr_base=addr_base+2;
					i++;
				}
			}
			g_Addr += len;
			buff += len;
			size -= len;
		}
	}
}

void ReadFlashByAddr(uint32_t addr, uint8_t *buff, uint16_t size)
{
	uint16_t i;
	if(addr < ADDR_FMC_SECTOR_6)
		return;
	else
	{
		for(i = 0; i < size; i++)
		{
			buff[i] = *(__IO uint8_t *)(addr + i);
		}
	}
}

void ReadFlash(uint8_t *buff, uint16_t size)
{
	uint16_t i;
	for(i = 0; i < size; i++)
	{
		buff[i] = *(__IO uint8_t *)(g_Addr + i);
	}
	g_Addr += size;
}




