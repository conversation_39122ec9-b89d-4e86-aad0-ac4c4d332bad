#include "bsp_tim.h"

uint32_t time_periodic_sec_cnt = 0;
uint32_t time_periodic_min_cnt = 0;
uint32_t time_periodic_hour_cnt = 0;
uint8_t time_sync_flag = 0;

uint32_t time_base_periodic_cnt = 0;

uint32_t time_base_100ms_periodic_cnt = 0;
uint32_t time_base_100ms_Flag = 0;

uint32_t time_base_20ms_periodic_cnt=0;
uint32_t time_base_20ms_Flag = 0;

void bsp_tim_init(void)
{
	printf("=== Timer Initialization Started ===\n");

	// 由于PA0没有连接到FPGA，暂时跳过所有定时器初始化
	// 这样可以测试程序是否能正常运行到后续步骤
	printf("Skipping all timer initialization (PA0 not connected to FPGA)\n");

	printf("=== Timer Initialization Completed (Skipped) ===\n");

	// 注释掉的原始代码：
	/*
	timer_parameter_struct timer_initpara;
	timer_oc_parameter_struct timer_ocintpara;

	// TIMER1 PWM for FPGA clock
	rcu_periph_clock_enable(RCU_TIMER1);
	timer_deinit(TIMER1);
	timer_initpara.prescaler = 3;
	timer_initpara.alignedmode = TIMER_COUNTER_EDGE;
	timer_initpara.counterdirection = TIMER_COUNTER_UP;
	timer_initpara.period = 19;
	timer_initpara.clockdivision = TIMER_CKDIV_DIV1;
	timer_initpara.repetitioncounter = 0;
	timer_init(TIMER1,&timer_initpara);

	timer_ocintpara.ocpolarity = TIMER_OC_POLARITY_HIGH;
	timer_ocintpara.outputstate = TIMER_CCX_ENABLE;
	timer_ocintpara.ocnpolarity = TIMER_OCN_POLARITY_HIGH;
	timer_ocintpara.outputnstate = TIMER_CCXN_DISABLE;
	timer_ocintpara.ocidlestate = TIMER_OC_IDLE_STATE_LOW;
	timer_ocintpara.ocnidlestate = TIMER_OCN_IDLE_STATE_LOW;

	timer_channel_output_config(TIMER1,TIMER_CH_0,&timer_ocintpara);
	timer_channel_output_pulse_value_config(TIMER1,TIMER_CH_0,9);
	timer_channel_output_mode_config(TIMER1,TIMER_CH_0,TIMER_OC_MODE_PWM0);
	timer_channel_output_shadow_config(TIMER1,TIMER_CH_0,TIMER_OC_SHADOW_DISABLE);

	timer_auto_reload_shadow_enable(TIMER1);
	timer_enable(TIMER1);
	*/
}




