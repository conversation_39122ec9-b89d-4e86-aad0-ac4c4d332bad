//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
//---------------------------------------------------------

#include <stdio.h>
#include <string.h>
#include "appmain.h"
//#include "app_tool.h"


uint8_t	g_UpdateBackFlag=1;//反馈标志(0x01-正常/02-异常)
uint8_t g_UpdateSuccessful=0;
uint8_t g_ucSystemResetFlag=0;//

uint8_t g_UpdateFinishSuccessful=0;//升级完成是否成功
uint8_t g_VersionQueryFlag = 0;//版本查询标志 2025-2-26 zjz

								//
Setpara_Data stSetPara={0};
//*********20250102***ZJZ*********
static const char SetCoord[6][3] =
{
    {'X','Y','Z'},
    {'X','Z','Y'},
    {'Y','X','Z'},
    {'Y','Z','X'},
    {'Z','X','Y'},
    {'Z','Y','X'},
};

static const float SetDir[8][3] =
{
    {1.0,1.0,1.0},
    {-1.0,1.0,1.0},
    {1.0,-1.0,1.0},
    {1.0,1.0,-1.0},
    {-1.0,-1.0,1.0},
    {1.0,-1.0,-1.0},
    {-1.0,1.0,-1.0},
    {-1.0,-1.0,-1.0},
};
//帧头
void SendPara_SetHead(p_parabag_Other_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//帧尾
void SendPara_SetEnd(p_parabag_Other_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//开始升级帧头
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//开始升级帧尾
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//parabag_UpdateSend_back
//发送升级包帧头
void UpdateSend_SetHead(p_parabag_UpdateSend_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//发送升级包帧尾
void UpdateSend_SetEnd(p_parabag_UpdateSend_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//升级完成帧头
void UpdateEnd_SetHead(p_parabag_UpdateEnd_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//升级完成帧尾
void UpdateEnd_SetEnd(p_parabag_UpdateEnd_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//软件升级终止帧头
void UpdateStop_SetHead(p_parabag_UpdateStop_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//软件升级终止帧尾
void UpdateStop_SetEnd(p_parabag_UpdateStop_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}


//参数回读命令0帧头
void ReadPara0_SetHead(p_parabag_ReadPara0_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//参数回读命令0帧尾
void ReadPara0_SetEnd(p_parabag_ReadPara0_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//参数回读命令1帧头
void ReadPara1_SetHead(p_parabag_ReadPara1_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//参数回读命令1帧尾
void ReadPara1_SetEnd(p_parabag_ReadPara1_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//参数回读命令2帧头
void ReadPara2_SetHead(p_parabag_ReadPara2_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//参数回读命令2帧尾
void ReadPara2_SetEnd(p_parabag_ReadPara2_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//参数回读命令3帧头
void ReadPara3_SetHead(p_parabag_ReadPara3_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//参数回读命令3帧尾
void ReadPara3_SetEnd(p_parabag_ReadPara3_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//参数回读命令4帧头
void ReadPara4_SetHead(p_parabag_ReadPara4_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//参数回读命令4帧尾
void ReadPara4_SetEnd(p_parabag_ReadPara4_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}



	

//设置波特率
void SetParaBaud(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setbaud stBaud;//接收
    memcpy(&stBaud,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    //printf("check=%d\n",stBaud.ender.check);
    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    //printf("crc=%d\n",crc);
    if(crc == stBaud.ender.check)
    {
        stSetPara.Setbaud = stBaud.info.SetbaudPara;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //printf("baud=%d\n",stSetPara.Setbaud);

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_baud,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	

    delay_ms(500);
    nvic_irq_enable(UART4_IRQn, 0, 0);
    gd_eval_com_init(UART4, stSetPara.Setbaud*100);
    usart_interrupt_enable(UART4, USART_INT_RBNE);
}

//设置数据输出频率
void SetParaFrequency(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setfrequency stfrequency;
    memcpy(&stfrequency,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stfrequency.ender.check)
    {
        stSetPara.Setfre = stfrequency.info.SetfrePara;
        hSetting.settingData.freq = stSetPara.Setfre;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_frequency,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置GNSS杆臂参数
void SetParaGnss(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setgnss stgnss;
    memcpy(&stgnss,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stgnss.ender.check)
    {
        stSetPara.armX = stgnss.info.armX;
        stSetPara.armY = stgnss.info.armY;
        stSetPara.armZ = stgnss.info.armZ;
      
        hSetting.settingData.param.gnssArmLength[0] = stSetPara.armX;
        hSetting.settingData.param.gnssArmLength[1] = stSetPara.armY;
        hSetting.settingData.param.gnssArmLength[2] = stSetPara.armZ;
        comm_param_setbits(0);
        comm_nav_para_syn();
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_gnss,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置天线安装角度
void SetParaAngle(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetAngle stAngles;
    memcpy(&stAngles,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stAngles.ender.check)
    {
        stSetPara.angleX = stAngles.info.angleX;
        stSetPara.angleY = stAngles.info.angleY;
        stSetPara.angleZ = stAngles.info.angleZ;
      
        hSetting.settingData.param.gnssAtt_from_vehicle[0] = stSetPara.angleX;
        hSetting.settingData.param.gnssAtt_from_vehicle[1] = stSetPara.angleY;
        hSetting.settingData.param.gnssAtt_from_vehicle[2] = stSetPara.angleZ;
        comm_param_setbits(1);
        comm_nav_para_syn();
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_angle,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//惯导-后轮轴中心位置矢量
void SetParaVector(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetVector stVector;
    memcpy(&stVector,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stVector.ender.check)
    {
        stSetPara.vectorX = stVector.info.vectorX;
        stSetPara.vectorY = stVector.info.vectorY;
        stSetPara.vectorZ = stVector.info.vectorZ;
        hSetting.settingData.param.OBArmLength[0] = stSetPara.vectorX;
        hSetting.settingData.param.OBArmLength[1] = stSetPara.vectorY;
        hSetting.settingData.param.OBArmLength[2] = stSetPara.vectorZ;
        comm_param_setbits(2);
        comm_nav_para_syn();
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_vector,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//惯导角度安装偏差
void SetParaDeviation(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetDeviation stDeviation;
    memcpy(&stDeviation,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stDeviation.ender.check)
    {
        stSetPara.pitch = stDeviation.info.pitch;
        stSetPara.roll = stDeviation.info.roll;
        stSetPara.Course = stDeviation.info.Course;

        hSetting.settingData.param.OBAtt_from_vehicle[0] = stSetPara.pitch;
        hSetting.settingData.param.OBAtt_from_vehicle[1] = stSetPara.roll;
        hSetting.settingData.param.OBAtt_from_vehicle[2] = stSetPara.Course;
        comm_param_setbits(3);
        comm_nav_para_syn();
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_deviation,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置GNSS初始值
void SetParaGnssInitValue(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetGnssinitval stGnssinitval;
    memcpy(&stGnssinitval,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stGnssinitval.ender.check)
    {
        stSetPara.GnssInitCourse = stGnssinitval.info.Course;
        stSetPara.GnssInitPitch = stGnssinitval.info.pitch;
        stSetPara.GnssInitRoll = stGnssinitval.info.roll;
        stSetPara.GnssInitYaw = stGnssinitval.info.yaw;
        stSetPara.GnssInitLongitude = stGnssinitval.info.longitude;
        stSetPara.GnssInitLatitude = stGnssinitval.info.latitude;
        stSetPara.GnssInitHight = stGnssinitval.info.hight;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_initvalue,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
 }

 //设置坐标轴参数进入算法接口******20250102******ZJZ*********
void SetCoordToAlgorithm(void)
{
    combineData.Axis[0] = SetCoord[stSetPara.SetCoord][0];
    combineData.Axis[1] = SetCoord[stSetPara.SetCoord][1];
    combineData.Axis[2] = SetCoord[stSetPara.SetCoord][2];
  
    combineData.Axis_sign[0] = SetDir[stSetPara.SetDir][0];
    combineData.Axis_sign[1] = SetDir[stSetPara.SetDir][1];
    combineData.Axis_sign[2] = SetDir[stSetPara.SetDir][2];
  
    combineData.Axis_flag = 1;

}
   
//设置用户坐标轴
void SetParaCoord(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetCoord stCoord;
    memcpy(&stCoord,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stCoord.ender.check)
    {
        //printf("Set Coord=%d,Dir=%d\n",stCoord.info.SetCoordPara,stCoord.info.SetDirPara);
        stSetPara.SetCoord = stCoord.info.SetCoordPara;
        stSetPara.SetDir = stCoord.info.SetDirPara;
        SetCoordToAlgorithm();
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_coord,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置静态测零偏时间
void SetParaTime(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetTime stTime;
    memcpy(&stTime,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stTime.ender.check)
    {

        stSetPara.SetTime = stTime.info.SetTimepara;
        hSetting.settingData.timeCompensation = stSetPara.SetTime;
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_offsetime,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//固化参数
void SaveParaToFlash(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    uint8_t SaveFlashDatabuf[sizeof(Setpara_Data)]={0};	

    parabag_pararead stSavePara;//接收
    memcpy(&stSavePara,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stSavePara.ender.check)
    {
        memcpy(SaveFlashDatabuf,&stSetPara,sizeof(Setpara_Data));
      	Drv_FlashErase(SAVE_SET_PARA_ADDR,TRUE);
        Drv_FlashWrite(SaveFlashDatabuf, SAVE_SET_PARA_ADDR, sizeof(Setpara_Data),FALSE);

        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_solidify,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//初始化参数
void InitParaToAlgorithm(void)
{
        hSetting.settingData.freq = stSetPara.Setfre;

        hSetting.settingData.param.OBAtt_from_vehicle[0] = stSetPara.pitch;
        hSetting.settingData.param.OBAtt_from_vehicle[1] = stSetPara.roll;
        hSetting.settingData.param.OBAtt_from_vehicle[2] = stSetPara.Course;
        comm_param_setbits(3);
        

        hSetting.settingData.param.gnssAtt_from_vehicle[0] = stSetPara.angleX;
        hSetting.settingData.param.gnssAtt_from_vehicle[1] = stSetPara.angleY;
        hSetting.settingData.param.gnssAtt_from_vehicle[2] = stSetPara.angleZ;
        comm_param_setbits(1);

        hSetting.settingData.param.gnssArmLength[0] = stSetPara.armX;
        hSetting.settingData.param.gnssArmLength[1] = stSetPara.armY;
        hSetting.settingData.param.gnssArmLength[2] = stSetPara.armZ;
        comm_param_setbits(0);

        hSetting.settingData.param.OBArmLength[0] = stSetPara.vectorX;
        hSetting.settingData.param.OBArmLength[1] = stSetPara.vectorY;
        hSetting.settingData.param.OBArmLength[2] = stSetPara.vectorZ;
        comm_param_setbits(2);

        SetCoordToAlgorithm();

        hSetting.settingData.timeCompensation = stSetPara.SetTime;
        
        comm_nav_para_syn();
        
        
        //是否开启Debug模式  AAC1
        combineData.Param.sim = stSetPara.SetDebugMode;
        //陀螺类型  AAC2
        combineData.imuSelect = stSetPara.SetGyroType;
        //GPS中断类型  AAC3
        combineData.fusion = stSetPara.SetGpsType;
        //数据输出Type   AAC4
        combineData.outputType = stSetPara.SetDataOutType; 
        
        
}

//读取参数
void ReadParaFromFlash(void)
{
    uint8_t ReadFlashDatabuf[sizeof(Setpara_Data)]={0};	

    Drv_FlashRead_8bit(ReadFlashDatabuf,SAVE_SET_PARA_ADDR,sizeof(Setpara_Data));
    //Drv_FlashRead_8bit(u8* data_8,u32 address, u32 length);

    memcpy(&stSetPara,ReadFlashDatabuf,sizeof(Setpara_Data)); 
    
    if(stSetPara.Flag != 422 )//首次烧录422程序时，设置波特率为460800
    {
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        stSetPara.Flag = 422;
        stSetPara.Setbaud=4608;
        stSetPara.Setfre=SETPARA_DATAOUT_FPGA_FREQ;
      
        memcpy(ReadFlashDatabuf,&stSetPara,sizeof(Setpara_Data)); 
      
        Drv_FlashErase(SAVE_SET_PARA_ADDR,TRUE);
        Drv_FlashWrite(ReadFlashDatabuf, SAVE_SET_PARA_ADDR, sizeof(Setpara_Data),FALSE);
    }
    
    InitParaToAlgorithm();
}



//恢复出厂值
void RestoreFactory(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    uint8_t SaveFlashDatabuf[sizeof(Setpara_Data)]={0};

    parabag_pararead stFactoryPara;//接收
    memcpy(&stFactoryPara,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stFactoryPara.ender.check)
    {
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        //设置参数初始值，后面可能会改
        stSetPara.Flag = 422;
        stSetPara.Setbaud=4608;
        stSetPara.GnssInitLongitude = 113.811000;
        stSetPara.GnssInitLatitude = 22.75120;
        stSetPara.GnssInitHight = 15.4662;
        stSetPara.Setfre=SETPARA_DATAOUT_FPGA_FREQ;

        memcpy(SaveFlashDatabuf,&stSetPara,sizeof(Setpara_Data));

      	Drv_FlashErase(SAVE_SET_PARA_ADDR,TRUE);
        Drv_FlashWrite(SaveFlashDatabuf, SAVE_SET_PARA_ADDR, sizeof(Setpara_Data),FALSE);

        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_factory,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置所有参数
void SetParaAll(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetPara stAllPara;
    memcpy(&stAllPara,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stAllPara.ender.check)
    {
        stSetPara.Setfre = stAllPara.info.SetfrePara;
        hSetting.settingData.freq = stSetPara.Setfre;

        stSetPara.Course = stAllPara.info.Course;
        stSetPara.pitch = stAllPara.info.pitch;
        stSetPara.roll = stAllPara.info.roll;
        hSetting.settingData.param.OBAtt_from_vehicle[0] = stSetPara.pitch;
        hSetting.settingData.param.OBAtt_from_vehicle[1] = stSetPara.roll;
        hSetting.settingData.param.OBAtt_from_vehicle[2] = stSetPara.Course;
        comm_param_setbits(3);
        

        stSetPara.angleX = stAllPara.info.angleX;
        stSetPara.angleY = stAllPara.info.angleY;
        stSetPara.angleZ = stAllPara.info.angleZ;
        hSetting.settingData.param.gnssAtt_from_vehicle[0] = stSetPara.angleX;
        hSetting.settingData.param.gnssAtt_from_vehicle[1] = stSetPara.angleY;
        hSetting.settingData.param.gnssAtt_from_vehicle[2] = stSetPara.angleZ;
        comm_param_setbits(1);

        stSetPara.armX = stAllPara.info.armX;
        stSetPara.armY = stAllPara.info.armY;
        stSetPara.armZ = stAllPara.info.armZ;
        hSetting.settingData.param.gnssArmLength[0] = stSetPara.armX;
        hSetting.settingData.param.gnssArmLength[1] = stSetPara.armY;
        hSetting.settingData.param.gnssArmLength[2] = stSetPara.armZ;
        comm_param_setbits(0);

        stSetPara.vectorX = stAllPara.info.vectorX;
        stSetPara.vectorY = stAllPara.info.vectorY;
        stSetPara.vectorZ = stAllPara.info.vectorZ;
        hSetting.settingData.param.OBArmLength[0] = stSetPara.vectorX;
        hSetting.settingData.param.OBArmLength[1] = stSetPara.vectorY;
        hSetting.settingData.param.OBArmLength[2] = stSetPara.vectorZ;
        comm_param_setbits(2);

        stSetPara.SetCoord = stAllPara.info.SetCoordPara;
        stSetPara.SetDir = stAllPara.info.SetDirPara;
        SetCoordToAlgorithm();

        stSetPara.SetTime = stAllPara.info.SetTimepara;
        hSetting.settingData.timeCompensation = stSetPara.SetTime;

        stSetPara.GnssInitCourse = stAllPara.info.GnssInitCourse;
        stSetPara.GnssInitPitch = stAllPara.info.GnssInitPitch;
        stSetPara.GnssInitRoll = stAllPara.info.GnssInitRoll;
        stSetPara.GnssInitYaw = stAllPara.info.GnssInitYaw;
        stSetPara.GnssInitLongitude = stAllPara.info.GnssInitLongitude;
        stSetPara.GnssInitLatitude = stAllPara.info.GnssInitLatitude;
        stSetPara.GnssInitHight = stAllPara.info.GnssInitHight;
        
        stSetPara.FactorDyroX = stAllPara.info.FactorDyroX;
        stSetPara.FactorDyroY = stAllPara.info.FactorDyroY;
        stSetPara.FactorDyroZ = stAllPara.info.FactorDyroZ;

        stSetPara.FactorAccX = stAllPara.info.FactorAccX;
        stSetPara.FactorAccY = stAllPara.info.FactorAccY;
        stSetPara.FactorAccZ = stAllPara.info.FactorAccZ;
        
        comm_nav_para_syn();

        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_setpara,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//参数回读(输出频率,GNSS杆臂参数)
void ReadPara_0(p_dmauart_t pdmauart)
{
    uint8_t crc=0;

    parabag_ReadPara0_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_ReadPara0_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara0_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara0_back));

    parabag_pararead stParaRead;//接收结构体变量
    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stParaRead.ender.check)
    {
        stSendPara.info.SetfrePara = stSetPara.Setfre;
        //memcpy(&stSendPara.info.armX,&stSetPara.armX,12);
        stSendPara.info.armX = stSetPara.armX;
        stSendPara.info.armY = stSetPara.armY;
        stSendPara.info.armZ = stSetPara.armZ;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    ReadPara0_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
    //配置帧尾
    ReadPara0_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//参数回读(天线安装角度,惯导-后轮轴中心位置矢量)
void ReadPara_1(p_dmauart_t pdmauart)
{
    uint8_t crc=0;

    parabag_ReadPara1_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_ReadPara1_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara1_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara1_back));

    parabag_pararead stParaRead;//接收结构体变量
    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stParaRead.ender.check)
    {
    	//memcpy(&stSendPara.info.angleX,&stSetPara.angleX,12);
    	stSendPara.info.angleX = stSetPara.angleX;
		stSendPara.info.angleY = stSetPara.angleY;
		stSendPara.info.angleZ = stSetPara.angleZ;
        
        //memcpy(&stSendPara.info.vectorX,&stSetPara.vectorX,12);
		stSendPara.info.vectorX = stSetPara.vectorX;
		stSendPara.info.vectorY = stSetPara.vectorY;
		stSendPara.info.vectorZ = stSetPara.vectorZ;
		
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    ReadPara1_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
    //配置帧尾
    ReadPara1_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}


//参数回读(惯导角度安装偏差,坐标轴设置)
void ReadPara_2(p_dmauart_t pdmauart)
{
    uint8_t crc=0;

    parabag_ReadPara2_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_ReadPara2_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara2_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara2_back));

    parabag_pararead stParaRead;//接收结构体变量
    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stParaRead.ender.check)
    {
        //printf("Read Coord=%d,Dir=%d\n",stSetPara.SetCoord,stSetPara.SetDir);
        //memcpy(&stSendPara.info.pitch,&stSetPara.pitch,12);
		stSendPara.info.pitch = stSetPara.pitch;
		stSendPara.info.roll = stSetPara.roll;
		stSendPara.info.Course = stSetPara.Course;
		
        //memcpy(&stSendPara.info.SetCoordPara,&stSetPara.SetCoord,2);
		stSendPara.info.SetCoordPara = stSetPara.SetCoord;
		stSendPara.info.SetDirPara = stSetPara.SetDir;
		
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    ReadPara2_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
    //配置帧尾
    ReadPara2_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//参数回读(静态测零偏时间,GNSS初始值)
void ReadPara_3(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_ReadPara3_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_ReadPara3_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara3_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara3_back));

    parabag_pararead stParaRead;//接收结构体变量
    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stParaRead.ender.check)
    {
        stSendPara.info.SetTimepara = stSetPara.SetTime;
		
        //memcpy(&stSendPara.info.GnssInitpitch,&stSetPara.GnssInitPitch,28);
		stSendPara.info.GnssInitpitch = stSetPara.GnssInitPitch;
		stSendPara.info.GnssInitroll = stSetPara.GnssInitRoll;
		stSendPara.info.GnssInitCourse = stSetPara.GnssInitCourse;
		stSendPara.info.GnssInityaw = stSetPara.GnssInitYaw;
		stSendPara.info.GnssInitlongitude = stSetPara.GnssInitLongitude;
		stSendPara.info.GnssInitlatitude = stSetPara.GnssInitLatitude;
		stSendPara.info.GnssInithight = stSetPara.GnssInitHight;
		
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    ReadPara3_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
    //配置帧尾
    ReadPara3_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//参数回读(陀螺加计标定因数)
void ReadPara_4(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_ReadPara4_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_ReadPara4_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara4_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara4_back));

    parabag_pararead stParaRead;//接收结构体变量
    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stParaRead.ender.check)
    {
        stSendPara.info.FactorDyroX = stSetPara.FactorDyroX;
        stSendPara.info.FactorDyroY = stSetPara.FactorDyroY;
        stSendPara.info.FactorDyroZ = stSetPara.FactorDyroZ;

        stSendPara.info.FactorAccX = stSetPara.FactorAccX;
        stSendPara.info.FactorAccY = stSetPara.FactorAccY;
        stSendPara.info.FactorAccZ = stSetPara.FactorAccZ;
        
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    ReadPara4_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
    //配置帧尾
    ReadPara4_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

uint16_t RecParaCmd[8]={SETPARA_TYPE0_frequency,SETPARA_TYPE0_gnss,SETPARA_TYPE0_angle,SETPARA_TYPE0_vector,SETPARA_TYPE0_deviation,SETPARA_TYPE0_coord,SETPARA_TYPE0_offsetime,SETPARA_TYPE0_initvalue};
//参数回读(选择参数)
void ReadPara(p_dmauart_t pdmauart)
{
    uint16_t usCmd0=0,usCmd1=0;	//选择回读参数的类型

    memcpy(&usCmd0,&pdmauart->rxbuffer[7],2);
    memcpy(&usCmd1,&pdmauart->rxbuffer[9],2);

    if((usCmd0 == SETPARA_TYPE0_frequency)&&(usCmd1 == SETPARA_TYPE0_gnss))
    {
        ReadPara_0(pdmauart);
    }
    else if((usCmd0 == SETPARA_TYPE0_angle)&&(usCmd1 == SETPARA_TYPE0_vector))
    {
        ReadPara_1(pdmauart);
    }
    else if((usCmd0 == SETPARA_TYPE0_deviation)&&(usCmd1 == SETPARA_TYPE0_coord))
    {
        ReadPara_2(pdmauart);
    }
    else if((usCmd0 == SETPARA_TYPE0_offsetime)&&(usCmd1 == SETPARA_TYPE0_initvalue))
    {
        ReadPara_3(pdmauart);
    }
    else if((usCmd0 == SETPARA_TYPE0_FactorGyro)&&(usCmd1 == SETPARA_TYPE0_FactorAcc))
    {
        ReadPara_4(pdmauart);
    }
}

////参数回读(选择参数)
//void ReadPara(p_dmauart_t pdmauart)
//{
//    uint8_t i=0,aPos=0,bPos=0,cPos=0,dPos=0;
//    uint16_t RecParaCmd[7]={SETPARA_TYPE0_frequency,SETPARA_TYPE0_gnss,SETPARA_TYPE0_angle,SETPARA_TYPE0_vector,SETPARA_TYPE0_deviation,SETPARA_TYPE0_coord,SETPARA_TYPE0_offsetime};
//    uint8_t RecParaLen[7]={2,12,12,12,12,2,2};
//    unsigned short BaoLen = sizeof(parabag_Other_back);
//    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
//    parabag_pararead stParaRead;//接收结构体变量
//    parabag_Other_back stSendPara;//定义回应结构体变量

//    memcpy(&stParaRead,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

//    for(i=0;i<7;i++)
//    {
//        if(stParaRead.info.ParaType[0] == RecParaCmd[i])
//        {
//            aPos=i;
//        }
//        if(stParaRead.info.ParaType[1] == RecParaCmd[i])
//        {
//            bPos=i;
//        }
//        if(stParaRead.info.ParaType[2] == RecParaCmd[i])
//        {
//            cPos=i;
//        }
//        if(stParaRead.info.ParaType[3] == RecParaCmd[i])
//        {
//            dPos=i;
//        }
//    }

//    stSendPara.info.BackFlag=0x01;
//    //选择第一个参数
//    if(aPos==0)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.Setfre,RecParaLen[aPos]);
//    }
//    else if(aPos==1)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.armX,RecParaLen[aPos]);
//    }
//    else if(aPos==2)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.angleX,RecParaLen[aPos]);
//    }
//    else if(aPos==3)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.vectorX,RecParaLen[aPos]);
//    }
//    else if(aPos==4)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.pitch,RecParaLen[aPos]);
//    }
//    else if(aPos==5)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.SetCoord,RecParaLen[aPos]);
//    }
//    else if(aPos==6)
//    {
//        memcpy(stSendPara.info.reserve,&stSetPara.SetTime,RecParaLen[aPos]);
//    }


//    //选择第二个参数
//    if(bPos==0)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.Setfre,RecParaLen[bPos]);
//    }
//    else if(bPos==1)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.armX,RecParaLen[bPos]);
//    }
//    else if(bPos==2)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.angleX,RecParaLen[bPos]);
//    }
//    else if(bPos==3)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.vectorX,RecParaLen[bPos]);
//    }
//    else if(bPos==4)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.pitch,RecParaLen[bPos]);
//    }
//    else if(bPos==5)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.SetCoord,RecParaLen[bPos]);
//    }
//    else if(bPos==6)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos],&stSetPara.SetTime,RecParaLen[bPos]);
//    }

//    //选择第三个参数
//    if(cPos==0)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.Setfre,RecParaLen[cPos]);
//    }
//    else if(cPos==1)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.armX,RecParaLen[cPos]);
//    }
//    else if(cPos==2)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.angleX,RecParaLen[cPos]);
//    }
//    else if(cPos==3)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.vectorX,RecParaLen[cPos]);
//    }
//    else if(cPos==4)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.pitch,RecParaLen[cPos]);
//    }
//    else if(cPos==5)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.SetCoord,RecParaLen[cPos]);
//    }
//    else if(cPos==6)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos],&stSetPara.SetTime,RecParaLen[cPos]);
//    }

//    //选择第四个参数
//    if(dPos==0)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.Setfre,RecParaLen[dPos]);
//    }
//    else if(dPos==1)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.armX,RecParaLen[dPos]);
//    }
//    else if(dPos==2)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.angleX,RecParaLen[dPos]);
//    }
//    else if(dPos==3)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.vectorX,RecParaLen[dPos]);
//    }
//    else if(dPos==4)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.pitch,RecParaLen[dPos]);
//    }
//    else if(dPos==5)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.SetCoord,RecParaLen[dPos]);
//    }
//    else if(dPos==6)
//    {
//        memcpy(stSendPara.info.reserve+RecParaLen[aPos]+RecParaLen[bPos]+RecParaLen[cPos],&stSetPara.SetTime,RecParaLen[dPos]);
//    }

//    ////配置帧头
//    //stSendPara.head.header[0]=SETPARA_HEADER_0;
//    //stSendPara.head.header[1]=SETPARA_HEADER_1;
//    //stSendPara.head.header[2]=SETPARA_HEADER_2;
//    //stSendPara.head.Type = SETPARA_TYPE1_readpara;
//    //stSendPara.head.len = BaoLen;


//    //memcpy(SendDatabuf,&stSendPara,BaoLen-3);
//    // //配置帧尾
//    //SendDatabuf[BaoLen-3] = crc_verify_8bit(&SendDatabuf[3],BaoLen - 3 - 3);
//    //SendDatabuf[BaoLen-2] = SETPARA_END_0;
//    //SendDatabuf[BaoLen-1] = SETPARA_END_1;

//    //uart3DMASend(SendDatabuf,BaoLen);	

//    //配置帧头
//    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_readpara,BaoLen);
//    //配置帧尾
//    SendPara_SetEnd(&stSendPara,BaoLen);

//    memcpy(SendDatabuf,&stSendPara,BaoLen);
//    uart3DMASend(SendDatabuf,BaoLen);
//}

//设置GPS中断类型
void SetParaGpsType(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stGpsType;
    memcpy(&stGpsType,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stGpsType.ender.check)
    {
        stSetPara.SetGpsType = stGpsType.info.SetTypePara;
        combineData.fusion = stSetPara.SetGpsType;
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_gps,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//数据输出Type
void SetParaDataOutType(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stDataOutType;
    memcpy(&stDataOutType,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stDataOutType.ender.check)
    {
        stSetPara.SetDataOutType = stDataOutType.info.SetTypePara;
        combineData.outputType = stSetPara.SetDataOutType;
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_type,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//是否开启Debug模式
void SetParaDebugMode(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stDebugMod;
    memcpy(&stDebugMod,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stDebugMod.ender.check)
    {
        stSetPara.SetDebugMode = stDebugMod.info.SetTypePara;
        combineData.Param.sim = stSetPara.SetDebugMode;
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_debug,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}


//陀螺类型
void SetParaGyroType(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stGyroTyped;
    memcpy(&stGyroTyped,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stGyroTyped.ender.check)
    {
        stSetPara.SetGyroType = stGyroTyped.info.SetTypePara;
        combineData.imuSelect = stSetPara.SetGyroType;
      
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_gyro,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置标定参数
void SetParaCalibration(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetCalibration stCalibration;
    memcpy(&stCalibration,&pdmauart->rxbuffer[0],SETPARA_RXBUFFER_DMA_SIZE);

    //long double G=3.1415926*3.1415926;
    //double G=13.14159261289409569804;
    //printf("G=%.20f\n",G);

    //double H=G*100000000;
    //printf("H=%.20f\n",H);

    //for(int i=0;i<12;i++)
    //  printf("Gyro_0[%d]=%.20f\n",i,stCalibration.info.GyroCalibrate[i]);

    //for(int i=0;i<12;i++)
    //  printf("Acc_0[%d]=%.20f\n",i,stCalibration.info.AccCalibrate[i]);

    //printf("0x%x,0x%x,0x%x\n",pdmauart->rxbuffer[253],pdmauart->rxbuffer[254],pdmauart->rxbuffer[255]);
    //printf("check=%d\n",stCalibration.ender.check);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    //printf("crc=%d\n",crc);

    if(crc == stCalibration.ender.check)
    {
        memcpy(stSetPara.GyroCalibrate,stCalibration.info.GyroCalibrate,96);
        memcpy(stSetPara.AccCalibrate,stCalibration.info.AccCalibrate,96);
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }


    //for(int i=0;i<12;i++)
    //  printf("Gyro[%d]=%.20f\n",i,stSetPara.GyroCalibrate[i]);

    //for(int i=0;i<12;i++)
    //  printf("Acc[%d]=%.20f\n",i,stSetPara.AccCalibrate[i]);

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_calibration,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置卡尔曼滤波Q矩阵
void SetParaKalmanQ(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetKalmanQ stKalmanQ;
    memcpy(&stKalmanQ,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stKalmanQ.ender.check)
    {
        memcpy(stSetPara.Q_phi,stKalmanQ.info.Q_phi,96);
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_KalmanQ,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//设置卡尔曼滤波R矩阵
void SetParaKalmanR(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetKalmanR stKalmanR;
    memcpy(&stKalmanR,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stKalmanR.ender.check)
    {
        memcpy(&stSetPara.R_heading,&stKalmanR.info.R_heading,64);
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_KalmanR,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//间接滤波校正系数
void SetParaFilter(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetFilter stFilter;//接收
    memcpy(&stFilter,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stFilter.ender.check)
    {
        memcpy(stSetPara.D_phi,stFilter.info.D_phi,96);
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_filter,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//陀螺标定因素
void SetParaFactorGyro(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetFactorGyro stFactorGyro;//接收
    memcpy(&stFactorGyro,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stFactorGyro.ender.check)
    {
        stSetPara.FactorDyroX = stFactorGyro.info.FactorDyroX;
        stSetPara.FactorDyroY = stFactorGyro.info.FactorDyroY;
        stSetPara.FactorDyroZ = stFactorGyro.info.FactorDyroZ;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_FactorGyro,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//加计标定因素
void SetParaFactorAcc(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetFactorAcc stFactorAcc;//接收
    memcpy(&stFactorAcc,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stFactorAcc.ender.check)
    {
        stSetPara.FactorAccX = stFactorAcc.info.FactorAccX;
        stSetPara.FactorAccY = stFactorAcc.info.FactorAccY;
        stSetPara.FactorAccZ = stFactorAcc.info.FactorAccZ;
        stSendPara.info.BackFlag=0x01;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    SendPara_SetHead(&stSendPara,SETPARA_TYPE1_FactorAcc,BaoLen);
    //配置帧尾
    SendPara_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}

//软件升级开始命令
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stUpdateStart;//接收
    memcpy(&stUpdateStart,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);
    //printf("check=%d\n",stUpdateStart.ender.check);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    //printf("crc=%d\n",crc);

    if(crc == stUpdateStart.ender.check)
    {
        stSendPara.info.BackFlag=0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;

        g_StartUpdateFirm=1;
        g_UpdateSuccessful=0;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }
    
    g_VersionQueryFlag=1;//版本查询

    stSendPara.info.Count++;//帧计数

    delay_ms(100);

    //配置帧头
    UpdateStart_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_START,BaoLen);
    //配置帧尾
    UpdateStart_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
    delay_ms(100);
}


//固件升级处理
void ParaUpdateHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen)
{
	static u32 uiOffsetAddr=0,uiOffsetAddr1=0,uiLastBaoInDex=1,flag=0;
	u8 UpdateFlagBuff[5]={0};//升级被分区完成标志,升级完成后，保存此标志到FLASH指定位置，拷贝到运行区后，此标志擦除

	//if(uiLastBaoInDex == usIndex)
  if(((uiLastBaoInDex == usIndex)||(uiLastBaoInDex != usIndex-1))&&(0 !=usIndex))
	{
		return;
	}

   if(flag==0)
   {
        if(0 !=usIndex)//首帧帧索引不为0时，报错
        {
            g_UpdateBackFlag=2;//反馈标志(0x01-正常/02-异常)
            return;
        }
        else//检测到首帧帧索引为0时，才通过
        {
            flag=1;
        }
    }

	if(usIndex == 0)
	{
		uiOffsetAddr=0;
		Drv_FlashErase(APP_UPDATE_ADDRESS,TRUE);
		Drv_FlashErase(APP_UPDATE_ADDRESS1,TRUE);
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}
	else
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}

	//升级完成后，保存升级标志和数据长度，并重启
	if(usIndex>=usTotalBao-1)
	{
    g_UpdateFinishSuccessful=1;
		UpdateFlagBuff[0] =0xC5;//保存升级标志
		//数据长度
		UpdateFlagBuff[4] = (u8)(uiOffsetAddr>>24);//高位
		UpdateFlagBuff[3] = (u8)(uiOffsetAddr>>16);//中高位
		UpdateFlagBuff[2] = (u8)(uiOffsetAddr>>8);//中低位
		UpdateFlagBuff[1] = (u8)(uiOffsetAddr);//低位
		
		//Drv_FlashWrite_8bit(APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), UpdateFlagBuff);//保存升级标志
		Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), FALSE);//保存升级标志

		uiOffsetAddr=0;

		//系统复位重启
		//Drv_SystemReset();
	}

	uiLastBaoInDex = usIndex;
}

//发送升级包命令
void SetParaUpdateSend(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateSend_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateSend_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateSend_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateSend_back));
    g_UpdateBackFlag=1;
    g_VersionQueryFlag=0;//版本查询

    parabag_UpdateSend stUpdateSend;//接收
    memcpy(&stUpdateSend,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateSend.ender.check)
    {
        //printf("UpdateSend :%d,%d,%d\n",stUpdateSend.info.BaoIndex,stUpdateSend.info.TotalBao,stUpdateSend.info.Length);
        ParaUpdateHandle(stUpdateSend.info.UpdateData,stUpdateSend.info.BaoIndex,stUpdateSend.info.TotalBao,stUpdateSend.info.Length);
        stSendPara.info.BackFlag=g_UpdateBackFlag;
        g_UpdateSuccessful=1;
    }
    else 
    {
        //printf("check=%d,crc=%d\n",stUpdateSend.ender.check,crc);
        //printf("UpdateSend Fail:%d\n",stUpdateSend.info.BaoIndex);
        stSendPara.info.BackFlag=0x02;
    }

    stSendPara.info.BaoIndex = stUpdateSend.info.BaoIndex;
    stSendPara.info.Count++;//帧计数
    
    if(stSendPara.info.BackFlag==0x02)
    {
        delay_ms(500);
        nvic_irq_enable(UART4_IRQn, 0, 0);
        gd_eval_com_init(UART4, stSetPara.Setbaud*100);
        usart_interrupt_enable(UART4, USART_INT_RBNE);
    }

    //配置帧头
    UpdateSend_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_SEND,BaoLen);
    //配置帧尾
    UpdateSend_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}


//固件升级包完成命令
void SetParaUpdateEnd(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateEnd_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateEnd_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateEnd_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateEnd_back));

    parabag_SetType stUpdateEnd;//接收
    memcpy(&stUpdateEnd,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stUpdateEnd.ender.check)
    {
        if((g_UpdateFinishSuccessful==1)||(g_VersionQueryFlag==1))
        {
            stSendPara.info.BackFlag=0x01;

            g_StartUpdateFirm=0;
            g_ucSystemResetFlag=1;
        }
        else
        {
            stSendPara.info.BackFlag=0x02;
        }
        
        //g_StartUpdateFirm=0;
        //g_ucSystemResetFlag=1;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    UpdateEnd_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_END,BaoLen);
    //配置帧尾
    UpdateEnd_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	

    if((g_ucSystemResetFlag==1)&&(g_UpdateSuccessful==1))
    {
        delay_ms(1000);
        //printf("Drv_SystemReset\n");
        Drv_SystemReset();
    }
}

//固件升级终止命令
void SetParaUpdateStop(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStop_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateStop_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStop_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStop_back));

    parabag_SetType stUpdateStop;//接收
    memcpy(&stUpdateStop,pdmauart->rxbuffer,SETPARA_RXBUFFER_DMA_SIZE);

    crc =  crc_verify_8bit(&pdmauart->rxbuffer[3],SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stUpdateStop.ender.check)
    {
        stSendPara.info.BackFlag=0x01;

        g_StartUpdateFirm=0;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    UpdateStop_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_END,BaoLen);
    //配置帧尾
    UpdateStop_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
}


//参数设置主流程控制
void UartDmaRecSetPara(p_dmauart_t pdmauart)
{
    unsigned short Type=0;	//报文类型

    memcpy(&Type,&pdmauart->rxbuffer[3],2);

    switch(Type)
    {
        case SETPARA_TYPE0_output:	//输出参数
            break;
        case SETPARA_TYPE0_baud:	//波特率	
            SetParaBaud(pdmauart);
            break;
        case SETPARA_TYPE0_frequency:	//数据输出频率
            SetParaFrequency(pdmauart);
            break;
        case SETPARA_TYPE0_gnss:	//设置GNSS杆臂参数
            SetParaGnss(pdmauart);
            break;
        case SETPARA_TYPE0_angle:	//设置天线安装角度
            SetParaAngle(pdmauart);
            break;
        case SETPARA_TYPE0_vector:	//惯导-后轮轴中心位置矢量
            SetParaVector(pdmauart);
            break;
        case SETPARA_TYPE0_deviation:	//惯导角度安装偏差
            SetParaDeviation(pdmauart);
            break;
        case SETPARA_TYPE0_initvalue:	//GNSS初始值
            SetParaGnssInitValue(pdmauart);
            break;

        case SETPARA_TYPE0_coord:	//设置用户坐标轴
            SetParaCoord(pdmauart);
            break;
        case SETPARA_TYPE0_offsetime:	//设置静态测零偏时间
            SetParaTime(pdmauart);
            break;
        case SETPARA_TYPE0_transfer:	//设置透传协议
            break;
        case SETPARA_TYPE0_solidify:	//固化参数
             SaveParaToFlash(pdmauart);
            break;
        case SETPARA_TYPE0_factory:	//恢复出厂设置
             RestoreFactory(pdmauart);
            break;
        case SETPARA_TYPE0_setpara:	//设置所有参数
             SetParaAll(pdmauart);
            break;

        case SETPARA_TYPE0_readpara:	//参数回读
             ReadPara(pdmauart);
            break;
        case SETPARA_TYPE0_readver:	//读取版本号 ?
            break;
        case SETPARA_TYPE0_gps:         //GPS中断类型
             SetParaGpsType(pdmauart);
            break;
        case SETPARA_TYPE0_type:	//数据输出Type
             SetParaDataOutType(pdmauart);
            break;
        case SETPARA_TYPE0_debug:	//是否开启Debug模式
             SetParaDebugMode(pdmauart);
            break;
        case SETPARA_TYPE0_gyro:	//陀螺类型
             SetParaGyroType(pdmauart);
            break;

        case SETPARA_TYPE0_calibration:	//标定参数
             SetParaCalibration(pdmauart);
            break;
        case SETPARA_TYPE0_temoffset:	//温度补偿
            break;
        case SETPARA_TYPE0_KalmanQ:	//卡尔曼滤波Q矩阵
             SetParaKalmanQ(pdmauart);
            break;
        case SETPARA_TYPE0_KalmanR:	//卡尔曼滤波R矩阵
             SetParaKalmanR(pdmauart);
            break;
        case SETPARA_TYPE0_filter:	//间接滤波校正系数
             SetParaFilter(pdmauart);
            break;
        case SETPARA_TYPE0_FactorGyro:	//陀螺标定因数
             SetParaFactorGyro(pdmauart);
            break;
        case SETPARA_TYPE0_FactorAcc:	//加计标定因数
             SetParaFactorAcc(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_START://软件升级开始命令
             SetParaUpdateStart(pdmauart);
            break;

        case SETPARA_TYPE0_UPDATE_SEND:	//发送升级包命令
             SetParaUpdateSend(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_END:	//升级包完成命令
             SetParaUpdateEnd(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_STOP:	//升级终止命令
             SetParaUpdateStop(pdmauart);
            break;

        default:
            break;
    }
}


