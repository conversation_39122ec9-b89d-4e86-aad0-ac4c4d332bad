/********************************** (C) COPYRIGHT *******************************
* File Name          : CH395UART.C
* Author             : MJX
* Version            : V1.0
* Date               : 2012/11/30
* Description        : CH395芯片 硬件标准异步串口连接的硬件抽象层 V1.0
*                      提供I/O接口子程序
*******************************************************************************/
/*
1.请mDelayuS ,mDelaymS函数实现自己的延时函数
2.CH395_Send_Byte 函数实现自己的串口发送函数

*******************************************************************************/

#define CH395UART_C_

/* 头文件包含 */
#include "CH395UART.H"
#include "CH395INC.H"
#include "bsp_sys.h"

#if CHIP_USED == USE_CHIP_GD32
// 前向声明延时函数，避免包含冲突
extern void delay_us(uint32_t nus);
extern void delay_ms(uint32_t nms);
#else
#include "delay.h"
#endif

/*******************************************************************************
* Function Name  : mDelayuS
* Description    : 微秒级延时函数
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelayuS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_us((uint32_t)delay);
#else
	delay_us(delay);
#endif
}

/*******************************************************************************
* Function Name  : mDelaymS
* Description    : 毫秒级延时函数
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelaymS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_ms((uint32_t)delay);
#else
	delay_ms(delay);
#endif
}


/*******************************************************************************
* Function Name  : CH395_Send_Byte
* Description    : 向CH395写入一字节数据
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395_Send_Byte( UINT8 dat )
{
#if CHIP_USED == USE_CHIP_GD32
	// GD32F4xx平台使用UART发送
	// 这里需要根据实际使用的UART端口进行配置
	// 示例使用USART1
	usart_data_transmit(USART1, dat);
	while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));
#else
	USART2->DR = ( dat & (UINT16)0x01FF );								
	while( !( USART2->SR & USART_FLAG_TXE ) ); 
#endif
}


/*******************************************************************************
* Function Name  : xWriteCH395Cmd
* Description    : 向CH395写命令
* Input          : mCmd---将要写入CH395的命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Cmd( UINT8 mCmd ) 
{
	CH395_Send_Byte( SER_SYNC_CODE1 );							 /* 启动操作的第1个串口同步码 */
	CH395_Send_Byte( SER_SYNC_CODE2 );							 /* 启动操作的第2个串口同步码 */
	CH395_Send_Byte( mCmd );							 		 /* 输出命令码 */
	mDelayuS( 2 );  											 /* 延时2uS确保读写周期大于2uS */
}

/*******************************************************************************
* Function Name  : xWriteCH395Data
* Description    : 向CH395写数据
* Input          : mData---将要写入CH395的数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Data( UINT8 mData ) 
{
	CH395_Send_Byte( mData );  								 /* 串口输出 */
}

/*******************************************************************************
* Function Name  : xReadCH395Data
* Description    : 从CH395读数据
* Input          : None
* Output         : None
* Return         : 返回读取的数据
*******************************************************************************/
UINT8 xReadCH395Data( void )  
{
	UINT32 i;
	
	for( i = 0; i < 0xFFFFF0; i ++ ) 
	{  
		/* 计数防止超时 */		
#if CHIP_USED == USE_CHIP_GD32
		if( usart_flag_get(USART1, USART_FLAG_RBNE) != RESET ) 
		{  
			/* 串口接收到 */
			return( (UINT8)usart_data_receive(USART1) );/* 返回数据 */
		}
#else
		if( USART2->SR & USART_FLAG_RXNE ) 
		{  
			/* 串口接收到 */
			return( ( UINT8 )( USART2->DR & (UINT16)0x01FF ) );/* 返回数据 */
		}
#endif
	}
	return( 0 );  												 /* 不应该发生的情况 */
}



/*******************************************************************************
* Function Name  : Query395Interrupt
* Description    : 查询CH395中断(INT#低电平)
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
UINT8 Query395Interrupt( void )
{
#if CHIP_USED == USE_CHIP_GD32
	return( gpio_input_bit_get(GPIOA, GPIO_PIN_1) ? FALSE : TRUE );
#else
	return( CH395_INT_PIN_WIRE( ) ? FALSE : TRUE );
#endif
}
