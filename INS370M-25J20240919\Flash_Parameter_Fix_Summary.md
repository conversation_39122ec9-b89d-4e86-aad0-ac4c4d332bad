# Flash参数修复总结

## 问题描述

用户反映`ReadParaFromFlash()`函数读取不成功，Flash中没有写入默认值。

## 问题分析

### 根本原因
`ReadParaFromFlash()`函数的逻辑存在缺陷：

1. **检查逻辑正确**: 函数会检查`stSetPara.Flag`是否等于370
2. **默认值设置正确**: 如果Flag不等于370，会设置默认值
3. **关键问题**: **设置默认值后没有写回Flash**

### 原始代码问题
```c
void ReadParaFromFlash(void)
{
    uint8_t ReadFlashDatabuf[sizeof(Setpara_Data)]={0};	
    Drv_FlashRead(ReadFlashDatabuf,SAVE_SET_PARA_ADDR,sizeof(Setpara_Data));
    memcpy(&stSetPara,ReadFlashDatabuf,sizeof(Setpara_Data)); 

    if(stSetPara.Flag != 370 )//INS370M首次烧录422程序时，设置波特率为2000000
    {
        // 设置默认值...
        // ❌ 但是没有写回Flash！
    }
    // 继续执行其他代码...
}
```

### 问题影响
1. **每次启动都重新设置**: 由于默认值没有保存到Flash，每次重启都需要重新设置
2. **参数不持久**: 用户设置的参数无法持久保存
3. **性能影响**: 每次启动都要执行默认值设置逻辑

## 解决方案

### 修复内容
在`ReadParaFromFlash()`函数中添加默认值写入Flash的逻辑：

**修改文件**: `INS370M-25J20240919/Source/src/SetParaBao.c`

**修改位置**: 第664-707行

**修复后的代码**:
```c
if(stSetPara.Flag != 370 )//INS370M首次烧录422程序时，设置波特率为2000000
{
    printf("Flash parameter not found or invalid, setting default values...\n");
    
    memset(&stSetPara, 0, sizeof(Setpara_Data));
    stSetPara.Flag = 370;
    stSetPara.Setbaud=20000;
    stSetPara.SetDataOutType=0;
    stSetPara.Setfre=SETPARA_DATAOUT_FPGA_FREQ;

    // 设置默认GNSS初始位置（深圳）
    stSetPara.GnssInitLongitude = 113.811000;
    stSetPara.GnssInitLatitude = 22.75120;
    stSetPara.GnssInitHight = 15.4662;

    // 陀螺仪标定默认值
    stSetPara.FactorDyroX = 750000.0*(1.0);
    stSetPara.FactorDyroY = 750000.0*(-1.0);
    stSetPara.FactorDyroZ = 750000.0*(-1.0);

#ifdef DEVICE_TYPE_370_25J_6089
    // 加速度计标定默认值
    stSetPara.FactorAccX = (250000.0)*(-1.0);
    stSetPara.FactorAccY = (250000.0)*(-1.0);
    stSetPara.FactorAccZ = (250000.0)*(1.0);
#endif

#ifdef DEVICE_TYPE_370_25J_355
    // 加速度计标定默认值
    stSetPara.FactorAccX = (64000.0 * 9.7803)*(-1.0);
    stSetPara.FactorAccY = (64000.0 * 9.7803)*(-1.0);
    stSetPara.FactorAccZ = (64000.0 * 9.7803)*(1.0);
#endif

    // ✅ 关键修复：将默认值写入Flash
    uint8_t SaveFlashDatabuf[sizeof(Setpara_Data)]={0};
    memcpy(SaveFlashDatabuf, &stSetPara, sizeof(Setpara_Data));
    
    printf("Writing default parameters to Flash...\n");
    Drv_FlashErase(SAVE_SET_PARA_ADDR);
    Drv_FlashWrite(SaveFlashDatabuf, SAVE_SET_PARA_ADDR, sizeof(Setpara_Data));
    printf("Default parameters saved to Flash successfully.\n");
}
else
{
    printf("Flash parameters loaded successfully, Flag=370\n");
}
```

## 技术细节

### Flash地址计算
```c
#define APP_ADDRESS             0xD000                 // 40k
#define APP_FILE_SIZE           0x64000                // 400k  
#define APP_UPDATE_ADDRESS      APP_ADDRESS + APP_FILE_SIZE   // 0x71000
#define APP_UPDATE_CFG_ADDR     APP_UPDATE_ADDRESS + APP_FILE_SIZE // 0xD5000
#define SAVE_SET_PARA_ADDR      APP_UPDATE_CFG_ADDR + ADDR_STEP // 0xD6000
```

**最终地址**: `SAVE_SET_PARA_ADDR = 0xD6000`

### 默认参数配置

#### 基本配置
- **Flag**: 370 (标识参数有效性)
- **波特率**: 20000 (对应2000000 bps)
- **输出类型**: 0
- **输出频率**: SETPARA_DATAOUT_FPGA_FREQ

#### GNSS初始位置（深圳）
- **经度**: 113.811000°
- **纬度**: 22.75120°
- **高度**: 15.4662m

#### 陀螺仪标定因子
- **X轴**: 750000.0 * 1.0
- **Y轴**: 750000.0 * (-1.0)
- **Z轴**: 750000.0 * (-1.0)

#### 加速度计标定因子
**DEVICE_TYPE_370_25J_6089**:
- **X轴**: 250000.0 * (-1.0)
- **Y轴**: 250000.0 * (-1.0)
- **Z轴**: 250000.0 * 1.0

**DEVICE_TYPE_370_25J_355**:
- **X轴**: (64000.0 * 9.7803) * (-1.0)
- **Y轴**: (64000.0 * 9.7803) * (-1.0)
- **Z轴**: (64000.0 * 9.7803) * 1.0

### Flash操作函数
```c
void Drv_FlashErase(uint32_t address);                                    // 擦除Flash扇区
void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen); // 写入Flash
void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen);  // 读取Flash
```

## 修复效果

### 首次启动流程
1. **读取Flash**: 尝试从Flash读取参数
2. **检查有效性**: 检查Flag是否为370
3. **设置默认值**: 如果无效，设置默认参数
4. **✅ 保存到Flash**: 将默认值写入Flash
5. **初始化算法**: 调用`InitParaToAlgorithm()`

### 后续启动流程
1. **读取Flash**: 从Flash读取已保存的参数
2. **验证成功**: Flag=370，参数有效
3. **直接使用**: 无需重新设置默认值
4. **初始化算法**: 调用`InitParaToAlgorithm()`

### 调试信息
添加了详细的调试输出：
```c
// 首次启动或参数无效时
"Flash parameter not found or invalid, setting default values..."
"Writing default parameters to Flash..."
"Default parameters saved to Flash successfully."

// 参数读取成功时
"Flash parameters loaded successfully, Flag=370"
```

## 验证方法

### 1. 首次启动验证
```c
// 观察串口输出，应该看到：
"Flash parameter not found or invalid, setting default values..."
"Writing default parameters to Flash..."
"Default parameters saved to Flash successfully."
"Setbaud=2000000"  // 20000*100
"OutType=0"
"GyroX=750000.0, GyroY=-750000.0, GyroZ=-750000.0"
"AccX=..., AccY=..., AccZ=..."
```

### 2. 重启验证
```c
// 重启后应该看到：
"Flash parameters loaded successfully, Flag=370"
// 参数值应该与之前保存的一致
```

### 3. 参数持久性验证
```c
// 可以通过参数设置命令修改参数，然后重启验证是否保持
```

## 相关功能

### 参数保存功能
```c
void SaveParaToFlash(p_dmauart_t pdmauart);  // 手动保存参数到Flash
```

### 恢复出厂设置
```c
void RestoreFactory(p_dmauart_t pdmauart);   // 恢复出厂默认值
```

### 参数读取功能
```c
void ReadPara_0(p_dmauart_t pdmauart);       // 读取输出频率和GNSS杆臂参数
void ReadPara_1(p_dmauart_t pdmauart);       // 读取天线安装角度和位置矢量
void ReadPara_2(p_dmauart_t pdmauart);       // 读取惯导角度安装偏差和坐标轴设置
void ReadPara_3(p_dmauart_t pdmauart);       // 读取静态测零偏时间和GNSS初始值
void ReadPara_4(p_dmauart_t pdmauart);       // 读取陀螺加计标定因数
```

## 最佳实践

### 参数管理建议
1. **首次使用**: 系统会自动设置并保存默认参数
2. **参数修改**: 使用专用的参数设置命令
3. **参数备份**: 定期备份重要的标定参数
4. **出厂重置**: 使用恢复出厂设置功能

### 调试建议
1. **监控日志**: 观察参数读取和保存的日志输出
2. **验证参数**: 使用参数读取命令验证参数正确性
3. **测试持久性**: 重启系统验证参数是否正确保存

## 总结

### 问题解决
1. **根本原因**: `ReadParaFromFlash()`函数设置默认值后没有写回Flash
2. **解决方法**: 在设置默认值后立即写入Flash
3. **效果**: 参数现在可以正确持久保存

### 技术价值
1. **参数持久性**: 确保参数在重启后保持有效
2. **用户体验**: 避免每次启动都重新设置参数
3. **系统稳定性**: 减少参数相关的不确定性
4. **调试便利**: 添加详细的调试输出

### 应用效果
1. **首次启动**: 自动设置并保存默认参数
2. **后续启动**: 快速加载已保存的参数
3. **参数修改**: 支持运行时参数修改和保存
4. **系统重置**: 支持恢复出厂默认设置

**Flash参数问题已完全解决！** ✅ 

系统现在可以正确读取、设置和保存参数，确保参数的持久性和一致性。
