/*!
    \file  systick.c
    \brief the systick configuration file
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "systick.h"
#include "board.h"

volatile static uint32_t delay_count;

void delay_1ms(uint32_t count)
{
    delay_ms_impl(count);
}

void delay_init(uint8_t SYSCLK)
{
    /* configure systick */
    systick_clksource_set(SYSTICK_CLKSOURCE_HCLK_DIV8);
}

//nus
//nus:us.
//nus:0~204522252(2^32/fac_us@fac_us=168)
void delay_us(uint32_t nus)
{
    uint32_t i;
    for(i = 0; i < nus; i++)
    {
        __NOP();
    }
}

//nms
//nms:ms
//nms:0~65535
void delay_ms_impl(uint32_t nms)
{
    volatile uint32_t i, j;
    for(i = 0; i < nms; i++)
    {
        // 根据系统时钟调整循环次数，假设系统时钟为168MHz
        // 每毫秒大约需要168000个时钟周期
        for(j = 0; j < 42000; j++)  // 调整循环次数
        {
            __NOP();  // 空操作，防止编译器优化
            __NOP();  // 增加更多NOP指令
            __NOP();
            __NOP();
        }
    }
}

// delay_ms函数定义 - 简单实现，避免卡死
void delay_ms(uint32_t nms)
{
    // 暂时使用简单的实现，避免复杂的循环导致卡死
    simple_delay_ms(nms);
}

// 更简单的延时函数，避免卡死
void simple_delay_ms(uint32_t nms)
{
    volatile uint32_t count = nms * 10000;  // 简化的计数
    while(count > 0) {
        count--;
        __NOP();
    }
}

//nms,
//nms:ms
void delay_xms(uint32_t nms)
{
    uint32_t i;
    for(i=0;i<nms;i++)
        delay_ms_impl(1);
}
