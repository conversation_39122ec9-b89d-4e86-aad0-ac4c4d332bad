/*!
    \file  systick.c
    \brief the systick configuration file
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "systick.h"
// 暂时不包含board.h，避免可能的宏定义冲突
// #include "board.h"

volatile static uint32_t delay_count;

// 最简单的delay_ms实现，避免任何复杂性
void my_delay_ms(uint32_t nms)
{
    volatile uint32_t i, j;
    for(i = 0; i < nms; i++) {
        for(j = 0; j < 50000; j++) {
            __NOP();
        }
    }
}

// delay_ms函数 - 调用内部实现
void delay_ms(uint32_t nms)
{
    my_delay_ms(nms);
}

void delay_1ms(uint32_t count)
{
    delay_ms_impl(count);
}

void delay_init(uint8_t SYSCLK)
{
    /* configure systick */
    systick_clksource_set(SYSTICK_CLKSOURCE_HCLK_DIV8);
}

//nus
//nus:us.
//nus:0~204522252(2^32/fac_us@fac_us=168)
void delay_us(uint32_t nus)
{
    volatile uint32_t i;
    for(i = 0; i < nus * 50; i++)
    {
        __NOP();
    }
}

//nms
//nms:ms
//nms:0~65535
void delay_ms_impl(uint32_t nms)
{
    volatile uint32_t i, j;
    for(i = 0; i < nms; i++)
    {
        // 根据系统时钟调整循环次数，假设系统时钟为168MHz
        // 每毫秒大约需要168000个时钟周期
        for(j = 0; j < 42000; j++)  // 调整循环次数
        {
            __NOP();  // 空操作，防止编译器优化
            __NOP();  // 增加更多NOP指令
            __NOP();
            __NOP();
        }
    }
}

//nms,
//nms:ms
void delay_xms(uint32_t nms)
{
    volatile uint32_t i;
    for(i=0;i<nms;i++)
        delay_ms_impl(1);
}
