#ifndef ____CAN_DATA_H____
#define ____CAN_DATA_H____

#include "gd32f4xx.h"
#include "data_convert.h"

typedef struct can_data_t
{
	float timestamp;					/* 时间戳, 单位:s , 精度：0.0001*/
	float WheelSpeed_Front_Left;		/* 轮速 左前, 单位: m/s, 精度：待定*/
	float WheelSpeed_Front_Right;		/* 轮速 右前, 单位: m/s, 精度：待定*/
	float WheelSpeed_Back_Left;			/* 轮速 左后, 单位: m/s, 精度：待定*/
	float WheelSpeed_Back_Right;		/* 轮速 右后, 单位: m/s, 精度：待定*/
	float WheelSteer;					/* 方向盘, 单位: °, 精度：待定*/
	float OdoPulse_1;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
	float OdoPulse_2;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
	uint8_t Gear;						/* 汽车档位 */
	
}CcanDataTypeDef;

extern CcanDataTypeDef hCanData;


#endif //____CAN_DATA_H____

