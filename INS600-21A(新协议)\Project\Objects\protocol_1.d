.\objects\protocol_1.o: ..\Protocol\protocol.c
.\objects\protocol_1.o: ..\Protocol\computerFrameParse.h
.\objects\protocol_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\protocol_1.o: ..\Library\CMSIS\core_cm4.h
.\objects\protocol_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\protocol_1.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\protocol_1.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\protocol_1.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\protocol_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\protocol_1.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\protocol_1.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\protocol_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\protocol_1.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\protocol_1.o: ..\Protocol\config.h
.\objects\protocol_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\protocol_1.o: ..\Protocol\algorithm.h
.\objects\protocol_1.o: ..\Protocol\protocol.h
.\objects\protocol_1.o: ..\Protocol\serial.h
.\objects\protocol_1.o: ..\Protocol\insdef.h
.\objects\protocol_1.o: ..\Protocol\uartadapter.h
.\objects\protocol_1.o: ..\Protocol\UartDefine.h
.\objects\protocol_1.o: ..\Source\inc\gnss.h
.\objects\protocol_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\protocol_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\protocol_1.o: ..\Common\inc\data_convert.h
.\objects\protocol_1.o: ..\Protocol\frame_analysis.h
.\objects\protocol_1.o: ..\NAV\nav.h
.\objects\protocol_1.o: ..\NAV\nav_type.h
.\objects\protocol_1.o: ..\NAV\nav_const.h
