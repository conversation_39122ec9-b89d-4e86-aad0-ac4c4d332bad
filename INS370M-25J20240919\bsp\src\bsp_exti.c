#include "bsp_exti.h"

EXTI_TypeDef hEXTI[EXTI_CNT_MAX]= {
	//{ RCU_GPIOA, GPIOA, GPIO_PIN_7, EXTI5_9_IRQn, EXTI_SOURCE_GPIOA, EXTI_SOURCE_PIN7, EXTI_7, EXTI_TRIG_FALLING },				// ARM 1 to ARM 2 INT
	//{ RCU_GPIOF, GPIOF, GPIO_PIN_11, EXTI10_15_IRQn, EXTI_SOURCE_GPIOF, EXTI_SOURCE_PIN11, EXTI_11, EXTI_TRIG_RISING },		// FPGA INT     F11   622-2A  2023/10/24
	{ RCU_GPIOE, GPIOE, GPIO_PIN_3, EXTI3_IRQn, EXTI_SOURCE_GPIOE, EXTI_SOURCE_PIN3, EXTI_3, EXTI_TRIG_RISING },		// FPGA INT     PE3   912-2A  2024/09/19
	{ RCU_GPIOA, GPIOA, GPIO_PIN_9, EXTI5_9_IRQn, EXTI_SOURCE_GPIOA, EXTI_SOURCE_PIN9, EXTI_9,EXTI_TRIG_FALLING },				// CAN1 ����  INT
	{ RCU_GPIOB, GPIOB, GPIO_PIN_14, EXTI10_15_IRQn, EXTI_SOURCE_GPIOB, EXTI_SOURCE_PIN14, EXTI_14,EXTI_TRIG_FALLING }			// CAN2 ����  INT
};

void bsp_exti_config(EXTI_TypeDef* hexti)
{
	/* enable the key clock */
	rcu_periph_clock_enable(hexti->exti_rcu);
	rcu_periph_clock_enable(RCU_SYSCFG);
	
	/* configure button pin as input */
	gpio_mode_set(hexti->gpio, GPIO_MODE_INPUT, GPIO_PUPD_NONE,hexti->pin);
	
	/* enable and set key EXTI interrupt to the lowest priority */
	nvic_irq_enable(hexti->IRQn, 2U, 2U);

	/* connect key EXTI line to key GPIO pin */
	syscfg_exti_line_config(hexti->exti_port_source, hexti->exti_pin_source);

	//nvic_irq_disable(EXTI10_15_IRQn);
	
	/* configure key EXTI line */
	exti_init(hexti->exti_line, EXTI_INTERRUPT, hexti->edge);
	exti_interrupt_flag_clear(hexti->exti_line);
}

void bsp_exti_init(void)
{
	int i = 0;
	for(i = 0; i < EXTI_CNT_MAX; i++)
		bsp_exti_config(&hEXTI[i]);
}


