#include "bsp_adc.h"
#include "systick.h"



void adc_config(void)
{
	/* enable GPIOC clock */
	rcu_periph_clock_enable(RCU_GPIOF);
	/* enable ADC clock */
	rcu_periph_clock_enable(RCU_ADC2);
	/* config ADC clock */
	adc_clock_config(ADC_ADCCK_PCLK2_DIV8);
	
	gpio_mode_set(ADC_GPIO_PORT,GPIO_MODE_ANALOG,GPIO_PUPD_NONE,ADC_GPIO_PIN);
	/* ADC mode config */
	adc_sync_mode_config(ADC_SYNC_MODE_INDEPENDENT);
	/* ADC continous and scan function enable */
	adc_special_function_config(ADC2, ADC_SCAN_MODE, ENABLE);
	adc_special_function_config(ADC2, ADC_CONTINUOUS_MODE, ENABLE);
	/* ADC data alignment config */
	adc_data_alignment_config(ADC2, ADC_DATAALIGN_RIGHT);

	/* ADC regular channel config */
	adc_regular_channel_config(ADC2,0,<PERSON><PERSON>RD_ADC_CHANNEL,ADC_SAMPLETIME_56);
	/* ADC external trigger enable */
	adc_external_trigger_config(ADC2,ADC_REGULAR_CHANNEL,EXTERNAL_TRIGGER_DISABLE);

	/* ADC analog watchdog threshold config */
	adc_watchdog_threshold_config(ADC2,1015,1805);
	/* ADC analog watchdog single channel config */
	adc_watchdog_single_channel_enable(ADC2,BOARD_ADC_CHANNEL);
	/* ADC analog watchdog enable on regular channel group*/
	adc_watchdog_group_channel_enable(ADC2,ADC_REGULAR_CHANNEL);

	/* ADC interrupt config */
	adc_interrupt_enable(ADC2, ADC_INT_WDE);

	/* enable ADC interface */
	adc_enable(ADC2);
	/* wait for ADC stability */
	delay_ms(1);
	/* ADC calibration and reset calibration */
	adc_calibration_enable(ADC2);

	/* ADC software trigger enable */
	adc_software_trigger_enable(ADC2,ADC_REGULAR_CHANNEL);
	
	nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);
	nvic_irq_enable(ADC_IRQn, 2, 0);
}

void enter_sleep_mode(void)
{
	rcu_periph_clock_enable(RCU_PMU);
	rcu_bkp_reset_enable();
}

