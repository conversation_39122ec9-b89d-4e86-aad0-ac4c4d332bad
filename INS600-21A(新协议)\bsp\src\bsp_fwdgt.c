#include "bsp_fwdgt.h"


void bsp_fwdgt_init(void)
{
	/* enable IRC32K */
	rcu_osci_on(RCU_IRC32K);
	/* wait till IRC32K is ready */
	while(SUCCESS != rcu_osci_stab_wait(RCU_IRC32K)){
	}
	/* confiure FWDGT counter clock: 32KHz(IRC32K) / 32 = 1 KHz == 1000Hz */
	/* period = 1 / FWDGT counter clock 1000Hz * FWDGT reload value 200 = 0.1s = 200ms */
	fwdgt_config(200,FWDGT_PSC_DIV32);
	/* After 2 seconds to generate a reset */
	fwdgt_enable();
#if 0
	/* check if the system has resumed from FWDGT reset */
	if (RESET != rcu_flag_get(RCU_FLAG_FWDGTRST)){
		/* clear the FWDGT reset flag */
		rcu_all_reset_flag_clear();
	}
#endif
}

void bsp_fwdgt_feed(void)
{
	/* reload FWDGT counter */
	fwdgt_counter_reload();
}

