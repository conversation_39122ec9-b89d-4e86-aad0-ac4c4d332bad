/*!
    \file  INS_Init.c
    \brief led spark with systick 
*/
#ifndef  __GOL_ALGORITHM_C__
#define  __GOL_ALGORITHM_C__
#include "appmain.h"
//#include "nav_includes.h"
//#include "frame_analysis.h"
#include "gdtypedefine.h"
#include "INS912AlgorithmEntry.h"
//#include "api_ch392.h"

#include "ins.h"
#include "uart.h"
#include "flash.h"
#include "bsp_gpio.h"
#include "bsp_exti.h"
#include "bsp_flash.h"
#include "drv_gpio.h"
#include "pin_numbers_def.h"
#include "SetParaBao.h"

// 临时解决方案：直接包含SetParaBao.c和FirmwareUpdateFile.c文件以确保其被编译
// 这不是最佳实践，但可以解决链接问题
#include "../src/SetParaBao.c"
#include "../src/FirmwareUpdateFile.c"


uint32_t g_bmp280_measTime;
uint8_t g_usb_ready;
char g_logFileName[256] = {0};
LogBufTypeDef g_LogBuf;
LogBufTypeDef* g_pLogBuf = &g_LogBuf;
int g_networkState = -1;
uint8_t fisrtTimeGNSSTimeSync = 1;
uint8_t fpga_syn = 0;//fpga同步
uint32_t fpga_syn_count =0;//fpga_syn硬触发计数
uint32_t fpga_loop_count =0;//fpga_syn主循环获取数据帧计数
uint8_t fpga_syn_flag = 0;//fpga同步标志位
int gcan0_rx_syn = 0;//can0 synchronization
int g_gpsWeek;							//GPS周内秒		周
double g_gpsSecond;						//GPS周内秒		秒
//unsigned short gfpgadata[200];
uint32_t g_CAN_Timeout_Start_flag = 0;
uint32_t g_CAN_Timeout_Cnt = 0;

uint8_t g_CAN_Count_Last = 0;

uint8_t g_KF_OutData_Rx_Flag;

//unsigned int gprotocol_send_baudrate = BAUD_RATE_921600;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
float calcGPRMC_TRA(char *pchar);
void wheel_is_running(void);
void NAV_Output(void);
void INS912_Output(navoutdata_t *pnavout);


char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
double get_16bit_D64(unsigned short *msgbuff)
{        
	#if 1
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    m16_uMemory.bd[2] = msgbuff[2];
    m16_uMemory.bd[3] = msgbuff[3];
	#else
    m16_uMemory.bd[0] = msgbuff[3];
    m16_uMemory.bd[1] = msgbuff[2];
    m16_uMemory.bd[2] = msgbuff[1];
    m16_uMemory.bd[3] = msgbuff[0];
	#endif
    return m16_uMemory.dv;
}
float get_16bit_D32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}

unsigned int  get_16bit_Int32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.iv;
}



void gd_eval_com_init_basic(void);
void gd_eval_com_init(uint32_t com, uint32_t baudval);
void gd_eval_com_init6(uint32_t com, uint32_t baudval);

// GD32F4xx平台适配函数
void Exti_Init(void)
{
    // GD32F4xx平台的外部中断初始化
    bsp_exti_init();

    // 注意：FPGA中断处理函数已在bsp_exti.c中的EXTI3_IRQHandler中实现
    // PE3引脚配置为EXTI3，上升沿触发
    // printf("FPGA interrupt configured on PE3 (EXTI3)\n");  // 禁用printf
}

int norflash_init(void)
{
    // GD32F4xx平台的NOR Flash初始化
    // 这里可以初始化外部NOR Flash，如果没有外部Flash，可以使用内部Flash
    // 对于GD32F4xx，通常使用内部Flash
    fmc_unlock();
    return 0;
}

void Fatfs_Init(void)
{
    // GD32F4xx平台的FatFS初始化
    // 这里需要初始化SD卡和文件系统
    // 具体实现需要根据硬件配置
    // printf("FatFS initialization for GD32F4xx\n");  // 禁用printf
}

void INS_Init(void)
{
	// printf("=== Starting INS_Init ===\n");  // 禁用printf

	// printf("Step 1: Basic init...\n");  // 禁用printf
	// delay_init(200);  // 暂时注释掉，避免delay相关函数问题
	initializationdriversettings();

	// printf("Step 2: GPIO/Timer init...\n");  // 禁用printf
	// 暂时注释掉PA0的PWM配置，因为PA0没有连接到FPGA
	// 配置PA0为TIMER1_CH0的复用功能（FPGA时钟输出）
	// rcu_periph_clock_enable(RCU_GPIOA);
	// gpio_af_set(PWM_IO_PORT, GPIO_AF_1, PWM_IO_PIN);  // TIMER1_CH0复用功能
	// gpio_mode_set(PWM_IO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, PWM_IO_PIN);
	// gpio_output_options_set(PWM_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, PWM_IO_PIN);
	// printf("PA0 PWM configuration skipped (not connected to FPGA)\n");  // 禁用printf

	// printf("Step 3: BSP init...\n");  // 禁用printf
	bsp_gpio_init();

	// printf("Step 4: Timer init...\n");  // 禁用printf
	bsp_tim_init();

	// printf("Step 5: PWM check...\n");  // 禁用printf
	// 跳过PWM检查，因为没有初始化TIMER1
	// printf("PWM check skipped (TIMER1 not initialized)\n");  // 禁用printf

	// printf("Step 6: Flash init...\n");  // 禁用printf
	InitFlashAddr(0);

	// printf("Step 7: Power management...\n");  // 禁用printf
	UM982_PowerON();
	Z_AXIS_5V_PowerON();
	MEMS_3V3_PowerON();
	ARM1_PowerON();
	// delay_ms(100);  // 暂时注释掉，避免delay_ms函数问题

	// printf("Step 8: EXMC init...\n");  // 禁用printf
	exmc_asynchronous_sram_init();

	// printf("Step 9: FPGA wait...\n");  // 禁用printf
	// delay_ms(500);  // 暂时注释掉，避免delay_ms函数问题

	// printf("Step 10: Basic EXMC test...\n");  // 禁用printf
	// 暂时注释掉EXMC访问，先确保其他部分正常
	// volatile unsigned short test_val = *(unsigned short*)0x60000000;
	// printf("EXMC read: 0x%04X\n", test_val);
	// printf("EXMC test skipped for now\n");  // 禁用printf

	// printf("Step 11: Skip FPGA data test for now...\n");  // 禁用printf
	// 暂时注释掉FPGA数据读取
	// extern void get_fpgadata_do(void);
	// extern unsigned short gfpgadata[200];
	// get_fpgadata_do();
	// printf("FPGA data test skipped for now\n");  // 禁用printf

	// printf("Step 12: Exti init...\n");  // 禁用printf
	Exti_Init();

	// printf("Step 13: Skip Fatfs for now...\n");  // 禁用printf
	// Fatfs_Init();  // 暂时跳过，可能有问题

	// printf("Step 14: Skip Norflash for now...\n");  // 禁用printf
	// norflash_init();  // 暂时跳过，可能有问题

	// printf("Step 15: Memory init...\n");  // 禁用printf
	memset(&hINSData,0,sizeof(hINSData));
	memset(&gdriverdatalist,0,sizeof(gdriverdatalist));

	// printf("Step 16: Read parameters from flash...\n");  // 禁用printf
	ReadParaFromFlash();  // 读取参数，初始化stSetPara变量

	// printf("Step 17: UART init...\n");  // 禁用printf
	gd_eval_com_init_basic();

	// printf("Step 18: UART4/UART6 interrupt init...\n");  // 禁用printf
	// UART4中断初始化
	nvic_irq_enable(UART4_IRQn, 0, 0);
	gd_eval_com_init(UART4, stSetPara.Setbaud*100);  // 使用配置的波特率初始化UART4
	usart_interrupt_enable(UART4, USART_INT_RBNE);

	// UART6中断初始化
	nvic_irq_enable(UART6_IRQn, 0, 0);
	gd_eval_com_init6(UART6, gprotocol_send_baudrate6);  // 使用配置的波特率初始化UART6
	usart_interrupt_enable(UART6, USART_INT_RBNE);

#ifdef DEVICE_ACC_TYPE_ADLX355
	// printf("Step 17: Skip ADXL355 for now...\n");  // 禁用printf
	// ADXL355_UART7_Init();  // 暂时跳过，可能有问题
#endif

	//printf("Step 18: Final setup...\n");
	g_LEDIndicatorState = LED_STATE_WHEEL_ERR_INIT_OK;
	// delay_ms(100);  // 暂时注释掉，避免delay_ms函数问题

	// printf("=== INS_Init COMPLETED SUCCESSFULLY ===\n");  // 禁用printf

	// 使用LED闪烁指示初始化完成 - 暂时跳过LED操作
	// 快速闪烁3次表示初始化成功
	
}

void GetChipID(void)
{
	hSetting.ChipID[0] = *(( volatile uint32_t * )0x1FFF7A10);
	hSetting.ChipID[1] = *(( volatile uint32_t * )0x1FFF7A14);
	hSetting.ChipID[2] = *(( volatile uint32_t * )0x1FFF7A18);
	hDefaultSetting.ChipID[0] = hSetting.ChipID[0];
	hDefaultSetting.ChipID[1] = hSetting.ChipID[1];
	hDefaultSetting.ChipID[2] = hSetting.ChipID[2];
}


int checkUSBReady(void)
{
//	uint8_t  status;
//	int i = 0;
//	for( i = 0; i < 10; i ++ )
//	{
//		status = CH378DiskReady( );		/* 初始化磁盘并测试磁盘是否就绪 */
//		if( status == ERR_SUCCESS ) 
//		{
////			CH378HardwareReset();
//			return 1;					/* 准备好 */
//		}
//		else if( status == ERR_DISK_DISCON ) 
//		{
//			return 0;					/* 检测到断开,重新检测并计时 */
//		}
//		if( CH378GetDiskStatus( ) >= DEF_DISK_MOUNTED && i >= 5 ) 
//		{
//			return 0;					/* 有的U盘总是返回未准备好,不过可以忽略,只要其建立连接MOUNTED且尝试5*50mS */
//		}
//	}
//	return 0;
}

//int queryDiskCapacity(void)
//{
//	UINT8 status;
//	status = CH378DiskCapacity(cap);
//}

void loggingLogFile(void* arg)
{
	LogBufTypeDef* pBuf = (LogBufTypeDef*)arg;
	
	unsigned char logfileName[128] = {0};
	memset(logfileName,0,128);
	generateCSVLogFileName((char*)logfileName);
	writeCSVLog(logfileName,pBuf);
}

/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
	// 完全禁用printf输出，直接返回
	// 这样可以避免USART相关的问题导致程序卡死
	return ch;
}

//读取FPGA数据
void StartNavigation(void)
{
	//通知ARM1进行卡尔曼滤波解算
	//ARM2_OUTPUT_ARM1_High();
	//delay_us(5);
	//ARM2_OUTPUT_ARM1_Low();

}

void StopNavigation(void)
{
	
}

void LEDIndicator(uint8_t state)
{
	// 调试版本 - 如果这个函数被调用，我们需要知道
	static uint32_t call_count = 0;
	call_count++;

	// printf("*** LEDIndicator called! count=%d, state=%d ***\n", (int)call_count, state);  // 禁用printf

	// 立即返回，不做任何LED操作
	return;
}

/*!
    \brief      configure COM port basic initialization
    \param[in]  none
    \param[out] none
    \retval     none
*/
void gd_eval_com_init_basic(void)
{
	UartIrqInit();
        SDUartIrqInit();
}

/*!
    \brief      configure COM port with parameters
    \param[in]  com: COM port
    \param[in]  baudval: baud rate value
    \param[out] none
    \retval     none
*/
void gd_eval_com_init(uint32_t com, uint32_t baudval)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable( RCU_GPIOC);
    rcu_periph_clock_enable( RCU_GPIOD);

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_UART4);

    /* connect port to USARTx_Tx */
    gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_12);

    /* connect port to USARTx_Rx */
    gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_2);

    /* configure USART Tx as alternate function push-pull */
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_12);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

    /* configure USART Rx as alternate function push-pull */
    gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_2);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);

    /* USART configure */
    usart_deinit(com);
    usart_baudrate_set(com, baudval);
    usart_receive_config(com, USART_RECEIVE_ENABLE);
    usart_transmit_config(com, USART_TRANSMIT_ENABLE);
    usart_enable(com);
}

/*!
    \brief      configure COM6 port with parameters
    \param[in]  com: COM port
    \param[in]  baudval: baud rate value
    \param[out] none
    \retval     none
*/
void gd_eval_com_init6(uint32_t com, uint32_t baudval)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable( RCU_GPIOF);

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_UART6);

    /* connect port to USARTx_Tx */
    gpio_af_set(GPIOF, GPIO_AF_8, GPIO_PIN_7);

    /* connect port to USARTx_Rx */
    gpio_af_set(GPIOF, GPIO_AF_8, GPIO_PIN_6);

    /* configure USART Tx as alternate function push-pull */
    gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_7);
    gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_7);

    /* configure USART Rx as alternate function push-pull */
    gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_6);
    gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_6);

    /* USART configure */
    usart_deinit(com);
    usart_baudrate_set(com, baudval);
    usart_receive_config(com, USART_RECEIVE_ENABLE);
    usart_transmit_config(com, USART_TRANSMIT_ENABLE);
    usart_enable(com);
}


float calcGPRMC_TRA(char *pchar)
{
	char tmpchar;
	float multiplier = 1.0;
	int i, bfind = 0;
	float tra = 0.0;
	for (i = 0; i < 5; i++) {
		if (*(pchar + i) == '.') {
			bfind = 1;
			break;
		}
	}
	if (bfind) {
		tmpchar = *(pchar + i + 1);
		if (tmpchar < '0' || tmpchar > '9')	return	888.8;
		else tra = (tmpchar - '0') * 0.1;
		
		for (i-- ; i >= 0; i--) {
			tmpchar = *(pchar + i);
			if (tmpchar < '0' || tmpchar > '9')	break;
			else tra += (tmpchar - '0') * multiplier;
			//multiplier *= 10.0;
		}
		return tra;
	}
	return 999.9;
}

void sys_irq_stop(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_DISABLE);
}

void sys_irq_restart(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_ENABLE);
}
#endif


