/*!
    \file  main.h
    \brief the header file of main 
*/
#ifndef __MAIN_H
#define __MAIN_H

#include "gd32f4xx.h"
#include "stdint.h"

// 函数声明
void SysInit(void);
void DeviceInit(void);
void AlgorithmDo(void);
void loopDoOther(void);
void analysisRxdata(void);
void SdFileWriteOperate(void);
void SdFileReadOperate(void);
void ff_handle_poll(void);
void sduart_recv_polling(void);
void l355_uart_recv_polling(void);
void Led_Control(void);
// delay_ms函数已在systick.h中声明，此处删除避免重复声明
void ReadParaFromFlash(void);

// 外部变量声明
extern uint8_t fpga_syn;
extern uint8_t uart4_sendImuFlag;
extern uint8_t Original_Data;

#endif /* __MAIN_H */
