/********************************** (C) COPYRIGHT *******************************
* File Name          : FILE_SYS.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/11/25
* Description        : CH378芯片 文件系统层 V1.2
*                      提供文件系统常用子程序,提供命令打包
*******************************************************************************/

/*******************************************************************************/
/* 头文件包含 */
#include "FILE_SYS.H"

/*******************************************************************************/
/* 变量定义 */
UINT16 SectorSize = DEF_SECTOR_SIZE;

/*******************************************************************************/
/* 函数定义 */

/*******************************************************************************
* Function Name  : CH378Read32bitDat
* Description    : 从CH378芯片读取32位的数据并结束命令
* Input          : None
* Output         : None
* Return         : 32位数据
*******************************************************************************/
UINT32 CH378Read32bitDat( void )
{
	UINT32 dat;
	dat = xReadCH378Data();
	dat |= (UINT32)xReadCH378Data() << 8;
	dat |= (UINT32)xReadCH378Data() << 16;
	dat |= (UINT32)xReadCH378Data() << 24;
	xEndCH378Cmd();
	return dat;
}

/*******************************************************************************
* Function Name  : CH378ReadVar8
* Description    : 读CH378芯片内部的8位变量
* Input          : var---变量地址
* Output         : None
* Return         : 8位数据
*******************************************************************************/
UINT8 CH378ReadVar8( UINT8 var )
{
	UINT8 dat;
	xWriteCH378Cmd( CMD11_READ_VAR8 );
	xWriteCH378Data( var );
	dat = xReadCH378Data();
	xEndCH378Cmd();
	return dat;
}

/*******************************************************************************
* Function Name  : CH378WriteVar8
* Description    : 写CH378芯片内部的8位变量
* Input          : var---变量地址, dat---数据
* Output         : None
* Return         : None
*******************************************************************************/
void CH378WriteVar8( UINT8 var, UINT8 dat )
{
	xWriteCH378Cmd( CMD20_WRITE_VAR8 );
	xWriteCH378Data( var );
	xWriteCH378Data( dat );
	xEndCH378Cmd();
}

/*******************************************************************************
* Function Name  : CH378ReadVar32
* Description    : 读CH378芯片内部的32位变量
* Input          : var---变量地址
* Output         : None
* Return         : 32位数据
*******************************************************************************/
UINT32 CH378ReadVar32( UINT8 var )
{
	xWriteCH378Cmd( CMD14_READ_VAR32 );
	xWriteCH378Data( var );
	return CH378Read32bitDat();
}

/*******************************************************************************
* Function Name  : CH378WriteVar32
* Description    : 写CH378芯片内部的32位变量
* Input          : var---变量地址, dat---32位数据
* Output         : None
* Return         : None
*******************************************************************************/
void CH378WriteVar32( UINT8 var, UINT32 dat )
{
	xWriteCH378Cmd( CMD50_WRITE_VAR32 );
	xWriteCH378Data( var );
	xWriteCH378Data( (UINT8)dat );
	xWriteCH378Data( (UINT8)(dat >> 8) );
	xWriteCH378Data( (UINT8)(dat >> 16) );
	xWriteCH378Data( (UINT8)(dat >> 24) );
	xEndCH378Cmd();
}

/*******************************************************************************
* Function Name  : CH378GetTrueLen
* Description    : 快速返回上一个命令执行完毕后请求长度所对应的实际长度
* Input          : None
* Output         : None
* Return         : 实际长度
*******************************************************************************/
UINT32 CH378GetTrueLen( void )
{
	xWriteCH378Cmd( CMD02_GET_REAL_LEN );
	return CH378Read32bitDat();
}

/*******************************************************************************
* Function Name  : CH378SetFileName
* Description    : 设置将要操作的文件的文件名、路径名
* Input          : PathName---文件路径名
* Output         : None
* Return         : None
*******************************************************************************/
void CH378SetFileName( PUINT8 PathName )
{
	UINT8 NameLen;
	
	NameLen = 0;
	while( PathName[NameLen] && NameLen < MAX_PATH_LEN ) NameLen++;
	
	xWriteCH378Cmd( CMD10_SET_FILE_NAME );
	while( NameLen ) {
		xWriteCH378Data( *PathName );
		PathName++;
		NameLen--;
	}
	xWriteCH378Data( 0 );
	xEndCH378Cmd();
}

/*******************************************************************************
* Function Name  : CH378GetDiskStatus
* Description    : 获取磁盘和文件系统的工作状态
* Input          : None
* Output         : None
* Return         : 磁盘状态
*******************************************************************************/
UINT8 CH378GetDiskStatus( void )
{
	return CH378ReadVar8( 0x20 );
}

/*******************************************************************************
* Function Name  : CH378GetIntStatus
* Description    : 获取中断状态并取消中断请求
* Input          : None
* Output         : None
* Return         : 中断状态
*******************************************************************************/
UINT8 CH378GetIntStatus( void )
{
	UINT8 s;
	xWriteCH378Cmd( CMD01_GET_STATUS );
	s = xReadCH378Data();
	xEndCH378Cmd();
	return s;
}

#ifndef	NO_DEFAULT_CH378_INT
/*******************************************************************************
* Function Name  : Wait378Interrupt
* Description    : 等待CH378中断(INT#低电平)，返回中断状态码, 超时则返回ERR_USB_UNKNOWN
* Input          : None
* Output         : None
* Return         : 中断状态码
*******************************************************************************/
UINT8 Wait378Interrupt( void )
{
	UINT16 i;
	
	for( i = 0; i < 10000; i++ ) {
		if( Query378Interrupt() ) {
			return CH378GetIntStatus();
		}
		CH378_mDelayuS( 50 );
	}
	return ERR_USB_UNKNOWN;
}
#endif

/*******************************************************************************
* Function Name  : CH378SendCmdWaitInt
* Description    : 发出命令码后,等待中断
* Input          : mCmd---命令码
* Output         : None
* Return         : 中断状态码
*******************************************************************************/
UINT8 CH378SendCmdWaitInt( UINT8 mCmd )
{
	xWriteCH378Cmd( mCmd );
	xEndCH378Cmd();
	return Wait378Interrupt();
}

/*******************************************************************************
* Function Name  : CH378SendCmdDatWaitInt
* Description    : 发出命令码和一字节数据后,等待中断
* Input          : mCmd---命令码, mDat---数据
* Output         : None
* Return         : 中断状态码
*******************************************************************************/
UINT8 CH378SendCmdDatWaitInt( UINT8 mCmd, UINT8 mDat )
{
	xWriteCH378Cmd( mCmd );
	xWriteCH378Data( mDat );
	xEndCH378Cmd();
	return Wait378Interrupt();
}

/*******************************************************************************
* Function Name  : CH378GetFileSize
* Description    : 读取当前文件长度
* Input          : None
* Output         : None
* Return         : 文件长度
*******************************************************************************/
UINT32 CH378GetFileSize( void )
{
	xWriteCH378Cmd( CMD14_GET_FILE_SIZE );
	xWriteCH378Data( 0x68 );
	return CH378Read32bitDat();
}

/*******************************************************************************
* Function Name  : CH378SetFileSize
* Description    : 设置当前文件长度
* Input          : filesize---文件长度
* Output         : None
* Return         : None
*******************************************************************************/
void CH378SetFileSize( UINT32 filesize )
{
	xWriteCH378Cmd( CMD50_SET_FILE_SIZE );
	xWriteCH378Data( 0x68 );
	xWriteCH378Data( (UINT8)filesize );
	xWriteCH378Data( (UINT8)(filesize >> 8) );
	xWriteCH378Data( (UINT8)(filesize >> 16) );
	xWriteCH378Data( (UINT8)(filesize >> 24) );
	xEndCH378Cmd();
}

/*******************************************************************************
* Function Name  : CH378DiskConnect
* Description    : 检查U盘/SD卡是否连接
* Input          : None
* Output         : None
* Return         : 错误码
*******************************************************************************/
UINT8 CH378DiskConnect( void )
{
	return CH378SendCmdWaitInt( CMD0H_DISK_CONNECT );
}

/*******************************************************************************
* Function Name  : CH378DiskReady
* Description    : 初始化磁盘并测试磁盘是否就绪
* Input          : None
* Output         : None
* Return         : 错误码
*******************************************************************************/
UINT8 CH378DiskReady( void )
{
	return CH378SendCmdWaitInt( CMD0H_DISK_MOUNT );
}
