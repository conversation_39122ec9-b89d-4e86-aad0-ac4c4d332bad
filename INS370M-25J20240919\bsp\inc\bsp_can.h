#ifndef ____BSP_CAN_H____
#define ____BSP_CAN_H____

#include "gd32f4xx.h"
//#include "bsp_fmc.h"
//#include "frame_analysis.h"



#define VEHICLE_TYPE_WuLing_MiniEV			1		//���� MiniEv
#define VEHICLE_TYPE_YiQiJieFang_J6V		2		//һ����� J6V
#define VEHICLE_TYPE_AutoBots_RoboMix		3		//AutoBots-RoboMix
#define VEHICLE_TYPE_BoLeiDun				4		//���׶�
#define VEHICLE_TYPE_DongFeng_YueXiang		5		//��������
#define VEHICLE_TYPE_YiQiJieFang_J7			6		//һ����� J7
#define VEHICLE_TYPE_DongFengFengShen_E70	7		//�������E70
#define	VEHICLE_TYPE_BYD					8

//#define VEHICLE_TYPE_USED					VEHICLE_TYPE_AutoBots_RoboMix
#define VEHICLE_TYPE_USED					VEHICLE_TYPE_BYD


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x155		// ������λ
#define		CAN_ID_FRONT_WHEEL		0x348		// ����ǰ��
#define		CAN_ID_BACK_WHEEL		0x34A		// ��������
#define		CAN_ID_WHEEL_STEER		0x350		// ����������
#define		CAN_ID_ODOMETER			0x35A		// ������̼�
#define		CAN_ID_OTA				0x7FF		// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		//��ϵ�������	������Ϣ
#define CAN_ID_INS_STD				0x507		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x508		//��ϵ�������	GPSʱ��

#define CAN_ID_INS_CALI				0x020		//IMU�궨״̬
#define CAN_ID_INS_SYS_STATUS		0x030		//INSϵͳ״̬
#define CAN_ID_INS_GEAR				0x110		//��λ
#define CAN_ID_INS_VEL_SPEED		0x010		//�����ٶ�

#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix
//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x1F5		// ������λ
#define		CAN_ID_FRONT_WHEEL		0x348		// ����ǰ��
#define		CAN_ID_BACK_WHEEL		0x34A		// ��������
#define		CAN_ID_WHEEL_STEER		0x350		// ����������
#define		CAN_ID_ODOMETER			0x35A		// ������̼�
#define		CAN_ID_OTA				0x7FF		// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		// - ��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		// - ��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		// - ��ϵ�������	����ǡ������ǡ�Roll��
#define CAN_ID_INS_HEIGHT			0x503		// - ��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		// - ��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		// - ��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		// - ��ϵ�������	������Ϣ
#define CAN_ID_INS_STD0				0x507		// - ��ϵ�������	��׼��
#define CAN_ID_INS_STD1				0x508		// - ��ϵ�������	��׼��
#define CAN_ID_INS_POSaTEMP			0x509		// - ��ϵ�������	pose and temperature 

#define CAN_ID_INS_CALI				0x020		//IMU�궨״̬
#define CAN_ID_INS_SYS_STATUS		0x030		//INSϵͳ״̬
#define CAN_ID_INS_GEAR				0x110		//��λ
#define CAN_ID_INS_VEL_SPEED		0x010		//�����ٶ�

#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V
//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x155		// ������λ
#define		CAN_ID_WHEEL			0x8FE6E0B	// ��������
#define		CAN_ID_WHEEL_STEER		0x350		// ����������
#define		CAN_ID_ODOMETER			0x35A		// ������̼�
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		//��ϵ�������	������Ϣ
#define CAN_ID_INS_STD				0x507		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x508		//��ϵ�������	GPSʱ��

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V



//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

//wu lin hong guang...
#define		CAN_ID_WL_GEAR			0x155		// ������λ
#define		CAN_ID_WL_FRONT_WHEEL	0x348		// ����ǰ��
#define		CAN_ID_WL_BACK_WHEEL	0x34A		// ��������

#define		CAN_ID_BYD_GEAR			0x1F5		// ������λ
#define		CAN_ID_BYD_FRONT_WHEEL	0x348		// ����ǰ��
#define		CAN_ID_BYD_BACK_WHEEL	0x34A		// ��������


//CAN ID �� ���� ����
//#define		CAN_ID_GEAR				0x1F5		// ������λ
//#define		CAN_ID_FRONT_WHEEL		0x348		// ����ǰ��
//#define		CAN_ID_BACK_WHEEL		0x34A		// ��������
#define		CAN_ID_WHEEL_STEER		0x350		// ����������
#define		CAN_ID_ODOMETER			0x35A		// ������̼�
#define		CAN_ID_OTA				0x7FF		// OTA

//define		CAN_ID_STATE			0x304		// ������ǰ״̬
//#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_STATUS			0x506		//��ϵ�������	status
#define CAN_ID_INS_STD_HEADING		0x507		//��ϵ�������	Heading ��׼��
#define CAN_ID_INS_TEMPERATURE		0x508		//��ϵ�������	Temperature Frame
//#define CAN_ID_INS_GPSTIME			0x108		//��ϵ�������	GPSʱ��
//#define CAN_ID_INS_CALI				0x020		//IMU�궨״̬
//#define CAN_ID_INS_SYS_STATUS		0x030		//INSϵͳ״̬
//#define CAN_ID_INS_GEAR				0x110  		//��λ
//#define CAN_ID_INS_VEL_SPEED		0x010 		//�����ٶ�

#endif 

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

//CAN ID �� ���� ����
#define		CAN_ID_STATE			0x18D0FF00		// ������ǰ״̬
#define		CAN_ID_STEER			0x18D1FF00		// ����������
#define		CAN_ID_OTA				0x1FFFFFFF		// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		//��ϵ�������	������Ϣ
#define CAN_ID_INS_STD				0x507		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x508		//��ϵ�������	GPSʱ��

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x5C0		// ������λ
#define		CAN_ID_WHEEL			0xA3		// ��������
#define		CAN_ID_ABS				0x4C0		// ABS����
#define		CAN_ID_OTA				0x7FFFFF	// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x5C3		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x5C4		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x5C5		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_POSITION			0x5C6		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x5C7		//��ϵ�������	�ٶ�
#define CAN_ID_INS_STATE			0x5CA		//��ϵ�������	״̬��Ϣ
#define CAN_ID_INS_STD				0x5CB		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x5CC		//��ϵ�������	GPSʱ��
#define CAN_ID_INS_GPSWEEK			0x5CD		//��ϵ�������	GPS��
#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x1F5		// ������λ
#define		CAN_ID_WHEEL_FRONT		0x348		// ��������
#define		CAN_ID_WHEEL_BACK		0x34A		// ����������
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		//��ϵ�������	������Ϣ
#define CAN_ID_INS_STD				0x507		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x508		//��ϵ�������	GPSʱ��

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

//CAN ID �� ���� ����
#define		CAN_ID_GEAR				0x355		// ������λ
#define		CAN_ID_WHEEL			0xA3		// ��������
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID �� ���� ����
#define CAN_ID_INS_ACC				0x500		//��ϵ�������	���ٶ�
#define CAN_ID_INS_GYRO				0x501		//��ϵ�������	������
#define CAN_ID_INS_ANGLE			0x502		//��ϵ�������	����ǡ������ǡ���λ��
#define CAN_ID_INS_HEIGHT			0x503		//��ϵ�������	�߶Ⱥ�ʱ��
#define CAN_ID_INS_POSITION			0x504		//��ϵ�������	��γ��
#define CAN_ID_INS_SPEED			0x505		//��ϵ�������	�ٶ�
#define CAN_ID_INS_INFO				0x506		//��ϵ�������	������Ϣ
#define CAN_ID_INS_STD				0x507		//��ϵ�������	��׼��
#define CAN_ID_INS_GPSTIME			0x508		//��ϵ�������	GPSʱ��

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

typedef struct bsp_can_t
{
	uint32_t canDev;
	rcu_periph_enum canClk;
	
	uint32_t canHGPIO;
	rcu_periph_enum canHGPIO_CLK;
	uint16_t canH_Pin;
	
	uint32_t canLGPIO;
	rcu_periph_enum canLGPIO_CLK;
	uint16_t canL_Pin;
	
	IRQn_Type canIRQ;
	uint32_t prioMain;
	uint32_t prioSub;
	
	can_parameter_struct CanPara;
	
	can_filter_parameter_struct CanFilter;
	
	can_receive_message_struct CanRxBuf;
	can_trasnmit_message_struct CanTxBuf;
	
	FlagStatus RecvdFlag;
}CANDevTypeDef;

// POLL_DATA_TypeDef已在frame_analysis.h中定义，此处删除重复定义

extern CANDevTypeDef hCAN0,hCAN1;
extern uint8_t g_gear_detected;
extern uint8_t g_front_detected;
extern uint8_t g_back_detected;
extern uint8_t g_can0_rx_flag;
extern uint8_t g_can1_rx_flag;

extern uint8_t g_CAN_Count;

void bsp_can_init(CANDevTypeDef* hCan);
void bsp_can_transmit(CANDevTypeDef* hCan, can_trasnmit_message_struct* TxMsg);

void can_fill_std(CANDevTypeDef* hCan);
void can_fill_status(CANDevTypeDef* hCan);

void can_transmit_acc(CANDevTypeDef* hCan);
void can_transmit_gyro(CANDevTypeDef* hCan);
void can_transmit_angle(CANDevTypeDef* hCan);
void can_transmit_h(CANDevTypeDef* hCan);
void can_transmit_pos(CANDevTypeDef* hCan);
void can_transmit_speed(CANDevTypeDef* hCan);
void can_transmit_status(CANDevTypeDef* hCan);
void can_transmit_std_heading(CANDevTypeDef* hCan);
//void can_transmit_std_speed(CANDevTypeDef* hCan);
//void can_transmit_std_pose(CANDevTypeDef* hCan);
void can_transmit_temperature(CANDevTypeDef* hCan);

void can_transmit_data_info(CANDevTypeDef* hCan);
void can_transmit_std(CANDevTypeDef* hCan);
void can_transmit_gpstime(CANDevTypeDef* hCan);
void can_transmit_posatemp(CANDevTypeDef* hCan);
void can_transmit_cali(CANDevTypeDef* hCan);
void can_transmit_sys_status(CANDevTypeDef* hCan);
void can_transmit_gear(CANDevTypeDef* hCan);
void can_transmit_vel_speed(CANDevTypeDef* hCan);
void can_transmit_gpsweek(CANDevTypeDef* hCan);

#endif //____BSP_CAN_H____
