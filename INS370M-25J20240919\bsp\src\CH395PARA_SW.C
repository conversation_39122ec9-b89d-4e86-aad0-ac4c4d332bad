/********************************** (C) COPYRIGHT *******************************
* File Name          : CH395PARA_SW.C
* Author             : MJX
* Version            : V1.0
* Date               : 2012/11/30
* Description        : CH395芯片 软件模拟8位并行连接的硬件抽象层 V1.0
*                      提供I/O接口子程序
*******************************************************************************/

#include "CH395INC.H"
#include "bsp_sys.h"

#if CHIP_USED == USE_CHIP_GD32
#include "gd32f4xx.h"
#else
#include "stm32f10x.h"
#endif

/*******************************************************************************/
/*******************************************************************************/
/* 硬件相关宏定义 */
/* 本例中的硬件连接方式如下(实际应用电路可以参照修改下述定义及子程序) */
/*   单片机的引脚                  CH395芯片的引脚
	 D0---D7(PB8---PB15) 	          D0---D7
		PB7								PCS#
		PB6								A0				 	
		PB5								WR#
		PB4								RD#
		PA1							  INT#	
*/

#if CHIP_USED == USE_CHIP_GD32

#define PIN_CH395_PCS_LOW( )       gpio_bit_reset(GPIOB, GPIO_PIN_7)  /* 模拟并行片选引脚输出低电平 */
#define PIN_CH395_PCS_HIGH( )      gpio_bit_set(GPIOB, GPIO_PIN_7)    /* 模拟并行片选引脚输出高电平 */
#define PIN_CH395_A0_LOW( )        gpio_bit_reset(GPIOB, GPIO_PIN_6)  /* 模拟并行A0引脚输出低电平 */
#define PIN_CH395_A0_HIGH( )       gpio_bit_set(GPIOB, GPIO_PIN_6)    /* 模拟并行A0引脚输出高电平 */
#define PIN_CH395_WR_LOW( )        gpio_bit_reset(GPIOB, GPIO_PIN_5)  /* 模拟并行WR引脚输出低电平 */
#define PIN_CH395_WR_HIGH( )       gpio_bit_set(GPIOB, GPIO_PIN_5)    /* 模拟并行WR引脚输出高电平 */
#define PIN_CH395_RD_LOW( )        gpio_bit_reset(GPIOB, GPIO_PIN_4)  /* 模拟并行RD引脚输出低电平 */
#define PIN_CH395_RD_HIGH( )       gpio_bit_set(GPIOB, GPIO_PIN_4)    /* 模拟并行RD引脚输出高电平 */

#define CH395_DATA_SET_IN( )	   { /* GD32F4xx数据引脚设为输入模式 */ \
	gpio_mode_set(GPIOB, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15); }
#define CH395_DATA_SET_OUT( )	   { /* GD32F4xx数据引脚设为输出模式 */ \
	gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15); \
	gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15); }

#define	CH395_INT_PIN_WIRE( )	   gpio_input_bit_get(GPIOA, GPIO_PIN_1)  /* 获取CH395的INT#引脚 */

#else

#define PIN_CH395_PCS_LOW( )       ( GPIOB->BRR  = GPIO_Pin_7 )  /* 模拟并行片选引脚输出低电平 */
#define PIN_CH395_PCS_HIGH( )      ( GPIOB->BSRR = GPIO_Pin_7 )  /* 模拟并行片选引脚输出高电平 */
#define PIN_CH395_A0_LOW( )        ( GPIOB->BRR  = GPIO_Pin_6 )  /* 模拟并行A0引脚输出低电平 */
#define PIN_CH395_A0_HIGH( )       ( GPIOB->BSRR = GPIO_Pin_6 )  /* 模拟并行A0引脚输出高电平 */
#define PIN_CH395_WR_LOW( )        ( GPIOB->BRR  = GPIO_Pin_5 )  /* 模拟并行WR引脚输出低电平 */
#define PIN_CH395_WR_HIGH( )       ( GPIOB->BSRR = GPIO_Pin_5 )  /* 模拟并行WR引脚输出高电平 */
#define PIN_CH395_RD_LOW( )        ( GPIOB->BRR  = GPIO_Pin_4 )  /* 模拟并行RD引脚输出低电平 */
#define PIN_CH395_RD_HIGH( )       ( GPIOB->BSRR = GPIO_Pin_4 )  /* 模拟并行RD引脚输出高电平 */

#define CH395_DATA_SET_IN( )	   { GPIOB->CRH = 0x44444444; }  /* 设置CH395数据引脚为输入模式 */    
#define CH395_DATA_SET_OUT( )	   { GPIOB->CRH = 0x33333333; }  /* 设置CH395数据引脚为输出模式 */

#define	CH395_INT_PIN_WIRE( )	   GPIOA->IDR & GPIO_Pin_1 		  /* 获取CH395的INT#引脚 */

#endif

/*******************************************************************************
* Function Name  : mDelayuS
* Description    : 微秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelayuS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_us((uint32_t)delay);
#else
	UINT8 i, j;

	for( i = delay; i != 0; i -- ) 
	{
		for( j = 5; j != 0; j -- )
		{
		    __nop();   __nop();
		}		
	}
#endif
}

/*******************************************************************************
* Function Name  : mDelaymS
* Description    : 毫秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelaymS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_ms((uint32_t)delay);
#else
	UINT8 i;
	UINT16 j;
	for( i = delay; i != 0; i -- ) 
	{
		for( j = 200; j != 0; j -- )
		{
			mDelayuS( 5 );
		}		
	}
#endif
}				
			
/*******************************************************************************
* Function Name  : CH395_PORT_INIT
* Description    : CH395端口初始化
*                  由于使用通用I/O模拟并行读写时序,所以进行初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395_PORT_INIT( void ) 
{
#if CHIP_USED == USE_CHIP_GD32
	
	/* 初始化GPIO时钟 */
	rcu_periph_clock_enable(RCU_GPIOA);
	rcu_periph_clock_enable(RCU_GPIOB);
	
	/* 配置CH395对应引脚PCS#(PB7)、A0(PB6)、WD#(PB5)、RD#(PB4)方向为输出 */
	gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_7|GPIO_PIN_6|GPIO_PIN_5|GPIO_PIN_4);
	gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_7|GPIO_PIN_6|GPIO_PIN_5|GPIO_PIN_4);

	/* CH395对应引脚PCS#(PB7)、A0(PB6)、WD#(PB5)、RD#(PB4)默认输出高电平 */
    PIN_CH395_WR_HIGH();		     										 /* WR默认为高 */
    PIN_CH395_RD_HIGH();		    										 /* RD默认为高 */
    PIN_CH395_A0_LOW();		 										 /* A0默认为低 */
    PIN_CH395_PCS_HIGH();		     										 /* CS默认为高 */
    CH395_DATA_SET_IN();      										 /* 端口设置为输入 */
	
	/* 配置CH395对应数据引脚PB8---PB15方向为输出 */
	CH395_DATA_SET_OUT();

	/* 配置CH395对应中断引脚INT#(PA1)方向为输入 */		
    gpio_mode_set(GPIOA, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, GPIO_PIN_1);
	
#else
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 由于例中的PB3和PB4对应单片机的JTAG功能,所以必须先禁止JTAG功能 */
	GPIO_PinRemapConfig( GPIO_Remap_SWJ_Disable, ENABLE ); 
	
	/* 配置CH395对应引脚PCS#(PB7)、A0(PB6)、WD#(PB5)、RD#(PB4)方向为输出 */
  	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_6 | GPIO_Pin_5 | GPIO_Pin_4;			
  	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;			 /* 推挽式输出 */
  	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;            /* 速度50MHZ */
	GPIO_Init( GPIOB, &GPIO_InitStructure );

	/* CH395对应引脚PCS#(PB7)、A0(PB6)、WD#(PB5)、RD#(PB4)默认输出高电平 */
    PIN_CH395_WR_HIGH();		     										 /* WR默认为高 */
    PIN_CH395_RD_HIGH();		    										 /* RD默认为高 */
    PIN_CH395_A0_LOW();		 										 /* A0默认为低 */
    PIN_CH395_PCS_HIGH();		     										 /* CS默认为高 */
    CH395_DATA_SET_IN();      										 /* 端口设置为输入 */
	
	/* 配置CH395对应数据引脚PB8---PB15方向为输出 */
  	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11
  	                            | GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15;			
  	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;			 /* 推挽式输出 */
  	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;            /* 速度50MHZ */
	GPIO_Init( GPIOB, &GPIO_InitStructure );

	/* 配置CH395对应INT#(PA1)方向为输入 */		
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;  				 /* 上拉输入 */
    GPIO_Init( GPIOA, &GPIO_InitStructure );
#endif					   
}

/*==============================================================================
* Function Name  : xWriteCH395Cmd
* Description    : 向CH395写命令
* Input          : cmd 8位命令码
* Output         : None
* Return         : None
==============================================================================*/
void xWriteCH395Cmd(UINT8 cmd)									     /* 向CH395写命令 */
{
#if CHIP_USED == USE_CHIP_GD32
	uint32_t data_value = cmd;
	data_value <<= 8;
	GPIO_OCTL(GPIOB) = (GPIO_OCTL(GPIOB) & 0x00FF) | data_value;		 /* 向CH395的并行数据输出命令 */
#else
	GPIOB->ODR = ( GPIOB->ODR & 0x00FF ) | ( cmd << 8 );		 /* 向CH395的并行数据输出命令 */
#endif
 	CH395_DATA_SET_OUT();											 /* 设置输出 */
    PIN_CH395_A0_HIGH();		 											 /* 将A0置为高 */
    PIN_CH395_WR_LOW();		     									 /* 将WR拉低 */
    PIN_CH395_PCS_LOW();		     									 /* 将CS拉低 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
    PIN_CH395_WR_HIGH();		     										 /* 将WR置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    PIN_CH395_A0_LOW();		 										 /* 将A0拉低 */
    PIN_CH395_A0_LOW();		 										 /* 将A0拉低 */
    CH395_DATA_SET_IN(); 											 /* 数据端口设置为输入 */
    mDelayuS(5);      												 /* 需要延时 */
}

/*==============================================================================
* Function Name  : xWriteCH395Data
* Description    : 向CH395写8位数据
* Input          : mdata 8位数据
* Output         : None
* Return         : None
==============================================================================*/
void  xWriteCH395Data(UINT8 mdata)
{																	 /* 向CH395写数据 */
#if CHIP_USED == USE_CHIP_GD32
	uint32_t data_value = mdata;
	data_value <<= 8;
	GPIO_OCTL(GPIOB) = (GPIO_OCTL(GPIOB) & 0x00FF) | data_value;		 /* 向CH395的并行数据输出数据 */
#else
	GPIOB->ODR = ( GPIOB->ODR & 0x00FF ) | ( mdata << 8 );		 /* 向CH395的并行数据输出数据 */
#endif	
	CH395_DATA_SET_OUT();											 /* 设置输出 */
    PIN_CH395_WR_LOW();		     									 /* 将WR拉低 */
    PIN_CH395_PCS_LOW();		     									 /* 将CS拉低 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
    PIN_CH395_PCS_LOW();		     									 /* 将CS拉低 */
    PIN_CH395_WR_HIGH();		     										 /* 将WR置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    CH395_DATA_SET_IN();											 /* 数据端口设置为输入 */
}

/*==============================================================================
* Function Name  : xReadCH395Data
* Description    : 从CH395读数据
* Input          : None
* Output         : None
* Return         : 读取的数据
==============================================================================*/
UINT8	xReadCH395Data( void )                                       /* 从CH395读数据 */
{
    UINT8 i;
    CH395_DATA_SET_IN();											 /* 数据端口设置为输入 */
    PIN_CH395_PCS_LOW();		     									 /* 将CS拉低 */
	PIN_CH395_RD_LOW();		     								     /* 将RD拉低 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
    PIN_CH395_PCS_LOW();		     									 /* 有效脉冲宽度延时使用 */
#if CHIP_USED == USE_CHIP_GD32
	i = (UINT8)( ( GPIO_ISTAT(GPIOB) ) >> 8 );							 /* 从端口读数据 */
#else
	i = (UINT8)( ( GPIOB->IDR ) >> 8 );									 /* 从端口读数据 */
#endif
    PIN_CH395_RD_HIGH();		     										 /* 将RD置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    PIN_CH395_PCS_HIGH();		     										 /* 将CS置为高 */
    CH395_DATA_SET_IN();											 /* 数据端口设置为输入 */
	return i;
}

#define xEndCH395Cmd() 	   {  PIN_CH395_PCS_HIGH();}  									     /* 结束命令，仅用于SPI模式无效 */

/*******************************************************************************
* Function Name  : Query395Interrupt
* Description    : 查询CH395中断(INT#低电平)
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
UINT8 Query395Interrupt( void )
{
	return( CH395_INT_PIN_WIRE( ) ? FALSE : TRUE );  
}
