Rebuild started: Project: INS
*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\softwawe\Keil_v5\ARM\ARMCC\Bin'
Rebuild target 'INS_4000'
compiling gd32f4xx_it.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\gd32f4xx_it.c: 0 warnings, 1 error
compiling main.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\main.c: 0 warnings, 1 error
compiling can_data.c...
compiling systick.c...
..\Source\src\systick.c(13): error:  #5: cannot open source input file "board.h": No such file or directory
  #include "board.h"
..\Source\src\systick.c: 0 warnings, 1 error
compiling imu_data.c...
compiling clock.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\clock.c: 0 warnings, 1 error
compiling INS_Data.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\INS_Data.c: 0 warnings, 1 error
compiling Data_shift.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\Data_shift.c: 0 warnings, 1 error
compiling fpgad.c...
..\Source\inc\fpgad.h(16): error:  #5: cannot open source input file "board.h": No such file or directory
  #include "board.h"
..\Source\src\fpgad.c: 0 warnings, 1 error
compiling INS_Sys.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\INS_Sys.c: 0 warnings, 1 error
compiling Time_Unify.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\Time_Unify.c: 0 warnings, 1 error
compiling gdwatch.c...
..\Source\inc\gdtypedefine.h(11): error:  #5: cannot open source input file "board.h": No such file or directory
  #include "board.h"
..\Source\src\gdwatch.c: 0 warnings, 1 error
compiling INS_Init.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\INS_Init.c: 0 warnings, 1 error
compiling INS_Output.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\INS_Output.c: 0 warnings, 1 error
compiling api_ch392.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\api_ch392.c: 0 warnings, 1 error
compiling datado.c...
..\Source\inc\deviceconfig.h(22): warning:  #47-D: incompatible redefinition of macro "CMPL_CODE_EDWOY" 
  #define CMPL_CODE_EDWOY         //绯荤粺瀹氫箟
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\src\datado.c: 1 warning, 1 error
compiling bmp280.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bmp280.c: 0 warnings, 1 error
compiling bsp_adc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_adc.c: 0 warnings, 1 error
compiling bsp_can.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_can.c: 0 warnings, 1 error
compiling bmp2.c...
compiling bsp_exti.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_exti.c: 0 warnings, 1 error
compiling bsp_flash.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_flash.c: 0 warnings, 1 error
compiling bsp_fmc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_fmc.c: 0 warnings, 1 error
compiling bsp_fwdgt.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_fwdgt.c: 0 warnings, 1 error
compiling bsp_gpio.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_gpio.c: 0 warnings, 1 error
compiling bsp_rtc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_rtc.c: 0 warnings, 1 error
compiling bsp_sys.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_sys.c: 0 warnings, 1 error
compiling bsp_tim.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_tim.c: 0 warnings, 1 error
compiling bsp_uart.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_uart.c: 0 warnings, 1 error
compiling CH378_HAL.C...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\CH378_HAL.C: 0 warnings, 1 error
compiling CH395CMD.C...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\CH395CMD.C: 0 warnings, 1 error
compiling common.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\common.c: 0 warnings, 1 error
compiling CH395SPI.C...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\CH395SPI.C: 0 warnings, 1 error
compiling FILE_SYS.C...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\FILE_SYS.C: 0 warnings, 1 error
compiling Logger.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\Logger.c: 0 warnings, 1 error
compiling TCP_Server.c...
compiling CH378_SPI_HW.C...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\CH378_SPI_HW.C: 0 warnings, 1 error
compiling bsp_soft_i2c_master.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\bsp_soft_i2c_master.c: 0 warnings, 1 error
compiling drv_spi.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\drv_spi.c: 0 warnings, 1 error
compiling drv_gpio.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\bsp\src\drv_gpio.c: 0 warnings, 1 error
compiling data_convert.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Common\src\data_convert.c: 0 warnings, 1 error
compiling nav_cli.c...
compiling SEGGER_RTT_printf.c...
compiling adxl355.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\adxl355.c: 0 warnings, 1 error
compiling SEGGER_RTT.c...
compiling private_math.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\private_math.c: 0 warnings, 1 error
compiling readpaoche.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\readpaoche.c: 0 warnings, 1 error
compiling navi.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\navi.c: 0 warnings, 1 error
compiling align.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\align.c: 0 warnings, 1 error
compiling AnnTempCompen.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\AnnTempCompen.c: 0 warnings, 1 error
compiling kalman.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\kalman.c: 0 warnings, 1 error
compiling matvecmath.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\matvecmath.c: 0 warnings, 1 error
compiling read_and_check_gnss_data.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\read_and_check_gnss_data.c: 0 warnings, 1 error
compiling dynamic_align.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\INAV\dynamic_align.c: 0 warnings, 1 error
compiling computerFrameParse.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Protocol\computerFrameParse.c: 0 warnings, 1 error
compiling protocol.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Protocol\protocol.c: 0 warnings, 1 error
compiling frame_analysis.c...
..\Protocol\protocol.h(13): error:  #5: cannot open source input file "board.h": No such file or directory
  #include "board.h"
..\Protocol\frame_analysis.c: 0 warnings, 1 error
compiling InsTestingEntry.c...
..\Source\inc\gdtypedefine.h(11): error:  #5: cannot open source input file "board.h": No such file or directory
  #include "board.h"
..\Protocol\InsTestingEntry.c: 0 warnings, 1 error
compiling pjt_board.c...
..\Source\inc\deviceconfig.h(22): warning:  #47-D: incompatible redefinition of macro "CMPL_CODE_EDWOY" 
  #define CMPL_CODE_EDWOY         //绯荤粺瀹氫箟
..\Source\Edwoy\pjt_glb_head.h(218): warning:  #1-D: last line of file ends without a newline
  #endif //<__PJT_GLB_HEAD__>
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\Edwoy\pjt_board.c: 2 warnings, 1 error
compiling sensor_misc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\Edwoy\sensor_misc.c: 0 warnings, 1 error
assembling startup_gd32f450_470.s...
compiling app_tool.c...
..\Source\inc\deviceconfig.h(22): warning:  #47-D: incompatible redefinition of macro "CMPL_CODE_EDWOY" 
  #define CMPL_CODE_EDWOY         //绯荤粺瀹氫箟
..\Source\Edwoy\pjt_glb_head.h(218): warning:  #1-D: last line of file ends without a newline
  #endif //<__PJT_GLB_HEAD__>
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Source\Edwoy\app_tool.c: 2 warnings, 1 error
compiling system_gd32f4xx.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c: 0 warnings, 1 error
compiling gd32f4xx_adc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c: 0 warnings, 1 error
compiling gd32f4xx_can.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c: 0 warnings, 1 error
compiling gd32f4xx_crc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c: 0 warnings, 1 error
compiling gd32f4xx_ctc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c: 0 warnings, 1 error
compiling gd32f4xx_dac.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c: 0 warnings, 1 error
compiling gd32f4xx_dbg.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c: 0 warnings, 1 error
compiling gd32f4xx_dci.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c: 0 warnings, 1 error
compiling gd32f4xx_dma.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c: 0 warnings, 1 error
compiling gd32f4xx_enet.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c: 0 warnings, 1 error
compiling gd32f4xx_exmc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c: 0 warnings, 1 error
compiling gd32f4xx_exti.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c: 0 warnings, 1 error
compiling gd32f4xx_fmc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c: 0 warnings, 1 error
compiling gd32f4xx_fwdgt.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c: 0 warnings, 1 error
compiling gd32f4xx_gpio.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c: 0 warnings, 1 error
compiling gd32f4xx_i2c.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c: 0 warnings, 1 error
compiling gd32f4xx_ipa.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c: 0 warnings, 1 error
compiling gd32f4xx_iref.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c: 0 warnings, 1 error
compiling gd32f4xx_misc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c: 0 warnings, 1 error
compiling gd32f4xx_pmu.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c: 0 warnings, 1 error
compiling gd32f4xx_rcu.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c: 0 warnings, 1 error
compiling gd32f4xx_rtc.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c: 0 warnings, 1 error
compiling gd32f4xx_sdio.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c: 0 warnings, 1 error
compiling gd32f4xx_spi.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c: 0 warnings, 1 error
compiling gd32f4xx_syscfg.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c: 0 warnings, 1 error
compiling gd32f4xx_timer.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c: 0 warnings, 1 error
compiling gd32f4xx_tli.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c: 0 warnings, 1 error
compiling gd32f4xx_trng.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c: 0 warnings, 1 error
compiling gd32f4xx_usart.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c: 0 warnings, 1 error
compiling gd32f4xx_wwdgt.c...
D:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include\gd32f4xx_libopt.h(11): error:  #5: cannot open source input file "RTE_Components.h": No such file or directory
  #include "RTE_Components.h"
..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c: 0 warnings, 1 error
".\Objects\arm2.axf" - 84 Error(s), 5 Warning(s).
Target not created.
Build Time Elapsed:  00:00:08