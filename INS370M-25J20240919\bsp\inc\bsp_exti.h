#ifndef ____BSP_EXTI_H____
#define ____BSP_EXTI_H____

#include "gd32f4xx.h"
#include "gd32f4xx_rcu.h"
#include "gd32f4xx_exti.h"
#include "gd32f4xx_syscfg.h"

typedef struct exti_t{
	rcu_periph_enum exti_rcu;
	uint32_t gpio;
	uint32_t pin;
	uint8_t IRQn;
	uint8_t exti_port_source;
	uint8_t exti_pin_source;
	exti_line_enum exti_line;
	exti_trig_type_enum edge;
} EXTI_TypeDef;

#define EXTI_CNT_MAX		3

void bsp_exti_init(void);

#endif //____BSP_EXTI_H____
