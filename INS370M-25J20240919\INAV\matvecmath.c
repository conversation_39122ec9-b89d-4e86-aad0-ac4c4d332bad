/***********************************************************************************************************************************/
/*MATVECMATH.C                                                                                                                   */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.h                                                                                                  */
/*GNSSlocusGen.m                                                              */
/*                     */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "const.h"
#include "typedefine.h"
#include <math.h>




/***************************************************************************************/
/*Vec_Cross                                                                          */
/*2                                                           */
/*  Ver 0.1                                                                            */
/*/                                                                              */
/*                                                                                     */
/*:InVecA:InVecB                                                           */
/*:OutVecC = InVecA  InVecB                                                    */
/*                                                                               */
/*                             */
/*NULLYESNO                      */
/**********************************************************************************************/
void Vec_Cross(VEC const InVecA[3], VEC const InVecB[3], VEC OutVecC[3])
{
    /*if((InVecA == OutVecC) || (InVecB == OutVecC))
    {
        return NO; //NO
    }

    if((InVecA == Null) || (InVecB == Null) || (OutVecC == Null))
    {
        return NO;
    }*/
    OutVecC[0] = InVecA[1] * InVecB[2] - InVecA[2] * InVecB[1];
    OutVecC[1] = InVecA[2] * InVecB[0] - InVecA[0] * InVecB[2];
    OutVecC[2] = InVecA[0] * InVecB[1] - InVecA[1] * InVecB[0];

    //return YES;
}





/****************************************************************************************/
/*Vec_Dot                                                                             */
/*2                                                            */
/*  Ver 0.1                                                                             */
/*/                                                                               */
/*                                                                                      */
/*:InVecA:InVecB                                                            */
/*:OutVecC = InVecA * InVecB                                                      */
/*                                                                                */
/*                              */
/*NULLYESNO                       */
/***********************************************************************************************/
void Vec_Dot(VEC const InVecA[3], VEC const InVecB[3], SCAL *OutVecC)
{
    /*if((InVecA == &OutVecC) || (InVecB == &OutVecC))
    {
        return NO; //NO
    }
    if((InVecA == Null) || (InVecB == Null) || (OutVecC == Null))
    {
        return NO;
    }*/
    *OutVecC = InVecA[0] * InVecB[0] + InVecA[1] * InVecB[1] + InVecA[2] * InVecB[2];

    //return YES;
}





/****************************************************************************************/
/*MultiDim_Vec_Dot                                                                    */
/*2                                               */
/*  Ver 0.1                                                                             */
/*/                                                                               */
/*                                                                                      */
/*:InVecA:InVecB                                                             */
/*:OutVecC = InVecA * InVecB                                                      */
/*                                                                                */
/*                               */
/*NULLYESNO                        */
/************************************************************************************************/
void MultiDim_Vec_Dot(VEC *InVecA, VEC *InVecB, SCAL *OutVecC,IPARA LengthVec)
{
    IPARA i;
    *OutVecC = 0.0;
    /*if((InVecA == OutVecC) || (InVecB == OutVecC))
    {
        return NO; //NO	
    }
    if((InVecA == Null) || (InVecB == Null) || (OutVecC == Null))
    {
        return NO;
    }*/
    for(i = 0;i < LengthVec;i++)
    {
        *OutVecC += *InVecA  * *InVecB;
        InVecA++;
        InVecB++;
    }
    //return YES;
}





/***************************************************************************************/
/*Mat_Mul                                                                             */
/*2                                                            */
/*  Ver 0.1                                                                             */
/*/                                                                               */
/*                                                                                      */
/*:InMatA:InMatB                                                            */
/*A:RowAAB):ColAB:ColB                            */
/*OutMatC = InMatA * InMatB                                                        */
/*                                                                                 */
/*                               */
/*NULLYESNO                        */
/************************************************************************************************/
void Mat_Mul(MATR *InMatA, MATR *InMatB, MATR *OutMatC, IPARA RowA, IPARA ColA, IPARA ColB)
{
    register IPARA i,j,k;

    /*if((InMatA == OutMatC) || (InMatB == OutMatC))
    {
        return NO; //NO
    }
    if((InMatA == Null) || (InMatB == Null) || (OutMatC == Null))
    {
        return NO;
    }*/
    for (i = 0; i < RowA; i++)
    {
        for (j = 0; j < ColB; j++)
        {
            OutMatC[i * ColB + j] = 0.0;     //
            
            for (k = 0; k < ColA; k++)
            {
                OutMatC[i * ColB + j] += InMatA[i * ColA + k] * InMatB[ k * ColB + j];
            }
        }
    }

    //return YES;
}





/***************************************************************************************/
/*Mat_Tr                                                                             */
/*                                                              */
/*  Ver 0.1                                                                            */
/*/                                                                               */
/*                                                                                      */
/*InMatA                                                                          */
/*OutMatB = InMatA'                                                               */
/*                                                                                */
/*                               */
/*NULLYESNO                        */
/************************************************************************************************/
void Mat_Tr(MATR *InMatA, MATR *OutMatB, IPARA RowA, IPARA ColA)
{
    register IPARA i,j;
     
    /*if(InMatA == OutMatB)
    {
        return NO; //NO
    }
    if((InMatA == Null) || (OutMatB == Null))
    {
            return NO;
    }*/
        
    for (i = 0; i < RowA ; i++)
    {
          
        for(j = 0; j < ColA; j++)
        {
            OutMatB[j * RowA + i] = InMatA[i * ColA + j];
        }
    }
        
    //return YES;
}





/***********************************************************************/
/*Mat_Inv                                                            */
/*                                              */
/*  Ver 0.1                                                            */
/*/                                                              */
/*                                                                     */
/*InMatA                                                         */
/*OutMatB = InMatA                                        */
/*                                                               */
/*1             */
/*2Guass                                 */
/*NULLYESNO                 */
/*******************************************************************************/
void Mat_Inv(MATR *InMatA, MATR *OutMatB,IPARA Order)
{
    //register BOOL isSingular = YES;  
    register IPARA i,j,k;
    register DPARA Max,Temp;

    MATR MatTemp[MAX_MAT_DIM * MAX_MAT_DIM];

    /*if((InMatA == Null) || (OutMatB == Null))
    {
            return NO;
    }*/
    //MatTempInMatAOutMatB
    for (i = 0; i < Order; i++)
    {
        for(j = 0; j < Order; j++)
        {
            MatTemp[i * Order + j] = InMatA[i * Order + j];
            OutMatB[i * Order + j] = (i == j) ? 1.0 : 0.0;
        }
    }

    for (i = 0; i < Order; i++)
    {
        Max = MatTemp[i * Order + i];
        k = i;
        
        for(j = i + 1; j < Order; j++)
        {
            if(fabs(MatTemp[j * Order + i]) > fabs(Max))
            {
                Max = MatTemp[j * Order + i];
                k = j;
            }
        }
        //i
        if(k != i)
        {
            for(j = 0; j < Order; j++)
            {
                Temp = MatTemp[i * Order + j];
                MatTemp[i * Order + j] = MatTemp[k * Order + j];
                MatTemp[k * Order + j] = Temp;
                //OutMatB
                Temp = OutMatB[i * Order + j];
                OutMatB[i * Order + j] = OutMatB[k * Order + j];
                OutMatB[k * Order + j] = Temp;
            }
        }
        //MIN_DATAInMatA
        /*if(fabs(MatTemp[i * Order + i]) < MIN_DATA)
        {
            isSingular = NO;
        }*/
        //InMatAii
        Temp = 1.0 / MatTemp[i * Order + i];
        for (j = 0; j < Order; j++)
        {
            MatTemp[i * Order + j] = MatTemp[i * Order + j] * Temp; //1
            OutMatB[i * Order + j] = OutMatB[i * Order + j] * Temp; //
        }
        for (j = 0; j < Order; j++)
        {
            if(j != i)
            {
                Temp = MatTemp[j * Order + i];
                for(k = 0; k < Order; k++)
                {
                    MatTemp[j * Order + k] = MatTemp[j * Order + k] - MatTemp[i * Order + k] * Temp;
                    OutMatB[j * Order + k] = OutMatB[j * Order + k] - OutMatB[i * Order + k] * Temp;
                }
            }
        }
    }
    //NO
    /*if(isSingular == NO)
    {
        return NO;
    }
    else
    {
        return YES;
    }*/
}





/***********************************************************************/
/*Qua_Mul                                                            */
/*2                                          */
/*  Ver 0.1                                                            */
/*/                                                               */
/*                                                                     */
/*:InQuaA:InQuaB                                        */
/*:OutQuaC = InQuaA * InQuaB                                    */
/*                                                                */
/*              */
/*NULLYESNO       */
/*******************************************************************************/
void Qua_Mul(QUAT InQuaA[4], QUAT InQuaB[4], QUAT OutQuaC[4])
{
    /*if((InQuaA == OutQuaC) || (InQuaB == OutQuaC))
    {
        return NO; //NO
    }
    if((InQuaA == Null) || (InQuaB == Null)||(OutQuaC == Null))
    {
        return NO; //NO	
    }*/
    OutQuaC[0] = InQuaA[0] * InQuaB[0] - InQuaA[1] * InQuaB[1] - InQuaA[2] * InQuaB[2] - InQuaA[3] * InQuaB[3];

    OutQuaC[1] = InQuaA[0] * InQuaB[1] + InQuaA[1] * InQuaB[0] + InQuaA[2] * InQuaB[3] - InQuaA[3] * InQuaB[2];

    OutQuaC[2] = InQuaA[0] * InQuaB[2] + InQuaA[2] * InQuaB[0] + InQuaA[3] * InQuaB[1] - InQuaA[1] * InQuaB[3];

    OutQuaC[3] = InQuaA[0] * InQuaB[3] + InQuaA[3] * InQuaB[0] + InQuaA[1] * InQuaB[2] - InQuaA[2] * InQuaB[1];

    //return YES; 
}





/***************************************************************************************/
/*                                                                    */
/*                                                   */
/*  Ver 0.1                                                                             */
/*/                                                                               */
/*                                                                                      */
/*X:Y                                */
/*Coef[1]Coef[0]                                            */
/*                                                                                 */
/*1NULL                                                         */
/*2MAX_LEASTSQUARE_NUM                           */
/*3                                        */
/*1233NOYES                                    */
/************************************************************************************************/
BOOL LinerLeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[2])
{
    IPARA i;
    MATR A[MAX_LEASTSQUARE_NUM * 2];
    MATR TrA[2 * MAX_LEASTSQUARE_NUM];
    MATR ASquare[2 * 2];
    MATR invASquare[2 * 2];
    VEC B[2];
    if((lp_Xaxis == Null) || (lp_Yaxis == Null)||(Coef == Null))
    {
        return NO; //NO
    } 
    if(DotNum > MAX_LEASTSQUARE_NUM)
    {
        return NO;
    }
    
    for(i = 0; i < DotNum; i++)
    {
        A[i * 2 + 0] = 1.0;
        A[i * 2 + 1] = lp_Xaxis[i];
    }
    Mat_Tr(A,TrA,DotNum,2);
    Mat_Mul(TrA,A,ASquare,2,DotNum,2);
    /*if(Mat_Inv(ASquare,invASquare,2) == NO)
    {
        return NO;
    }*/
    Mat_Mul(TrA,lp_Yaxis,B,2,DotNum,1);
    Mat_Mul(invASquare,B,Coef,2,2,1);
    
    return YES;	
}




/******************************************************************************************/
/*2                                                                        */
/*2                                                      */
/*  Ver 0.1                                                                               */
/*/                                                                                */
/*                                                                                       */
/*X:Y                                 */
/*Coef[2]Coef[1]Coef[0]                      */
/*                                                                                 */
/*1NULL                                                          */
/*2MAX_LEASTSQUARE_NUM                           */
/*3                                        */
/*1233NOYES                                    */
/************************************************************************************************/
BOOL Ord2LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[3])
{
    IPARA i;
    MATR A[MAX_LEASTSQUARE_NUM * 3];
    MATR TrA[3 * MAX_LEASTSQUARE_NUM];
    MATR ASquare[3 * 3];
    MATR invASquare[3 * 3];
    VEC B[3];
    if((lp_Xaxis == Null) || (lp_Yaxis == Null)||(Coef == Null))
    {
        return NO; //NO	
    }
    if(DotNum > MAX_LEASTSQUARE_NUM)
    {
        return NO;
    }
    
    for(i = 0; i < DotNum; i++)
    {
        A[i * 3 + 0] = 1.0;
        A[i * 3 + 1] = lp_Xaxis[i];
        A[i * 3 + 2] = A[i * 3 + 1] * lp_Xaxis[i];
    }
    Mat_Tr(A,TrA,DotNum,3);
    Mat_Mul(TrA,A,ASquare,3,DotNum,3);
    /*if(Mat_Inv(ASquare,invASquare,3) == NO)
    {
        return NO;
    }*/
    Mat_Mul(TrA,lp_Yaxis,B,3,DotNum,1);
    Mat_Mul(invASquare,B,Coef,3,3,1);
    
    return YES;	
}





/****************************************************************************************/
/*3                                                                      */
/*3                                                     */
/*  Ver 0.1                                                                             */
/*/                                                                                */
/*                                                                                      */
/*Xlp_Xaxis:Ylp_YaxisDotNum                     */
/*Coef[3]Coef[2]Coef[1]Coef[0]   */
/*                                                                                 */
/*1NULL                                                         */
/*2MAX_LEASTSQUARE_NUM                           */
/*3                                        */
/*1233NOYES                                    */
/************************************************************************************************/
BOOL Ord3LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[4])
{
    IPARA i;
    MATR A[MAX_LEASTSQUARE_NUM * 4];
    MATR TrA[4 * MAX_LEASTSQUARE_NUM];
    MATR ASquare[4 * 4];
    MATR invASquare[4 * 4];
    VEC B[4];
    if((lp_Xaxis == Null) || (lp_Yaxis == Null)||(Coef == Null))
    {
        return NO; //NO
    } 
    if(DotNum > MAX_LEASTSQUARE_NUM)
    {
        return NO;	
    }
    
    for(i = 0; i < DotNum; i++)
    {
        A[i * 4 + 0] = 1.0;
        A[i * 4 + 1] = lp_Xaxis[i];
        A[i * 4 + 2] = A[i * 4 + 1] * lp_Xaxis[i];
        A[i * 4 + 3] = A[i * 4 + 2] * lp_Xaxis[i];
    }
    Mat_Tr(A,TrA,DotNum,4);
    Mat_Mul(TrA,A,ASquare,4,DotNum,4);
    /*if(Mat_Inv(ASquare,invASquare,4) == NO)
    {
        return NO;
    }*/
    Mat_Mul(TrA,lp_Yaxis,B,4,DotNum,1);
    Mat_Mul(invASquare,B,Coef,4,4,1);
    
    return YES;	
}





/***************************************************************************************************/
/*4                                                                                  */
/*3                                                                 */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*Xlp_Xaxis:Ylp_YaxisDotNum                                  */
/*Coef[3]Coef[2]Coef[1]Coef[0]                */
/*                                                                                             */
/*1NULL                                                                     */
/*2MAX_LEASTSQUARE_NUM                                       */
/*3                                                    */
/*1233NOYES                                                */
/************************************************************************************************************/
BOOL Ord4LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[5])
{
    IPARA i;
    MATR A[MAX_LEASTSQUARE_NUM * 5];
    MATR TrA[5 * MAX_LEASTSQUARE_NUM];
    MATR ASquare[5 * 5];
    MATR invASquare[5 * 5];
    VEC B[5];
    if((lp_Xaxis == Null) || (lp_Yaxis == Null)||(Coef == Null))
    {
        return NO; //NO
    }
    if(DotNum > MAX_LEASTSQUARE_NUM)
    {
        return NO;
    }

    for(i = 0; i < DotNum; i++)
    {
        A[i * 5 + 0] = 1.0;
        A[i * 5 + 1] = lp_Xaxis[i];
        A[i * 5 + 2] = A[i * 5 + 1] * lp_Xaxis[i];
        A[i * 5 + 3] = A[i * 5 + 2] * lp_Xaxis[i];
        A[i * 5 + 4] = A[i * 5 + 3] * lp_Xaxis[i];
    }
    Mat_Tr(A,TrA,DotNum,5);
    Mat_Mul(TrA,A,ASquare,5,DotNum,5);
    /*if(Mat_Inv(ASquare,invASquare,5) == NO)
    {
    return NO;
    }*/
    Mat_Mul(TrA,lp_Yaxis,B,5,DotNum,1);
    Mat_Mul(invASquare,B,Coef,5,5,1);

    return YES;	
}





/***************************************************************************************************/
/*                                                                                      */
/*                                              */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*lp_CoefInVarorder                                */
/*lp_OutVal                                                                         */
/*                                                                                             */
/*1NULL                                                                     */
/*2MAX_LEASTSQUARE_NUM                                       */
/*3                                                    */
/*4                                                                                    */
/*1233NOYES                                                */
/************************************************************************************************************/
BOOL PolyVal(DPARA *lp_Coef, DPARA InVar, IPARA order, DPARA *lp_OutVal)
{
    IPARA i;
    DPARA accum = 0.0;
    if((lp_Coef == Null) || (lp_OutVal == Null))
    {
        return NO; //NO	
    }

    for(i = order ; i >= 0; i--)
    {
        accum = lp_Coef[i] + InVar * accum;
    }

    *lp_OutVal = accum;
    return YES;
}





/****************************************************************************************************/
/*Relu                                                                                            */
/*                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
void Relu(DPARA *InArrayA , IPARA LengthArrayA)
{
    IPARA i;
    for(i = 0; i < LengthArrayA ; i++)
    {
        if(*InArrayA < 0.0)
        {
            *InArrayA = 0.0;
        }
        InArrayA++;
    }
}




/****************************************************************************************************/
/*DoubleMax3                                                                                      */
/*                                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
DPARA DoubleMax3(DPARA Input[3])
{
    DPARA max = Input[0];
    IPARA i = 1;
    for(i = 1;i < 3;i++)
    {
        if(max < Input[i])
        {
            max = Input[i];	
        }
    }
    return max;
}





/****************************************************************************************************/
/*Uint32Max3Max3                                                                                  */
/*                                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
UINT32 Uint32Max3(UINT32 Input[3])
{
    UINT32 max = Input[0];
    IPARA i = 1;
    for(i = 1;i < 3;i++)
    {
        if(max < Input[i])
        {
            max = Input[i];	
        }
    }
    return max;
}





/****************************************************************************************************/
/*DPARAMax2                                                                                  */
/*                                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
DPARA DPARAMax2(DPARA InputA,DPARA InputB)
{
    DPARA max = InputA;
    if(InputB > max)
        max = InputB;
    return max;
}




/****************************************************************************************************/
/*DPARAMin2                                                                                  */
/*                                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
DPARA DPARAMin2(DPARA InputA,DPARA InputB)
{
    DPARA min = InputA;
    if(InputB < min)
        min = InputB;
    return min;
}




/******************************************************************************************************/
/*IIR_Data_Input_buffer                                                                             */
/*                                                                                              */
/*  Ver 0.1                                                                                           */
/*/                                                                                             */
/*                                                                                                    */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*1                                                                                                     */
/*                                                                                                  */
/**************************************************************************************************************/
void IIR_Data_Input_buffer(DPARA InputBuffer[NUM_B],DPARA LastInput)
{
    IPARA i;
    for(i = NUM_B - 2;i >=0;i--)
    {
        InputBuffer[i + 1] = InputBuffer[i];
    }
    InputBuffer[0] = LastInput;
}




/******************************************************************************************************/
/*IIR_Data_Output_buffer                                                                            */
/*                                                                                              */
/*  Ver 0.1                                                                                           */
/*/                                                                                             */
/*                                                                                                    */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*1                                                                                                     */
/*                                                                                                  */
/**************************************************************************************************************/
void IIR_Data_Output_buffer(DPARA OutputBuffer[NUM_A],DPARA LastOutput)
{
    IPARA i;
    for(i = NUM_A - 2;i >=0;i--)
    {
        OutputBuffer[i + 1] = OutputBuffer[i];	
    }
    OutputBuffer[0] = LastOutput;
}




/****************************************************************************************************/
/*IIR_Filter                                                                                      */
/*                                                                                            */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                */
/************************************************************************************************************/
DPARA IIR_Filter(DPARA InputBuffer[NUM_B],DPARA OutputBuffer[NUM_A],DPARA IIR_a[NUM_A] ,DPARA IIR_b[NUM_B])
{
    IPARA i;
    DPARA Output = 0.0;
    for(i = 0; i < NUM_B;i++)
    {
        Output += IIR_b[i] * InputBuffer[i];
    }
    for(i = 0;i < NUM_A;i++)
    {
        Output -= IIR_a[i] * OutputBuffer[i];
    }
    return Output;
}

