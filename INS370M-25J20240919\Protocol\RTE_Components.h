/*!
    \file    RTE_Components.h
    \brief   RTE Components header file for GD32F4xx
*/

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

/* RTE Components definitions */

/* Define to prevent recursive inclusion */
#define RTE_COMPONENTS_H

/* GD32F4xx Device Family Pack Components */
#define RTE_DEVICE_STARTUP_GD32F4XX    /* Device Startup for GD32F4xx */
#define RTE_DEVICE_STDPERIPH_FRAMEWORK /* Standard Peripheral Framework */

/* CMSIS Components */
#define RTE_CMSIS_CORE                 /* CMSIS-CORE */

/* Standard Library Components */
#define RTE_COMPILER_IO_STDOUT         /* Compiler I/O: STDOUT */
#define RTE_COMPILER_IO_STDIN          /* Compiler I/O: STDIN */
#define RTE_COMPILER_IO_STDERR         /* Compiler I/O: STDERR */

#endif /* RTE_COMPONENTS_H */
