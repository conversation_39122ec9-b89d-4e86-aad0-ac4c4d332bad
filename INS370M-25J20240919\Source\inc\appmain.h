/*!
    \file  appmain.h
    \brief the header file of main 
*/
#ifndef __APPMAIN_H
#define __APPMAIN_H


//#include "algorithm.h"
#include "gd32f4xx.h"
#include "systick.h"
#include <stdio.h>

#include "main.h"

#include "bsp_gpio.h"
#include "bsp_flash.h"
#include "INS_Data.h"
#include "bsp_sys.h"
#include "bsp_rtc.h"
#include "bsp_can.h"
#include "bsp_fwdgt.h"
#include "INS_Sys.h"
#include "gnss.h"
#include "CH395SPI.H"
#include "CH395INC.H"
#include "CH395CMD.H"
// #include "TCPServer.h"  // GD32F4xx平台暂不支持
#include "bsp_fmc.h"
#include "bsp_exti.h"
#include "bmp280.h"
#include "CH378_HAL.h"
#include "logger.h"
#include "time_unify.h"
#include "bsp_tim.h"
//#include "nav_app.h"
#include "fpgad.h"
// #include "serial.h"  // GD32F4xx平台暂不支持
//#include "nav.h"
#include "config.h"
#include "computerFrameParse.h"
#include "INS912AlgorithmEntry.h"
#include "appdefine.h"
#include "gdtypedefine.h"
#include "frame_analysis.h"
#include "InsTestingEntry.h"

#include "deviceconfig.h"
#include "datado.h"
#include "SetParaBao.h"
#include "FirmwareUpdateFile.h"


void GetChipID(void);
void INS_Init(void);
//void StartNavigation(void);
void StopNavigation(void);
void protocol_send(void);


int checkUSBReady(void);
void LEDIndicator(uint8_t state);
void bsp_systick_init01(uint32_t com);
void uart4sendmsg(char *txbuf, int size);
void get_fpgadata(void);
void comm_store_init(void);
void INS912_Output(navoutdata_t *pnavout);


extern	unsigned int gprotocol_send_baudrate;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
extern	unsigned int gprotocol_send_baudrate6;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600, BAUD_RATE_2000000, BAUD_RATE_230400
extern	int	gfpgasenddatalen;
extern	uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
extern	int gbilldebuguart4;

#endif /* __APPMAIN_H */


