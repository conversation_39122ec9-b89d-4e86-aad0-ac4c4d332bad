#include "bsp_gpio.h"




void bsp_gpio_init(void)
{
	rcu_periph_clock_enable( RCU_GPIOA);
	rcu_periph_clock_enable( RCU_GPIOB);
	rcu_periph_clock_enable( RCU_GPIOC);
	rcu_periph_clock_enable( RCU_GPIOD);
	rcu_periph_clock_enable( RCU_GPIOE);
	rcu_periph_clock_enable( RCU_GPIOF);
	rcu_periph_clock_enable( RCU_GPIOG);
	rcu_periph_clock_enable( RCU_GPIOH);
	
	//////////////////////////////////////////////////////////////////
	//�����������
	//////////////////////////////////////////////////////////////////
	//UM982 Power EN
	gpio_mode_set(UM982_POWER_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,UM982_POWER_IO_PIN);
	gpio_output_options_set(UM982_POWER_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,UM982_POWER_IO_PIN);
	
	//Z��������� Power EN
	gpio_mode_set(Z_AXIS_5V_POWER_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,Z_AXIS_5V_POWER_IO_PIN);
	gpio_output_options_set(Z_AXIS_5V_POWER_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,Z_AXIS_5V_POWER_IO_PIN);
	
	//Mems 3.3V Power EN
	gpio_mode_set(MEMS_3V3_POWER_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP,MEMS_3V3_POWER_IO_PIN);
	gpio_output_options_set(MEMS_3V3_POWER_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,MEMS_3V3_POWER_IO_PIN);
	
	//ARM1 3.3V Power EN
	gpio_mode_set(ARM1_POWER_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,ARM1_POWER_IO_PIN);
	gpio_output_options_set(ARM1_POWER_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,ARM1_POWER_IO_PIN);
	
	//LED SRAM
	gpio_mode_set(GPIOH, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,LED_SRAM_IO_PIN);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,LED_SRAM_IO_PIN);
	
	//Wheel LED
	gpio_mode_set(LED_WHEEL_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,LED_WHEEL_IO_PIN);
	gpio_output_options_set(LED_WHEEL_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,LED_WHEEL_IO_PIN);
	
	//State LED
	gpio_mode_set(LED_STATE_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,LED_STATE_IO_PIN);
	gpio_output_options_set(LED_STATE_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,LED_STATE_IO_PIN);
	
#if 0
	gpio_mode_set(GPIOC, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,GPIO_PIN_12);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

	gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,GPIO_PIN_2);
	gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);
	
	int x = 1;
	while (x) {
		for (int i = 0; i < 10000; i++);
		gpio_bit_set(GPIOH,GPIO_PIN_7);
		gpio_bit_set(GPIOC,GPIO_PIN_12);
		gpio_bit_set(GPIOD,GPIO_PIN_2);
		
		
		
		for (int i = 0; i < 10000; i++);
		gpio_bit_reset(GPIOH,GPIO_PIN_7);
		gpio_bit_reset(GPIOC,GPIO_PIN_12);
		gpio_bit_reset(GPIOD,GPIO_PIN_2);
	}
#endif
	
	//CAN 1 ʹ��
	gpio_mode_set(CAN1_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,CAN1_IO_PIN);
	gpio_output_options_set(CAN1_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CAN1_IO_PIN);
	
	//CAN 2 ʹ��
	gpio_mode_set(CAN2_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,CAN2_IO_PIN);
	gpio_output_options_set(CAN2_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CAN2_IO_PIN);
	
	//ETH ��λ
	gpio_mode_set(ETH_RST_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,ETH_RST_IO_PIN);
	gpio_output_options_set(ETH_RST_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,ETH_RST_IO_PIN);
	
	//ARM2 ֪ͨ ARM1  IO
	gpio_mode_set(ARM2_OUTPUT_ARM1_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP,ARM2_OUTPUT_ARM1_IO_PIN);
	gpio_output_options_set(ARM2_OUTPUT_ARM1_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,ARM2_OUTPUT_ARM1_IO_PIN);
	
	//CAN1 STB IO
	gpio_mode_set(CAN1_STB_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,CAN1_STB_IO_PIN);
	gpio_output_options_set(CAN1_STB_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CAN1_STB_IO_PIN);
	
	//CAN2 STB IO
	gpio_mode_set(CAN2_STB_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,CAN2_STB_IO_PIN);
	gpio_output_options_set(CAN2_STB_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CAN2_STB_IO_PIN);
	
	
	
	//////////////////////////////////////////////////////////////////
	//��������
	//////////////////////////////////////////////////////////////////
	
	
	
	
	//////////////////////////////////////////////////////////////////
	//PWM���� - 暂时注释掉，因为PA0没有连接到FPGA
	//////////////////////////////////////////////////////////////////
	// rcu_periph_clock_enable(RCU_GPIOA);  // 已在上面使能
	// gpio_mode_set(PWM_IO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, PWM_IO_PIN);
	// gpio_output_options_set(PWM_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,PWM_IO_PIN);
	// gpio_af_set(PWM_IO_PORT, GPIO_AF_1, PWM_IO_PIN);
}

