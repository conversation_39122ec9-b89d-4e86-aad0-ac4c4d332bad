.\objects\gdwatch.o: ..\Source\src\gdwatch.c
.\objects\gdwatch.o: ..\Source\inc\gdtypedefine.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\gdwatch.o: ..\Source\inc\board.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx_libopt.h
.\objects\gdwatch.o: ..\Protocol\RTE_Components.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\gdwatch.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\gdwatch.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\gdwatch.o: ..\Source\inc\systick.h
.\objects\gdwatch.o: ..\Source\inc\appmain.h
.\objects\gdwatch.o: ..\Source\inc\main.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_gpio.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_flash.h
.\objects\gdwatch.o: ..\Source\inc\INS_Data.h
.\objects\gdwatch.o: ..\Library\CMSIS\arm_math.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\gdwatch.o: ..\Source\inc\gnss.h
.\objects\gdwatch.o: ..\Common\inc\data_convert.h
.\objects\gdwatch.o: ..\Source\inc\tlhtype.h
.\objects\gdwatch.o: ..\Source\inc\can_data.h
.\objects\gdwatch.o: ..\Source\inc\imu_data.h
.\objects\gdwatch.o: ..\Source\inc\INS_sys.h
.\objects\gdwatch.o: ..\Source\inc\appmain.h
.\objects\gdwatch.o: ..\Source\inc\INS912AlgorithmEntry.h
.\objects\gdwatch.o: ..\Source\inc\deviceconfig.h
.\objects\gdwatch.o: ..\Protocol\frame_analysis.h
.\objects\gdwatch.o: ..\Protocol\protocol.h
.\objects\gdwatch.o: ..\Protocol\config.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\gdwatch.o: ..\Protocol\frame_analysis.h
.\objects\gdwatch.o: ..\Protocol\insdef.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_sys.h
.\objects\gdwatch.o: ..\Library\CMSIS\core_cm4.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_rtc.h
.\objects\gdwatch.o: ..\Source\inc\Time_unify.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_can.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_fwdgt.h
.\objects\gdwatch.o: ..\bsp\inc\CH395SPI.H
.\objects\gdwatch.o: ..\bsp\inc\CH395INC.H
.\objects\gdwatch.o: ..\bsp\inc\CH395CMD.H
.\objects\gdwatch.o: ..\bsp\inc\bsp_fmc.h
.\objects\gdwatch.o: ..\bsp\inc\bsp_exti.h
.\objects\gdwatch.o: ..\bsp\inc\bmp280.h
.\objects\gdwatch.o: ..\bsp\inc\bmp2.h
.\objects\gdwatch.o: ..\bsp\inc\bmp2_defs.h
.\objects\gdwatch.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\gdwatch.o: ..\bsp\inc\common.h
.\objects\gdwatch.o: ..\bsp\inc\CH378_HAL.h
.\objects\gdwatch.o: ..\bsp\inc\CH378INC.H
.\objects\gdwatch.o: ..\bsp\inc\logger.h
.\objects\gdwatch.o: ..\bsp\inc\CH378_HAL.h
.\objects\gdwatch.o: ..\bsp\inc\FILE_SYS.h
.\objects\gdwatch.o: ..\bsp\inc\CH378_HAL.H
.\objects\gdwatch.o: ..\bsp\inc\bsp_tim.h
.\objects\gdwatch.o: ..\Source\inc\fpgad.h
.\objects\gdwatch.o: ..\Source\inc\appdefine.h
.\objects\gdwatch.o: ..\Protocol\computerFrameParse.h
.\objects\gdwatch.o: ..\Source\inc\gdtypedefine.h
.\objects\gdwatch.o: ..\Protocol\InsTestingEntry.h
.\objects\gdwatch.o: ..\Source\inc\gdtypedefine.h
.\objects\gdwatch.o: ..\Source\inc\datado.h
.\objects\gdwatch.o: ..\Source\inc\SetParaBao.h
.\objects\gdwatch.o: ..\Source\inc\FirmwareUpdateFile.h
