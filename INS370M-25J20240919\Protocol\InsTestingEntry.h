/*!
    \file    INS912AlgorithmEntry.h
    \brief   INS912 algorithm interface functions 
    \version v1.0.1
	\author	 Bill
	\date    2023/10/23
*/
#ifndef __INSTESTINGENTRY_H
#define	__INSTESTINGENTRY_H
#include "gdtypedefine.h"
#include "INS912AlgorithmEntry.h"
#include <stdio.h>
#include "board.h"
// GD32F4xx平台适配 - 注释掉HPM平台头文件
// #include "hpm_sysctl_drv.h"
// #include "hpm_gptmr_drv.h"
// #include "hpm_debug_console.h"
//typedef  struct _navcanin
//{
//	float WheelSpeed_Front_Left;	/* 轮速 左前, 单位: km/h, 精度：待定 */
//	float WheelSpeed_Front_Right;	/* 轮速 右前, 单位: km/h, 精度：待定 */
//	float WheelSpeed_Back_Left;		/* 轮速 左后, 单位: km/h, 精度：待定 */
//	float WheelSpeed_Back_Right;	/* 轮速 右后, 单位: km/h, 精度：待定 */
//	unsigned int Gear;				/* 汽车档位  0: 空档 和 停车档	2：前进档	4：倒车档 */
//	unsigned int counter;			/* CAN信息更新次数，任何一个CAN信息有改变，此变量加 1 */
//} navcanin_t;


//typedef  struct _polldata
//{
//    unsigned short	data1;
//    unsigned short 	data2;
//    unsigned short	data3;
//} polldata_t;

//typedef struct _navoutdata {
//    short 			roll;			//roll angle
//    short 			pitch;			//angle of pitch
//    short			azimuth;		//azimuth
//    short 			gyroX;			//Gyro x axis
//    short 			gyroY;			//Gyro y axis
//	short			gyroZ;			//Gyro z axis
//    short 			accelX;			//Accelerometer x axis
//    short 			accelY;			//Accelerometer y axis
//    short			accelZ;			//Accelerometer z axis
//    long			latitude;		//latitude
//    long			longitude;		//longitude
//    long			altitude;		//altitude
//    short			ve;				//East direction velocity
//    short			vn;				//North direction velocity
//    short			vu;				//upward velocity
//    unsigned char	status;			//bit0: Position bit1: Velocity bit2: attitude bit3: heading Angle
//    unsigned char	reserved[6];
//    polldata_t		poll_frame;		//Polling data, Refer to INS instruction manual for specific meaning tabl
//    unsigned int	gps_time;		//gps weekly seconds
//    unsigned char	type;			//Polling data types
//    unsigned char	gps_week;		//gps week
//	
//	short			temperature;	//device temperature
//} navoutdata_t;

//#ifndef	u16
//typedef unsigned short	u16;
//typedef short			s16;
//typedef int				s32;
//typedef unsigned int	u32;
//#endif


//typedef struct gdwrxdata912info {
//	u16		datalength;       		//1 
//	u16     selftestingcode;         //2 
//	u16     fpgaversion;             //3 
//	u16		watchversion;            //4 
//	u16     gears;                   //5 
//	float   flwheelspeed;            //6 
//	float   frwheelspeed;            //7 
//	float   blwheelspeed;            //8 
//	float   brwheelspeed;            //9 
//	u16     caninfocounter;          //10

//	s32     fogx;                    //11
//	s32     fogy;                    //12
//	s32     fogz;                    //13
//	s16     fogtemperaturex;         //14
//	s16     fogtemperaturey;         //15
//	s16     fogtemperaturez;         //26
//	float   accelerometerx;          //17
//	float   accelerometery;          //18
//	float   accelerometerz;          //19
//	s16     accelerometertemp;       //20
//	u16     reserve1e;               //21
//	u16     reserve1f;               //22
//	u16     Reserve20;               //23
//	u16     gnssweek;                //24
//	u32     millisecondofweek;       //25

//	u32     secondofweek;            //26
//	u32     ppsdelay10ns;            //27
//	u16     gpsstarnumber;           //28
//	u16     rtkstatus;               //29
//	u16     speedstatus;             //30
//	u16     truenorthtrack[3];       //31
//	float	northvelocity;   		//32
//	float   eastvelocity;            //33
//	float   upvelocity;              //34
//	u16     positionstatus;          //35
//	u16     directionoflat;          //36
//	double  latitude;                //37
//	u16     directionoflon;          //38
//	double  longitude;               //39
//	double  altitude;                //40
//	u16     Headingstate;            //41
//	u32     baselength;              //42
//	float   roll;                    //43
//	float   pitch;                   //44
//	float   yaw;                     //45

//	float   ECEF_X;                  //46
//	float	ECEF_Y;                  //47
//	float	ECEF_Z;                  //48
//	float   geometricprecZ;          //49
//	float	positionprecZ;           //50
//	float	timeprecisionZ;          //51
//	float	verticalprecZ;           //52
//	float	horizontalprecZ;         //53
//	float	northprecisionZ;         //54
//	float	eastprecisionZ;          //55
//	float	endheightangleZ;         //56
//	u16		checksum;                //57
//	u16		checksumA;               //58
//	u16		frameindex;              //59
//} gdwrxdata912_t;

typedef union _arraytodata {
	gdwrxdata912_t	wcdata;
	uint16_t	gddata[111];
} arraytodata_t;


extern	unsigned short gfpgadata[200];
extern	navoutdata_t	gnavout;
extern	arraytodata_t	ginputdata;
extern	gdwrxdata912_t	gins912data;
extern	fpga2ins_t  fpga2ins;
/*
input:	pfpgadata -- Algorithm input parameters, FPGA 96 bytes. See Andrew's FPGA documentation for details on what each byte represents
		pnavout -- This parameter is the result of the algorithm, and the firmware code will transfer this result to the host computer
				-- Refer to the struct definition for the meaning of each variable in the struct
return: none
*/
extern	void INS912AlgorithmEntry(unsigned short *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout);

extern void AlgorithmDo();

//读取SD卡并发送
void SDTo422_00BB_send(navoutdata_t *pnavout);
extern SdDataSave_t	gSdDataSave;	




#endif
