#include "bsp_uart.h"
#include "fpgad.h"

// UART接收缓冲区相关变量 - 匹配fpgad.h中的定义
unsigned char grxbuffer[U4RX_MAXCOUNT + 2];
int grxlen = 0;
int grxst = 0;

void bsp_systick_init(uint32_t com)
{
	// 暂时禁用USART0初始化，避免与PA9引脚冲突
	// PA9已被配置为CAN1中断引脚（EXTI9）
	// 如果需要USART0功能，请使用其他引脚或重新配置引脚分配

	/* 注释掉原有的USART0配置，避免引脚冲突
	rcu_periph_clock_enable( RCU_GPIOA);

	// enable USART clock
	rcu_periph_clock_enable(RCU_USART0);

	// connect port to USARTx_Tx
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);

	// connect port to USARTx_Rx
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);

	// configure USART Tx as alternate function push-pull
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_9);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_9);

	// configure USART Rx as alternate function push-pull
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_10);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);

	// USART configure - 统一使用USART0
	usart_deinit(USART0);
	usart_baudrate_set(USART0,115200U);
	usart_receive_config(USART0, USART_RECEIVE_ENABLE);
	usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
	usart_enable(USART0);
	*/

	// 直接返回，不进行任何USART0配置
	return;
}

void bsp_systick_init01(uint32_t com)
{

	rcu_periph_clock_enable( RCU_GPIOC);
	rcu_periph_clock_enable( RCU_GPIOD);

	/* enable USART clock */
	rcu_periph_clock_enable(RCU_UART4);

	/* connect port to USARTx_Tx */
	gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_12);

	/* connect port to USARTx_Rx */
	gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_2);

	/* configure USART Tx as alternate function push-pull */
	gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_12);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

	/* configure USART Rx as alternate function push-pull */
	gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_2);
	gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);

	/* USART configure */
	usart_deinit(UART4);
	usart_baudrate_set(UART4,115200U);
	usart_receive_config(UART4, USART_RECEIVE_ENABLE);
	usart_transmit_config(UART4, USART_TRANSMIT_ENABLE);
	usart_enable(UART4);
}



/*!
    \brief      this function handles USART0 exception
    \param[in]  none
    \param[out] none
    \retval     none
*/

void UART4_IRQHandler(void)
{
    if((RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_RBNE)) &&
            (RESET != usart_flag_get(UART4, USART_FLAG_RBNE))) {
        /* receive data */
		if (grxlen < U4RX_MAXCOUNT) {
			grxbuffer[(grxst + grxlen) % U4RX_MAXCOUNT] = usart_data_receive(UART4);
			grxlen++;
		} else {
			// 缓冲区满，丢弃数据
			usart_data_receive(UART4);
		}
        //if(grxcount == U4RX_MAXCOUNT) {
            //usart_interrupt_disable(USART0, USART_INT_RBNE);
        //}
    }
    if((RESET != usart_flag_get(UART4, USART_FLAG_TBE)) &&
            (RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_TBE))) {

		//gtxcount++;
        /* transmit data */
//        usart_data_transmit(USART0, txbuffer[txcount++]);
//        if(txcount == tx_size) {
//            //usart_interrupt_disable(USART0, USART_INT_TBE);
//        }
    }
}

// UART6中断处理函数 - 暂时屏蔽，项目中未使用
void UART6_IRQHandler(void)
{
    // 简化的中断处理，仅清除中断标志
    if((RESET != usart_interrupt_flag_get(UART6, USART_INT_FLAG_RBNE)) &&
            (RESET != usart_flag_get(UART6, USART_FLAG_RBNE))) {
        // 读取数据以清除中断标志
        usart_data_receive(UART6);
    }

    if((RESET != usart_flag_get(UART6, USART_FLAG_TBE)) &&
            (RESET != usart_interrupt_flag_get(UART6, USART_INT_FLAG_TBE))) {
        // 发送中断处理 - 暂时为空
    }
}
