#include "INS_Data.h"
#include "INS912AlgorithmEntry.h"
#include "fpgad.h"

INSDataTypeDef hINSData;
FPGA_FRAME_DEF hINSFPGAData;
FPGA_FRAME_DEF hINSCANData;
uint16_t fpga_data_read_flag;
uint16_t fpga_setting_update_flag;
//AppSettingTypeDef hSetting;
//AppSettingTypeDef hDefaultSetting;
FPGA_Setting_TypeDef hFPGASetting;

LEDStateEnumTypeDef g_LEDIndicatorState;


uint32_t g_week;
double   g_second;
navoutdata_t gnavout;


void caninfupdate(navcanin_t *pcandata)
{//AGV  
	pcandata->WheelSpeed_Front_Left = gcanInfo.data.WheelSpeed_Front_Left;
	pcandata->WheelSpeed_Front_Right = gcanInfo.data.WheelSpeed_Front_Right;
	pcandata->WheelSpeed_Back_Left = gcanInfo.data.WheelSpeed_Back_Left;
	pcandata->WheelSpeed_Back_Right = gcanInfo.data.WheelSpeed_Back_Right;
	pcandata->Gear = gcanInfo.data.Gear;	//
	pcandata->counter = gcanInfo.counter;
}




