T3624 000:012.856   SEGGER J-Link V7.22b Log File
T3624 000:013.111   DLL Compiled: Jun 17 2021 17:22:49
T3624 000:013.135   Logging started @ 2025-06-11 11:56
T3624 000:013.155 - 13.166ms
T3624 000:013.186 JLINK_SetWarnOutHandler(...)
T3624 000:013.213 - 0.040ms
T3624 000:013.237 JLINK_OpenEx(...)
T3624 000:014.545   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T3624 000:014.807   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T3624 000:014.968   Decompressing FW timestamp took 114 us
T3624 000:020.665   Hardware: V9.40
T3624 000:020.709   S/N: 59406895
T3624 000:020.737   OEM: SEGGER
T3624 000:020.764   Feature(s): RDI, GDB, FlashDL, FlashB<PERSON>, JFlash
T3624 000:021.533   TELNET listener socket opened on port 19021
T3624 000:021.700   WEBSRV Starting webserver
T3624 000:021.842   WEBSRV Webserver running on local port 19080
T3624 000:021.877 - 8.648ms returns "O.K."
T3624 000:021.902 JLINK_GetEmuCaps()
T3624 000:021.922 - 0.028ms returns 0xB9FF7BBF
T3624 000:021.943 JLINK_TIF_GetAvailable(...)
T3624 000:022.083 - 0.152ms
T3624 000:022.107 JLINK_SetErrorOutHandler(...)
T3624 000:022.126 - 0.027ms
T3624 000:022.159 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T3624 000:033.080   Device "GD32F450II" selected.
T3624 000:033.942 - 11.823ms returns 0x00
T3624 000:034.015 JLINK_ExecCommand("Device = GD32F470II", ...). 
T3624 000:035.088   Device "GD32F450II" selected.
T3624 000:035.652 - 1.589ms returns 0x00
T3624 000:035.686 JLINK_GetHardwareVersion()
T3624 000:035.706 - 0.028ms returns 94000
T3624 000:035.725 JLINK_GetDLLVersion()
T3624 000:035.743 - 0.027ms returns 72202
T3624 000:035.763 JLINK_GetOEMString(...)
T3624 000:035.782 JLINK_GetFirmwareString(...)
T3624 000:035.806 - 0.032ms
T3624 000:035.838 JLINK_GetDLLVersion()
T3624 000:035.859 - 0.030ms returns 72202
T3624 000:035.879 JLINK_GetCompileDateTime()
T3624 000:035.897 - 0.026ms
T3624 000:035.920 JLINK_GetFirmwareString(...)
T3624 000:035.941 - 0.029ms
T3624 000:035.964 JLINK_GetHardwareVersion()
T3624 000:035.984 - 0.029ms returns 94000
T3624 000:036.006 JLINK_GetSN()
T3624 000:036.027 - 0.029ms returns 59406895
T3624 000:036.049 JLINK_GetOEMString(...)
T3624 000:036.084 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T3624 000:036.531 - 0.466ms returns 0x00
T3624 000:036.561 JLINK_HasError()
T3624 000:036.594 JLINK_SetSpeed(2000)
T3624 000:036.668 - 0.087ms
T3624 000:036.692 JLINK_GetId()
T3624 000:037.353   Found SW-DP with ID 0x2BA01477
T3624 000:039.853   DPIDR: 0x2BA01477
T3624 000:039.944   Scanning AP map to find all available APs
T3624 000:040.449   AP[1]: Stopped AP scan as end of AP map has been reached
T3624 000:040.485   AP[0]: AHB-AP (IDR: 0x24770011)
T3624 000:040.513   Iterating through AP map to find AHB-AP to use
T3624 000:041.314   AP[0]: Core found
T3624 000:041.351   AP[0]: AHB-AP ROM base: 0xE00FF000
T3624 000:041.861   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T3624 000:041.924   Found Cortex-M4 r0p1, Little endian.
T3624 000:143.510   -- Max. mem block: 0x00010E60
T3624 000:143.694   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:144.069   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:144.503   CPU_ReadMem(4 bytes @ 0xE0002000)
T3624 000:144.898   FPUnit: 6 code (BP) slots and 2 literal slots
T3624 000:144.932   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3624 000:145.294   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:145.649   CPU_ReadMem(4 bytes @ 0xE0001000)
T3624 000:146.041   CPU_WriteMem(4 bytes @ 0xE0001000)
T3624 000:146.400   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3624 000:146.774   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3624 000:147.111   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3624 000:147.484   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3624 000:147.824   CoreSight components:
T3624 000:147.859   ROMTbl[0] @ E00FF000
T3624 000:147.888   CPU_ReadMem(64 bytes @ 0xE00FF000)
T3624 000:148.696   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T3624 000:149.384   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T3624 000:149.449   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T3624 000:150.227   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T3624 000:150.280   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T3624 000:150.815   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T3624 000:150.853   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T3624 000:151.412   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T3624 000:151.451   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T3624 000:152.045   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T3624 000:152.100   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T3624 000:152.692   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T3624 000:152.919 - 116.241ms returns 0x2BA01477
T3624 000:152.948 JLINK_GetDLLVersion()
T3624 000:152.968 - 0.027ms returns 72202
T3624 000:153.052 JLINK_CORE_GetFound()
T3624 000:153.078 - 0.035ms returns 0xE0000FF
T3624 000:153.098 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3624 000:153.118   Value=0xE00FF000
T3624 000:153.144 - 0.055ms returns 0
T3624 000:153.170 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3624 000:153.191   Value=0xE00FF000
T3624 000:153.217 - 0.056ms returns 0
T3624 000:153.236 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T3624 000:153.254   Value=0x00000000
T3624 000:153.280 - 0.053ms returns 0
T3624 000:153.307 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T3624 000:153.345   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T3624 000:153.814   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T3624 000:153.848 - 0.549ms returns 16 (0x10)
T3624 000:153.868 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T3624 000:153.888   Value=0x00000000
T3624 000:153.914 - 0.055ms returns 0
T3624 000:153.933 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T3624 000:153.952   Value=0x********
T3624 000:153.977 - 0.053ms returns 0
T3624 000:153.997 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T3624 000:154.014   Value=0x********
T3624 000:154.040 - 0.052ms returns 0
T3624 000:154.059 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T3624 000:154.077   Value=0xE0001000
T3624 000:154.103 - 0.053ms returns 0
T3624 000:154.122 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T3624 000:154.139   Value=0xE0002000
T3624 000:154.166 - 0.060ms returns 0
T3624 000:154.191 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T3624 000:154.210   Value=0xE000E000
T3624 000:154.235 - 0.052ms returns 0
T3624 000:154.253 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T3624 000:154.273   Value=0xE000EDF0
T3624 000:154.298 - 0.053ms returns 0
T3624 000:154.316 JLINK_GetDebugInfo(0x01 = Unknown)
T3624 000:154.335   Value=0x00000001
T3624 000:154.361 - 0.052ms returns 0
T3624 000:154.379 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T3624 000:154.402   CPU_ReadMem(4 bytes @ 0xE000ED00)
T3624 000:154.735   Data:  41 C2 0F 41
T3624 000:154.768   Debug reg: CPUID
T3624 000:154.793 - 0.423ms returns 1 (0x1)
T3624 000:154.814 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T3624 000:154.832   Value=0x00000000
T3624 000:154.858 - 0.053ms returns 0
T3624 000:154.877 JLINK_HasError()
T3624 000:154.897 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T3624 000:154.917 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T3624 000:154.935 JLINK_Reset()
T3624 000:154.966   CPU is running
T3624 000:154.994   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:155.307   CPU is running
T3624 000:155.338   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:155.667   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T3624 000:155.952   Reset: Reset device via AIRCR.SYSRESETREQ.
T3624 000:155.984   CPU is running
T3624 000:156.009   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T3624 000:209.449   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:209.878   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:210.372   CPU is running
T3624 000:210.410   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:210.851   CPU is running
T3624 000:210.946   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:216.769   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:220.771   CPU_WriteMem(4 bytes @ 0xE0002000)
T3624 000:221.160   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3624 000:221.487   CPU_ReadMem(4 bytes @ 0xE0001000)
T3624 000:221.792 - 66.870ms
T3624 000:221.829 JLINK_HasError()
T3624 000:221.872 JLINK_ReadReg(R15 (PC))
T3624 000:221.936 - 0.092ms returns 0x080001C4
T3624 000:221.975 JLINK_ReadReg(XPSR)
T3624 000:222.015 - 0.049ms returns 0x01000000
T3624 000:222.052 JLINK_Halt()
T3624 000:222.091 - 0.048ms returns 0x00
T3624 000:222.129 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T3624 000:222.173   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:222.567   Data:  03 00 03 00
T3624 000:222.597   Debug reg: DHCSR
T3624 000:222.621 - 0.501ms returns 1 (0x1)
T3624 000:222.642 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T3624 000:222.684   Debug reg: DHCSR
T3624 000:222.996   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:223.375 - 0.745ms returns 0 (0x00000000)
T3624 000:223.399 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T3624 000:223.416   Debug reg: DEMCR
T3624 000:223.447   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:223.814 - 0.429ms returns 0 (0x00000000)
T3624 000:223.874 JLINK_GetHWStatus(...)
T3624 000:224.040 - 0.179ms returns 0
T3624 000:224.077 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T3624 000:224.096 - 0.028ms returns 0x06
T3624 000:224.116 JLINK_GetNumBPUnits(Type = 0xF0)
T3624 000:224.134 - 0.027ms returns 0x2000
T3624 000:224.153 JLINK_GetNumWPUnits()
T3624 000:224.171 - 0.026ms returns 4
T3624 000:224.273 JLINK_GetSpeed()
T3624 000:224.304 - 0.039ms returns 2000
T3624 000:224.333 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3624 000:224.359   CPU_ReadMem(4 bytes @ 0xE000E004)
T3624 000:224.684   Data:  02 00 00 00
T3624 000:224.716 - 0.391ms returns 1 (0x1)
T3624 000:224.734 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3624 000:224.754   CPU_ReadMem(4 bytes @ 0xE000E004)
T3624 000:225.149   Data:  02 00 00 00
T3624 000:225.203 - 0.476ms returns 1 (0x1)
T3624 000:225.222 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T3624 000:225.241   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T3624 000:225.272   CPU_WriteMem(28 bytes @ 0xE0001000)
T3624 000:225.797 - 0.589ms returns 0x1C
T3624 000:225.830 JLINK_HasError()
T3624 000:225.850 JLINK_ReadReg(R15 (PC))
T3624 000:225.869 - 0.028ms returns 0x080001C4
T3624 000:225.887 JLINK_ReadReg(XPSR)
T3624 000:225.907 - 0.028ms returns 0x01000000
T3624 000:230.029 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T3624 000:230.103   Data:  00 00 00 00
T3624 000:230.149   Debug reg: DWT_CYCCNT
T3624 000:230.173 - 0.152ms returns 4 (0x4)
T3624 000:312.193 JLINK_HasError()
T3624 000:312.243 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T3624 000:312.263 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T3624 000:312.282 JLINK_Reset()
T3624 000:312.310   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:312.682   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:313.081   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T3624 000:313.581   Reset: Reset device via AIRCR.SYSRESETREQ.
T3624 000:313.631   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T3624 000:366.356   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:366.787   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:367.162   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3624 000:367.538   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3624 000:373.845   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3624 000:377.622   CPU_WriteMem(4 bytes @ 0xE0002000)
T3624 000:378.073   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3624 000:378.417   CPU_ReadMem(4 bytes @ 0xE0001000)
T3624 000:378.757 - 66.497ms
T3624 000:378.919 JLINK_HasError()
T3624 000:378.944 JLINK_ReadReg(R15 (PC))
T3624 000:378.966 - 0.031ms returns 0x080001C4
T3624 000:378.986 JLINK_ReadReg(XPSR)
T3624 000:379.006 - 0.030ms returns 0x01000000
T3624 000:379.117 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T3624 000:379.146   CPU_ReadMem(64 bytes @ 0x080001C0)
T3624 000:379.934    -- Updating C cache (64 bytes @ 0x080001C0)
T3624 000:379.971    -- Read from C cache (60 bytes @ 0x080001C4)
T3624 000:379.999   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:380.026 - 0.918ms returns 60 (0x3C)
T3624 000:380.046 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T3624 000:380.067    -- Read from C cache (2 bytes @ 0x080001C4)
T3624 000:380.094   Data:  06 48
T3624 000:380.121 - 0.083ms returns 2 (0x2)
T3624 000:380.185 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T3624 000:380.207    -- Read from C cache (2 bytes @ 0x080001C6)
T3624 000:380.234   Data:  80 47
T3624 000:380.261 - 0.084ms returns 2 (0x2)
T3624 000:380.305 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T3624 000:380.328    -- Read from C cache (2 bytes @ 0x080001C6)
T3624 000:380.355   Data:  80 47
T3624 000:380.382 - 0.085ms returns 2 (0x2)
T3624 000:380.401 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T3624 000:380.421   CPU_ReadMem(64 bytes @ 0x08000200)
T3624 000:381.220    -- Updating C cache (64 bytes @ 0x08000200)
T3624 000:381.253    -- Read from C cache (60 bytes @ 0x080001C8)
T3624 000:381.280   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:381.308 - 0.915ms returns 60 (0x3C)
T3624 000:381.327 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T3624 000:381.348    -- Read from C cache (2 bytes @ 0x080001C8)
T3624 000:381.375   Data:  06 48
T3624 000:381.401 - 0.084ms returns 2 (0x2)
T3624 000:381.441 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T3624 000:381.462    -- Read from C cache (60 bytes @ 0x080001C8)
T3624 000:381.490   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:381.517 - 0.085ms returns 60 (0x3C)
T3624 000:381.535 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T3624 000:381.556    -- Read from C cache (2 bytes @ 0x080001C8)
T3624 000:381.582   Data:  06 48
T3624 000:381.608 - 0.082ms returns 2 (0x2)
T3624 000:381.628 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T3624 000:381.647    -- Read from C cache (2 bytes @ 0x080001CA)
T3624 000:381.674   Data:  00 47
T3624 000:381.702 - 0.082ms returns 2 (0x2)
T3624 000:381.733 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T3624 000:381.755    -- Read from C cache (2 bytes @ 0x080001CA)
T3624 000:381.782   Data:  00 47
T3624 000:381.809 - 0.086ms returns 2 (0x2)
T3624 000:381.830 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T3624 000:381.849    -- Read from C cache (60 bytes @ 0x080001CC)
T3624 000:381.877   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:381.903 - 0.082ms returns 60 (0x3C)
T3624 000:381.922 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T3624 000:381.942    -- Read from C cache (2 bytes @ 0x080001CC)
T3624 000:381.969   Data:  FE E7
T3624 000:381.996 - 0.087ms returns 2 (0x2)
T3624 000:382.034 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T3624 000:382.063    -- Read from C cache (60 bytes @ 0x080001CC)
T3624 000:382.100   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:382.135 - 0.113ms returns 60 (0x3C)
T3624 000:382.162 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T3624 000:382.188    -- Read from C cache (2 bytes @ 0x080001CC)
T3624 000:382.223   Data:  FE E7
T3624 000:382.258 - 0.108ms returns 2 (0x2)
T3624 000:382.285 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T3624 000:382.310    -- Read from C cache (2 bytes @ 0x080001CE)
T3624 000:382.350   Data:  FE E7
T3624 000:382.388 - 0.114ms returns 2 (0x2)
T3624 000:382.434 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T3624 000:382.458    -- Read from C cache (2 bytes @ 0x080001CE)
T3624 000:382.485   Data:  FE E7
T3624 000:382.512 - 0.088ms returns 2 (0x2)
T3624 000:382.532 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T3624 000:382.556    -- Read from C cache (60 bytes @ 0x080001D0)
T3624 000:382.673   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T3624 000:382.700 - 0.180ms returns 60 (0x3C)
T3624 000:382.723 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T3624 000:382.750    -- Read from C cache (2 bytes @ 0x080001D0)
T3624 000:382.780   Data:  FE E7
T3624 000:382.827 - 0.115ms returns 2 (0x2)
T3624 001:693.522 JLINK_ReadMemEx(0x0800D7B2, 0x2 Bytes, Flags = 0x02000000)
T3624 001:693.566   CPU_ReadMem(64 bytes @ 0x0800D780)
T3624 001:694.404    -- Updating C cache (64 bytes @ 0x0800D780)
T3624 001:694.544    -- Read from C cache (2 bytes @ 0x0800D7B2)
T3624 001:694.572   Data:  07 F0
T3624 001:694.597 - 1.083ms returns 2 (0x2)
T3624 001:694.617 JLINK_ReadMemEx(0x0800D7B4, 0x3C Bytes, Flags = 0x02000000)
T3624 001:694.638   CPU_ReadMem(64 bytes @ 0x0800D7C0)
T3624 001:695.588    -- Updating C cache (64 bytes @ 0x0800D7C0)
T3624 001:695.636    -- Read from C cache (60 bytes @ 0x0800D7B4)
T3624 001:695.662   Data:  1B FB 00 22 11 46 35 20 07 F0 12 FE 14 48 C0 88 ...
T3624 001:695.688 - 1.079ms returns 60 (0x3C)
T3624 001:695.708 JLINK_ReadMemEx(0x0800D7B4, 0x2 Bytes, Flags = 0x02000000)
T3624 001:695.729    -- Read from C cache (2 bytes @ 0x0800D7B4)
T3624 001:695.754   Data:  1B FB
T3624 001:695.780 - 0.080ms returns 2 (0x2)
T3624 001:846.605 JLINK_HasError()
T3624 001:846.668 JLINK_ReadReg(R0)
T3624 001:847.047 - 0.401ms returns 0x00000000
T3624 001:847.083 JLINK_ReadReg(R1)
T3624 001:847.104 - 0.031ms returns 0x000186A0
T3624 001:847.124 JLINK_ReadReg(R2)
T3624 001:847.142 - 0.028ms returns 0x0000000A
T3624 001:847.163 JLINK_ReadReg(R3)
T3624 001:847.182 - 0.028ms returns 0x00000080
T3624 001:847.201 JLINK_ReadReg(R4)
T3624 001:847.221 - 0.028ms returns 0x00000000
T3624 001:847.240 JLINK_ReadReg(R5)
T3624 001:847.260 - 0.030ms returns 0x08016B34
T3624 001:847.280 JLINK_ReadReg(R6)
T3624 001:847.298 - 0.028ms returns 0x00000000
T3624 001:847.319 JLINK_ReadReg(R7)
T3624 001:847.338 - 0.028ms returns 0x00000000
T3624 001:847.357 JLINK_ReadReg(R8)
T3624 001:847.390 - 0.042ms returns 0x00000000
T3624 001:847.410 JLINK_ReadReg(R9)
T3624 001:847.430 - 0.029ms returns 0x20000348
T3624 001:847.449 JLINK_ReadReg(R10)
T3624 001:847.469 - 0.029ms returns 0x00000000
T3624 001:847.489 JLINK_ReadReg(R11)
T3624 001:847.508 - 0.027ms returns 0x00000000
T3624 001:847.528 JLINK_ReadReg(R12)
T3624 001:847.547 - 0.028ms returns 0x000001E0
T3624 001:847.566 JLINK_ReadReg(R13 (SP))
T3624 001:847.586 - 0.029ms returns 0x20008038
T3624 001:847.605 JLINK_ReadReg(R14)
T3624 001:847.625 - 0.029ms returns 0xFFFFFFFF
T3624 001:847.645 JLINK_ReadReg(R15 (PC))
T3624 001:847.664 - 0.027ms returns 0x080001C4
T3624 001:847.684 JLINK_ReadReg(XPSR)
T3624 001:847.703 - 0.028ms returns 0x01000000
T3624 001:847.722 JLINK_ReadReg(MSP)
T3624 001:847.742 - 0.029ms returns 0x20008038
T3624 001:847.761 JLINK_ReadReg(PSP)
T3624 001:847.781 - 0.029ms returns 0x20001000
T3624 001:847.801 JLINK_ReadReg(CFBP)
T3624 001:847.819 - 0.027ms returns 0x00000000
T3624 001:847.839 JLINK_ReadReg(FPSCR)
T3624 001:853.209 - 5.403ms returns 0x00000000
T3624 001:853.259 JLINK_ReadReg(FPS0)
T3624 001:853.283 - 0.033ms returns 0x00000000
T3624 001:853.304 JLINK_ReadReg(FPS1)
T3624 001:853.324 - 0.030ms returns 0xC10E8480
T3624 001:853.345 JLINK_ReadReg(FPS2)
T3624 001:853.365 - 0.029ms returns 0x55555593
T3624 001:853.385 JLINK_ReadReg(FPS3)
T3624 001:853.405 - 0.029ms returns 0x3FE55555
T3624 001:853.425 JLINK_ReadReg(FPS4)
T3624 001:853.445 - 0.029ms returns 0x004D402C
T3624 001:853.465 JLINK_ReadReg(FPS5)
T3624 001:853.485 - 0.029ms returns 0x77490280
T3624 001:853.506 JLINK_ReadReg(FPS6)
T3624 001:853.525 - 0.029ms returns 0xD8863040
T3624 001:853.547 JLINK_ReadReg(FPS7)
T3624 001:853.566 - 0.028ms returns 0x6F4222A1
T3624 001:853.587 JLINK_ReadReg(FPS8)
T3624 001:853.607 - 0.028ms returns 0x1A100861
T3624 001:853.627 JLINK_ReadReg(FPS9)
T3624 001:853.647 - 0.029ms returns 0x4C607626
T3624 001:853.667 JLINK_ReadReg(FPS10)
T3624 001:853.687 - 0.029ms returns 0x790C9808
T3624 001:853.711 JLINK_ReadReg(FPS11)
T3624 001:853.734 - 0.032ms returns 0x432099A0
T3624 001:853.755 JLINK_ReadReg(FPS12)
T3624 001:853.775 - 0.029ms returns 0x8A2080C8
T3624 001:853.795 JLINK_ReadReg(FPS13)
T3624 001:853.814 - 0.029ms returns 0x1B944171
T3624 001:853.836 JLINK_ReadReg(FPS14)
T3624 001:853.855 - 0.029ms returns 0x4502A8C8
T3624 001:853.877 JLINK_ReadReg(FPS15)
T3624 001:853.896 - 0.028ms returns 0x192E0105
T3624 001:853.918 JLINK_ReadReg(FPS16)
T3624 001:853.940 - 0.033ms returns 0x55555555
T3624 001:853.964 JLINK_ReadReg(FPS17)
T3624 001:853.983 - 0.029ms returns 0x40A38155
T3624 001:854.003 JLINK_ReadReg(FPS18)
T3624 001:854.024 - 0.030ms returns 0x00000000
T3624 001:854.044 JLINK_ReadReg(FPS19)
T3624 001:854.064 - 0.029ms returns 0x00000000
T3624 001:854.084 JLINK_ReadReg(FPS20)
T3624 001:854.105 - 0.029ms returns 0x00000000
T3624 001:854.125 JLINK_ReadReg(FPS21)
T3624 001:854.145 - 0.029ms returns 0x00000000
T3624 001:854.165 JLINK_ReadReg(FPS22)
T3624 001:854.185 - 0.029ms returns 0x00000000
T3624 001:854.206 JLINK_ReadReg(FPS23)
T3624 001:854.225 - 0.029ms returns 0x3FF00000
T3624 001:854.247 JLINK_ReadReg(FPS24)
T3624 001:854.266 - 0.028ms returns 0x8624820A
T3624 001:854.287 JLINK_ReadReg(FPS25)
T3624 001:854.306 - 0.029ms returns 0xC00F1403
T3624 001:854.327 JLINK_ReadReg(FPS26)
T3624 001:854.347 - 0.030ms returns 0x202A04C0
T3624 001:854.367 JLINK_ReadReg(FPS27)
T3624 001:854.388 - 0.029ms returns 0x1D2712AA
T3624 001:854.408 JLINK_ReadReg(FPS28)
T3624 001:854.428 - 0.030ms returns 0x544840BB
T3624 001:854.449 JLINK_ReadReg(FPS29)
T3624 001:854.469 - 0.030ms returns 0x01482220
T3624 001:854.489 JLINK_ReadReg(FPS30)
T3624 001:854.509 - 0.029ms returns 0x0247A931
T3624 001:854.530 JLINK_ReadReg(FPS31)
T3624 001:854.550 - 0.030ms returns 0x608C08A3
T3624 001:923.149 JLINK_ReadMemEx(0x2001957E, 0x2 Bytes, Flags = 0x02000000)
T3624 001:923.226   CPU_ReadMem(64 bytes @ 0x20019540)
T3624 001:924.100    -- Updating C cache (64 bytes @ 0x20019540)
T3624 001:924.147    -- Read from C cache (2 bytes @ 0x2001957E)
T3624 001:924.174   Data:  00 00
T3624 001:924.200 - 1.061ms returns 2 (0x2)
T3624 001:924.306 JLINK_ReadMemEx(0x2001957E, 0x2 Bytes, Flags = 0x02000000)
T3624 001:924.335    -- Read from C cache (2 bytes @ 0x2001957E)
T3624 001:924.362   Data:  00 00
T3624 001:924.388 - 0.091ms returns 2 (0x2)
T3624 001:924.426 JLINK_ReadMemEx(0x2001957E, 0x2 Bytes, Flags = 0x02000000)
T3624 001:924.449    -- Read from C cache (2 bytes @ 0x2001957E)
T3624 001:924.476   Data:  00 00
T3624 001:924.503 - 0.085ms returns 2 (0x2)
T3624 001:930.577 JLINK_ReadMemEx(0x20019584, 0x4 Bytes, Flags = 0x02000000)
T3624 001:930.633   CPU_ReadMem(64 bytes @ 0x20019580)
T3624 001:931.430    -- Updating C cache (64 bytes @ 0x20019580)
T3624 001:931.483    -- Read from C cache (4 bytes @ 0x20019584)
T3624 001:931.514   Data:  00 00 00 00
T3624 001:931.543 - 0.975ms returns 4 (0x4)
T3624 001:931.665 JLINK_ReadMemEx(0x20019584, 0x4 Bytes, Flags = 0x02000000)
T3624 001:931.720    -- Read from C cache (4 bytes @ 0x20019584)
T3624 001:931.748   Data:  00 00 00 00
T3624 001:931.776 - 0.119ms returns 4 (0x4)
T3624 001:931.830 JLINK_ReadMemEx(0x20019584, 0x4 Bytes, Flags = 0x02000000)
T3624 001:931.863    -- Read from C cache (4 bytes @ 0x20019584)
T3624 001:931.896   Data:  00 00 00 00
T3624 001:931.923 - 0.103ms returns 4 (0x4)
T3624 001:948.451 JLINK_ReadMemEx(0x2001957A, 0x4 Bytes, Flags = 0x02000000)
T3624 001:948.594    -- Read from C cache (4 bytes @ 0x2001957A)
T3624 001:948.634   Data:  00 00 00 00
T3624 001:948.670 - 0.232ms returns 4 (0x4)
T3624 001:948.724 JLINK_ReadMemEx(0x2001957A, 0x4 Bytes, Flags = 0x02000000)
T3624 001:948.758    -- Read from C cache (4 bytes @ 0x2001957A)
T3624 001:948.796   Data:  00 00 00 00
T3624 001:948.833 - 0.121ms returns 4 (0x4)
T3624 001:948.884 JLINK_ReadMemEx(0x2001957A, 0x4 Bytes, Flags = 0x02000000)
T3624 001:948.918    -- Read from C cache (4 bytes @ 0x2001957A)
T3624 001:948.960   Data:  00 00 00 00
T3624 001:949.011 - 0.138ms returns 4 (0x4)
T3624 001:965.814 JLINK_ReadMemEx(0x20019588, 0x4 Bytes, Flags = 0x02000000)
T3624 001:965.897    -- Read from C cache (4 bytes @ 0x20019588)
T3624 001:965.944   Data:  00 00 00 00
T3624 001:965.979 - 0.178ms returns 4 (0x4)
T3624 001:966.069 JLINK_ReadMemEx(0x20019588, 0x4 Bytes, Flags = 0x02000000)
T3624 001:966.100    -- Read from C cache (4 bytes @ 0x20019588)
T3624 001:966.133   Data:  00 00 00 00
T3624 001:966.165 - 0.106ms returns 4 (0x4)
T3624 001:966.254 JLINK_ReadMemEx(0x20019588, 0x4 Bytes, Flags = 0x02000000)
T3624 001:966.279    -- Read from C cache (4 bytes @ 0x20019588)
T3624 001:966.314   Data:  00 00 00 00
T3624 001:966.403 - 0.169ms returns 4 (0x4)
T3624 001:975.641 JLINK_ReadMemEx(0x2001958C, 0x4 Bytes, Flags = 0x02000000)
T3624 001:975.703    -- Read from C cache (4 bytes @ 0x2001958C)
T3624 001:975.730   Data:  00 00 00 00
T3624 001:975.755 - 0.122ms returns 4 (0x4)
T3624 001:975.786 JLINK_ReadMemEx(0x2001958C, 0x4 Bytes, Flags = 0x02000000)
T3624 001:975.808    -- Read from C cache (4 bytes @ 0x2001958C)
T3624 001:975.834   Data:  00 00 00 00
T3624 001:975.861 - 0.082ms returns 4 (0x4)
T3624 001:975.892 JLINK_ReadMemEx(0x2001958C, 0x4 Bytes, Flags = 0x02000000)
T3624 001:975.914    -- Read from C cache (4 bytes @ 0x2001958C)
T3624 001:975.941   Data:  00 00 00 00
T3624 001:975.965 - 0.081ms returns 4 (0x4)
T3624 001:985.640 JLINK_ReadMemEx(0x20019572, 0x4 Bytes, Flags = 0x02000000)
T3624 001:985.719    -- Read from C cache (4 bytes @ 0x20019572)
T3624 001:985.749   Data:  00 00 00 00
T3624 001:985.776 - 0.145ms returns 4 (0x4)
T3624 001:985.815 JLINK_ReadMemEx(0x20019572, 0x4 Bytes, Flags = 0x02000000)
T3624 001:985.839    -- Read from C cache (4 bytes @ 0x20019572)
T3624 001:985.866   Data:  00 00 00 00
T3624 001:985.893 - 0.087ms returns 4 (0x4)
T3624 001:985.933 JLINK_ReadMemEx(0x20019572, 0x4 Bytes, Flags = 0x02000000)
T3624 001:985.954    -- Read from C cache (4 bytes @ 0x20019572)
T3624 001:985.984   Data:  00 00 00 00
T3624 001:986.011 - 0.087ms returns 4 (0x4)
T3624 002:048.752 JLINK_ReadMemEx(0x200000A8, 0x4 Bytes, Flags = 0x02000000)
T3624 002:048.878   CPU_ReadMem(64 bytes @ 0x20000080)
T3624 002:049.756    -- Updating C cache (64 bytes @ 0x20000080)
T3624 002:049.833    -- Read from C cache (4 bytes @ 0x200000A8)
T3624 002:049.864   Data:  00 00 00 00
T3624 002:049.895 - 1.155ms returns 4 (0x4)
T3624 002:050.216 JLINK_ReadMemEx(0x200000A8, 0x4 Bytes, Flags = 0x02000000)
T3624 002:050.244    -- Read from C cache (4 bytes @ 0x200000A8)
T3624 002:050.271   Data:  00 00 00 00
T3624 002:050.297 - 0.090ms returns 4 (0x4)
T3624 002:050.337 JLINK_ReadMemEx(0x200000A8, 0x4 Bytes, Flags = 0x02000000)
T3624 002:050.369    -- Read from C cache (4 bytes @ 0x200000A8)
T3624 002:050.396   Data:  00 00 00 00
T3624 002:050.423 - 0.094ms returns 4 (0x4)
T3624 002:060.300 JLINK_ReadMemEx(0x200000AC, 0x4 Bytes, Flags = 0x02000000)
T3624 002:060.351    -- Read from C cache (4 bytes @ 0x200000AC)
T3624 002:060.379   Data:  00 00 00 00
T3624 002:060.404 - 0.112ms returns 4 (0x4)
T3624 002:060.445 JLINK_ReadMemEx(0x200000AC, 0x4 Bytes, Flags = 0x02000000)
T3624 002:060.467    -- Read from C cache (4 bytes @ 0x200000AC)
T3624 002:060.493   Data:  00 00 00 00
T3624 002:060.520 - 0.083ms returns 4 (0x4)
T3624 002:060.552 JLINK_ReadMemEx(0x200000AC, 0x4 Bytes, Flags = 0x02000000)
T3624 002:060.574    -- Read from C cache (4 bytes @ 0x200000AC)
T3624 002:060.621   Data:  00 00 00 00
T3624 002:060.662 - 0.117ms returns 4 (0x4)
T3624 002:072.482 JLINK_ReadMemEx(0x20000028, 0x4 Bytes, Flags = 0x02000000)
T3624 002:072.554   CPU_ReadMem(64 bytes @ 0x20000000)
T3624 002:073.352    -- Updating C cache (64 bytes @ 0x20000000)
T3624 002:073.396    -- Read from C cache (4 bytes @ 0x20000028)
T3624 002:073.424   Data:  00 00 00 00
T3624 002:073.451 - 0.977ms returns 4 (0x4)
T3624 002:073.497 JLINK_ReadMemEx(0x20000028, 0x4 Bytes, Flags = 0x02000000)
T3624 002:073.523    -- Read from C cache (4 bytes @ 0x20000028)
T3624 002:073.555   Data:  00 00 00 00
T3624 002:073.582 - 0.094ms returns 4 (0x4)
T3624 002:073.617 JLINK_ReadMemEx(0x20000028, 0x4 Bytes, Flags = 0x02000000)
T3624 002:073.638    -- Read from C cache (4 bytes @ 0x20000028)
T3624 002:073.668   Data:  00 00 00 00
T3624 002:073.694 - 0.086ms returns 4 (0x4)
T3624 002:088.246 JLINK_ReadMemEx(0x20000114, 0x4 Bytes, Flags = 0x02000000)
T3624 002:088.351   CPU_ReadMem(64 bytes @ 0x20000100)
T3624 002:089.195    -- Updating C cache (64 bytes @ 0x20000100)
T3624 002:089.250    -- Read from C cache (4 bytes @ 0x20000114)
T3624 002:089.278   Data:  00 00 00 00
T3624 002:089.305 - 1.068ms returns 4 (0x4)
T3624 002:089.383 JLINK_ReadMemEx(0x20000114, 0x4 Bytes, Flags = 0x02000000)
T3624 002:089.409    -- Read from C cache (4 bytes @ 0x20000114)
T3624 002:089.437   Data:  00 00 00 00
T3624 002:089.464 - 0.090ms returns 4 (0x4)
T3624 002:089.514 JLINK_ReadMemEx(0x20000114, 0x4 Bytes, Flags = 0x02000000)
T3624 002:089.537    -- Read from C cache (4 bytes @ 0x20000114)
T3624 002:089.565   Data:  00 00 00 00
T3624 002:089.591 - 0.085ms returns 4 (0x4)
T3624 002:107.666 JLINK_ReadMemEx(0x20000030, 0x4 Bytes, Flags = 0x02000000)
T3624 002:107.745    -- Read from C cache (4 bytes @ 0x20000030)
T3624 002:107.773   Data:  00 00 00 00
T3624 002:107.800 - 0.144ms returns 4 (0x4)
T3624 002:107.850 JLINK_ReadMemEx(0x20000030, 0x4 Bytes, Flags = 0x02000000)
T3624 002:107.873    -- Read from C cache (4 bytes @ 0x20000030)
T3624 002:107.901   Data:  00 00 00 00
T3624 002:107.927 - 0.086ms returns 4 (0x4)
T3624 002:107.968 JLINK_ReadMemEx(0x20000030, 0x4 Bytes, Flags = 0x02000000)
T3624 002:107.992    -- Read from C cache (4 bytes @ 0x20000030)
T3624 002:108.020   Data:  00 00 00 00
T3624 002:108.046 - 0.087ms returns 4 (0x4)
T3624 002:125.248 JLINK_ReadMemEx(0x20000790, 0x1 Bytes, Flags = 0x02000000)
T3624 002:125.372   CPU_ReadMem(64 bytes @ 0x20000780)
T3624 002:126.195    -- Updating C cache (64 bytes @ 0x20000780)
T3624 002:126.234    -- Read from C cache (1 bytes @ 0x20000790)
T3624 002:126.263   Data:  71
T3624 002:126.290 - 1.051ms returns 1 (0x1)
T3624 002:126.350 JLINK_ReadMemEx(0x20000790, 0x1 Bytes, Flags = 0x02000000)
T3624 002:126.376    -- Read from C cache (1 bytes @ 0x20000790)
T3624 002:126.404   Data:  71
T3624 002:126.432 - 0.090ms returns 1 (0x1)
T3624 002:126.469 JLINK_ReadMemEx(0x20000790, 0x1 Bytes, Flags = 0x02000000)
T3624 002:126.492    -- Read from C cache (1 bytes @ 0x20000790)
T3624 002:126.527   Data:  71
T3624 002:126.553 - 0.092ms returns 1 (0x1)
