/********************************** (C) COPYRIGHT *******************************
* File Name          : CH378INC.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/11/25
* Description        : C Define for CH378
*					   Website: http://wch.cn
*					   Email:   <EMAIL>
*******************************************************************************/



#ifndef __CH378INC_H__
#define __CH378INC_H__

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************/
/* 芯片版本定义 */
#define	DEF_CH378_VER			0x42			/* 当前芯片及固件版本,高2位为芯片版本,低6位为固件版本 */

/***********************************************************************************************************************/
/* 常用类型和常量定义 */

#ifndef		TRUE
#define		TRUE	1
#define		FALSE	0
#endif
#ifndef		NULL
#define		NULL	0
#endif

#ifndef UINT8
typedef unsigned char				UINT8;
#endif
#ifndef UINT16
typedef unsigned short				UINT16;
#endif
#ifndef UINT32
typedef unsigned long				UINT32;
#endif
#ifndef PUINT8
typedef unsigned char				*PUINT8;
#endif
#ifndef PUINT16
typedef unsigned short				*PUINT16;
#endif
#ifndef PUINT32
typedef unsigned long				*PUINT32;
#endif
#ifndef UINT8V
typedef unsigned char volatile		UINT8V;
#endif
#ifndef PUINT8V
typedef unsigned char volatile		*PUINT8V;
#endif

/* ********************************************************************************************************************* */
/* 命令码说明: 
   (1)、部分命令兼容CH376芯片, 但是输入数据或者输出数据的可能局部不同;
   (2)、一个命令操作顺序包含:
          a、一个命令码(对于串口方式,命令码之前还需要两个同步码);
          b、若干个输入数据(可以是0个);
          c、产生中断通知或者若干个输出数据(可以是0个), 二选一, 有中断通知则一定没有输出数据, 有输出数据则一定不产生中断;
             仅CMD01_WR_REQ_DATA命令例外, 顺序包含: 一个命令码, 一个输出数据, 若干个输入数据   
   (3)、命令码起名规则: CMDxy_NAME
        其中的x和y都是数字, x说明最少输入数据个数(字节数), y说明最少输出数据个数(字节数), y如果是H则说明产生中断通知,
        有些命令能够实现0到多个字节的数据块读写, 数据块本身的字节数未包含在上述x或y之内 */

/* ********************************************************************************************************************* */
/* 主要命令(手册一), 常用, 以下命令在操作结束时不会产生中断通知 */

#define	CMD01_GET_IC_VER			0x01						/* 获取芯片及固件版本 */
/* 输出: 版本号( 位7为0, 位6为1, 位5~位0为版本号 ) */
/*       CH378返回版本号的值为040H即版本号为00H */

#define	CMD31_SET_BAUDRATE			0x02						/* 串口方式: 设置串口通讯波特率(上电或者复位后的默认波特率为9600bps,由SCK/SDI/SDO引脚选择) */
/* 输入: 3个字节波特率系数,低字节在前 */
/* 输出: 操作状态( CMD_RET_SUCCESS或CMD_RET_ABORT, 其它值说明操作未完成 ) */

#define	CMD10_ENTER_SLEEP			0x03						/* 进入睡眠状态 */
/* 输入: 1个字节参数, 如果参数为0x11,则使能芯片进入半睡眠状态,唤醒之后USB功能可以继续使用;
                      如果参数为0x22,则使能芯片进入深度睡眠状态,唤醒之后如果操作的是USB设备,需要全部重新初始化; */

#define	CMD00_RESET_ALL				0x05						/* 执行硬件复位 */

#define	CMD11_CHECK_EXIST			0x06						/* 测试通讯接口和工作状态 */
/* 输入: 任意数据 */
/* 输出: 输入数据的按位取反 */

#define	CMD20_SET_SDO_INT			0x0B						/* SPI接口方式: 设置SPI的SDO引脚的中断方式 */
/* 输入: 数据16H, 中断方式 */
/*           00H=禁止SDO引脚用于中断输出,在SCS片选无效时三态输出禁止, 01H=SDO引脚在SCS片选无效时兼做中断请求输出 */

#define	CMD14_GET_FILE_SIZE			0x0C						/* 主机文件模式: 获取当前文件长度 */
/* 输入: 数据68H */
/* 输出: 当前文件长度(总长度32位,低字节在前) */

#define	CMD11_SET_USB_MODE			0x15						/* 设置USB工作模式 */
/* 输入: 模式代码 */
/*       00H=未启用的设备方式, 01H=已启用的设备方式并且使用外部固件模式, 02H=已启用的设备方式并且使用内置固件模式 */
/*       03H=SD卡主机模式/未启用的主机模式,用于管理和存取SD卡中的文件 */
/*       04H=SD卡主机模式/已启用的主机模式,用于管理和存取SD卡中的文件 */
/*       05H=未启用的主机方式 */
/*       06H=USB主机模式/未启用的主机模式,用于管理和存取USB存储设备中的文件 */
/*       07H=USB主机模式/已启用的主机模式,用于管理和存取USB存储设备中的文件 */
/* 输出: 操作状态( CMD_RET_SUCCESS或CMD_RET_ABORT, 其它值说明操作未完成 ) */

#define	CMD01_GET_STATUS			0x22						/* 获取中断状态并取消中断请求 */
/* 输出: 中断状态 */

#define	CMD40_RD_HOST_OFS_DATA		0x26						/* 主机模式: 读取内部指定缓冲区指定偏移地址数据块(内部指针自动增加) */
/* 输入: 偏移地址( 2个字节,低字节在前,最大为20480 )、读取长度(2个字节,低字节在前,最大为20480) */
/* 输出: 数据流 */
/* 注意: 若输入参数错误,则只发送前面实际缓冲区数据 */

#define	CMD20_RD_HOST_CUR_DATA		0x27						/* 主机模式: 读取内部指定缓冲区当前偏移地址数据块(内部指针自动增加) */
/* 输入: 读取长度(2个字节,低字节在前,最大为20480) */
/* 输出: 数据流 */
/* 注意: 若输入参数错误,则只发送前面实际缓冲区数据 */

#define	CMD00_RD_HOST_REQ_DATA		0x28						/* 主机模式: 读取内部指定缓冲区当前偏移地址请求读取的数据块(内部指针自动增加) */
/* 输出: 读取长度(2个字节,低字节在前,最大为20480) */
/* 输出: 数据流 */
/* 注意: 若输入参数错误,则只发送前面实际缓冲区数据 */

#define	CMD40_WR_HOST_OFS_DATA		0x2D						/* 主机模式: 向内部指定缓冲区指定偏移地址写入数据块(内部指针自动增加) */
/* 输入: 偏移地址( 2个字节,低字节在前,最大为20480 )、输入长度(2个字节,低字节在前,最大为20480)、数据流 */
/* 注意: 若输入参数错误,则写入数据会自动从缓冲区起始地址开始保存 */

#define	CMD20_WR_HOST_CUR_DATA		0x2E						/* 主机模式: 向内部指定缓冲区当前偏移地址写入数据块(内部指针自动增加) */
/* 输入: 输入长度(2个字节,低字节在前,最大为20480)、数据流 */
/* 注意: 若输入参数错误,则写入数据会自动从缓冲区起始地址开始保存 */

#define	CMD10_SET_FILE_NAME			0x2F						/* 主机文件模式: 设置将要操作的文件的文件名、路径名 */
/* 输入: 以0结束的字符串(含结束符0在内长度不超过MAX_FILE_NAME_LEN个字符) */


/* ********************************************************************************************************************* */
/* 主要命令(手册一), 常用, 以下命令总是在操作结束时产生中断通知, 并且总是没有输出数据 */

#define	CMD0H_DISK_CONNECT			0x30						/* 主机文件模式: 检查磁盘是否连接 */
/* 输出中断 */

#define	CMD0H_DISK_MOUNT			0x31						/* 主机文件模式: 初始化磁盘并测试磁盘是否就绪 */
/* 输出中断 */

#define	CMD0H_FILE_OPEN				0x32						/* 主机文件模式: 打开文件或者目录(文件夹),或者枚举文件和目录(文件夹) */
/* 输出中断 */

#define	CMD0H_FILE_ENUM_GO			0x33						/* 主机文件模式: 继续枚举文件和目录(文件夹) */
/* 输出中断 */

#define	CMD0H_FILE_CREATE			0x34						/* 主机文件模式: 新建文件,如果文件已经存在那么先删除 */
/* 输出中断 */

#define	CMD0H_FILE_ERASE			0x35						/* 主机文件模式: 删除文件,如果已经打开则直接删除,否则对于文件会先打开再删除,子目录必须先打开 */
/* 输出中断 */

#define	CMD1H_FILE_CLOSE			0x36						/* 主机文件模式: 关闭当前已经打开的文件或者目录(文件夹) */
/* 输入: 是否允许更新文件长度 */
/*       00H=禁止更新长度, 01H=允许更新长度 */
/* 输出中断 */

#define	CMD1H_DIR_INFO_READ			0x37						/* 主机文件模式: 读取文件的目录信息 */
/* 输入: 指定需要读取的目录信息结构在扇区内的索引号 */
/*       索引号范围为00H~0FH, 索引号0FFH则为当前已经打开的文件 */
/* 输出中断 */

#define	CMD1H_DIR_INFO_SAVE			0x38						/* 主机文件模式: 保存文件的目录信息 */
/* 输入: 指定需要写入的目录信息结构在扇区内的索引号 */
/*       索引号范围为00H~0FH, 索引号0FFH则为当前已经打开的文件 */
/* 输出中断 */

#define	CMD4H_BYTE_LOCATE			0x39						/* 主机文件模式: 以字节为单位移动当前文件指针 */
/* 输入: 偏移字节数(总长度32位,低字节在前) */
/* 输出中断 */

#define	CMD2H_BYTE_READ				0x3A						/* 主机文件模式: 以字节为单位从当前位置读取数据块 */
/* 输入: 请求读取的字节数(总长度16位,低字节在前,最大为20480) */
/* 输出中断 */
/* 注意: 发送该命令后读取的实际数据在内部缓冲区中,需要通过CMD40_RD_HOST_OFS_DATA、CMD20_RD_HOST_CUR_DATA命令获取 */

#define	CMD2H_BYTE_WRITE			0x3C						/* 主机文件模式: 以字节为单位向当前位置写入数据块 */
/* 输入: 请求写入的字节数(总长度16位,低字节在前,最大为20480) */
/* 输出中断 */
/* 注意: 调用该命令前需要通过CMD40_WR_HOST_OFS_DATA或CMD20_WR_HOST_CUR_DATA命令将要写的数据预先填充到内部缓冲区中 */

#define	CMD0H_DISK_CAPACITY			0x3E						/* 主机文件模式: 查询磁盘物理容量 */
/* 输出中断 */

#define	CMD0H_DISK_QUERY			0x3F						/* 主机文件模式: 查询磁盘空间信息 */
/* 输出中断 */

#define	CMD0H_DIR_CREATE			0x40						/* 主机文件模式: 新建目录(文件夹)并打开,如果目录已经存在那么直接打开 */
/* 输出中断 */

#define	CMD4H_SEC_LOCATE			0x4A						/* 主机文件模式: 以扇区为单位移动当前文件指针 */
/* 输入: 偏移扇区数(总长度32位,低字节在前) */
/* 输出中断 */

#define	CMD1H_SEC_READ				0x4B						/* 主机文件模式: 以扇区为单位从当前位置读取数据块 */
/* 输入: 请求读取的扇区数 */
/* 输出中断 */
/* 注意: 受内部缓冲区大小限制,单次扇区读写最大扇区数为40 */

#define	CMD1H_SEC_WRITE				0x4C						/* 主机文件模式: 以扇区为单位在当前位置写入数据块 */
/* 输入: 请求写入的扇区数 */
/* 输出中断 */
/* 注意: 受内部缓冲区大小限制,单次扇区读写最大扇区数为40 */

#define	CMD0H_DISK_BOC_CMD			0x50						/* 主机方式/不支持SD卡: 对USB存储器执行BulkOnly传输协议的命令 */
/* 输出中断 */

#define	CMD5H_DISK_READ				0x54						/* 主机方式: 从USB存储器或SD卡读物理扇区 */
/* 输入: LBA物理扇区地址(总长度32位, 低字节在前), 扇区数(01H~28H) */
/* 输出中断 */
/* 注意: 受内部缓冲区大小限制,单次扇区读写最大扇区数为40 */

#define	CMD5H_DISK_WRITE			0x56						/* 主机方式: 向USB存储器或SD卡写物理扇区 */
/* 输入: LBA物理扇区地址(总长度32位, 低字节在前), 扇区数(01H~FFH) */
/* 输出中断 */
/* 注意: 受内部缓冲区大小限制,单次扇区读写最大扇区数为40 */
/* 注意: 调用该命令前需要通过CMD40_WR_HOST_OFS_DATA或CMD20_WR_HOST_CUR_DATA命令将要写的数据预先填充到内部缓冲区中 */

#define	CMD0H_FILE_QUERY			0x55						/* 主机文件模式: 查询当前文件的信息 */
/* 输出中断 */
/* 注意: 返回数据格式: 4个字节文件长度、2个字节文件日期、2个字节文件时间、1个字节文件属性 */

#define	CMD0H_FILE_MODIFY			0x57						/* 主机文件模式: 查询或者修改当前文件的信息 */
/* 输出中断 */
/* 注意: 输入数据格式: 4个字节文件长度、2个字节文件日期、2个字节文件时间、1个字节文件属性 */


/* ********************************************************************************************************************* */
/* 次要命令(手册二), 非常用, 以下命令在操作结束时不会产生中断通知 */

#define	CMD20_BULK_WR_TEST			0x07						/* 批量写数据测试 */
/* 输入: 2个字节后续数据长度,N个字节后续数据 */

#define	CMD20_BULK_RD_TEST			0x08						/* 批量读数据测试 */
/* 输入: 2个字节后续数据长度 */
/* 输出: N个字节后续数据 */

#define	CMD11_READ_VAR8				0x0A						/* 读取指定的8位CH378系统变量 */
/* 输入: 变量地址 */
/* 输出: 当前地址对应的8位数据 */

#define	CMD11_GET_DEV_RATE			0x0A						/* 主机方式: 获取当前连接的USB设备的数据速率类型 */
/* 输入: 数据07H */
/* 输出: 数据速率类型 */
/*           00表示速度未知; 01表示1.5Mbps低速USB设备; 02表示12Mbps全速USB设备; 03表示480Mbps高速USB设备 */

#define	CMD20_WRITE_VAR8			0x0B						/* 设置指定的8位CH378系统变量 */
/* 输入: 变量地址, 数据 */

#define	CMD14_READ_VAR32			0x0C						/* 读取指定的32位CH378系统变量 */
/* 输入: 变量地址 */
/* 输出: 当前地址对应的32位数据(总长度32位,低字节在前) */

#define	CMD50_WRITE_VAR32			0x0D						/* 设置指定的32位CH378系统变量 */
/* 输入: 变量地址, 数据(总长度32位,低字节在前) */

#define	CMD50_SET_FILE_SIZE			0x0D						/* 主机文件模式: 设置当前文件长度 */
/* 输入: 数据68H, 当前文件长度(总长度32位,低字节在前) */
/* 注意：该命令只是临时改变当前打开的文件的文件长度 */

#define	CMD02_GET_REAL_LEN			0x0E						/* 快速返回上一个命令执行完毕后请求长度所对应的实际长度 */
/* 输出: 4个字节实际数据, 低字节在前 */
/* 注意: 该命令仅仅在使用字节定位、字节读写、扇区定位和扇区读写命令时使用,以加快执行速度  */

#define	CMD01_TEST_CONNECT			0x16						/* 主机方式: 检查USB设备/SD卡连接状态 */
/* 输出: 连接状态( USB_INT_CONNECT或USB_INT_DISCONNECT或USB_INT_USB_READY, 其它值说明操作未完成 ) */

#define	CMD00_DIRTY_BUFFER		   0x25							 /* 主机文件模式: 清除内部的磁盘和文件缓冲区 */

/* ********************************************************************************************************************* */
/* 次要命令(手册二), 非常用, 以下命令总是在操作结束时产生中断通知, 并且总是没有输出数据 */

#define	CMD1H_CLR_STALL				0x41						/* 主机方式/不支持SD卡: 控制传输-清除端点错误 */
/* 输入: 端点号 */
/* 输出中断 */

#define	CMD1H_SET_ADDRESS			0x45						/* 主机方式/不支持SD卡: 控制传输-设置USB地址 */
/* 输入: 地址值 */
/* 输出中断 */

#define	CMD1H_GET_DESCR				0x46						/* 主机方式/不支持SD卡: 控制传输-获取描述符 */
/* 输入: 描述符类型 */
/* 输出中断 */

#define	CMD1H_SET_CONFIG			0x49						/* 主机方式/不支持SD卡: 控制传输-设置USB配置 */
/* 输入: 配置值 */
/* 输出中断 */

#define	CMD0H_AUTO_SETUP			0x4D						/* 主机方式/不支持SD卡: 自动配置USB设备 */
/* 输出中断 */

#define	CMD0H_ISSUE_CTRL_TRAN		0x4E						/* 主机方式/不支持SD卡: 执行控制传输 */
/* 输出中断 */
/* 注意: 发起该命令前,必须通过CMD40_WR_HOST_OFS_DATA命令将8个字节的SETUP包写入到内部缓冲区中,如果需要通过OUT阶段下传数据包,
         则还需要将后续数据包紧接着8个字节的SETUP包写入到内部缓冲区中 */

#define	CMD0H_DISK_INIT				0x51						/* 主机方式/不支持SD卡: 初始化USB存储器 */
/* 输出中断 */

#define	CMD0H_DISK_RESET			0x52						/* 主机方式/不支持SD卡: 控制传输-复位USB存储器 */
/* 输出中断 */

#define	CMD0H_DISK_SIZE				0x53						/* 主机方式/不支持SD卡: 获取USB存储器的容量 */
/* 输出中断 */

#define	CMD0H_DISK_INQUIRY			0x58						/* 主机方式/不支持SD卡: 查询USB存储器特性 */
/* 输出中断 */

#define	CMD0H_DISK_READY			0x59						/* 主机方式/不支持SD卡: 检查USB存储器就绪 */
/* 输出中断 */

#define	CMD0H_DISK_R_SENSE			0x5A						/* 主机方式/不支持SD卡: 检查USB存储器错误 */
/* 输出中断 */

#define	CMD0H_DISK_MAX_LUN			0x5D						/* 主机方式/不支持SD卡: 控制传输-获取USB存储器最大逻辑单元号 */
/* 输出中断 */

#define	CMD10_SET_LONG_FILE_NAME	0x60						/* 主机文件模式: 设置将要操作的长文件的文件名(仅仅是文件名) */
/* 输入长度(2个字节,低字节在前,最大为520)、以两个0结束的字符串(长文件名长度不超过520个字节) */

#define	CMD10_GET_LONG_FILE_NAME	0x61						/* 主机文件模式: 由完整短文件名路径(可以是文件或文件夹)得到相应的长文件名 */
/* 输出中断 */
/* 输出: 读取长度(2个字节,低字节在前,最大为520)、长文件名 */
/* 注意：发起该命令前,必须先通过CMD10_SET_FILE_NAME命令将短文件完整的路径名送入CH378 */

#define	CMD0H_LONG_FILE_CREATE		0x62						/* 主机文件模式: 新建长文件名文件,如果文件已经存在那么先删除 */
/* 输出中断 */
/* 注意：发起该命令前,必须先通过CMD10_SET_LONG_FILE_NAME命令和CMD10_SET_FILE_NAME命令分别将长文件名,以及短文件完整的路径名送入CH378 */

#define	CMD0H_LONG_DIR_CREATE		0x63						/* 主机文件模式: 新建长文件名目录(文件夹)并打开,如果目录已经存在那么直接打开 */
/* 输出中断 */
/* 注意：发起该命令前,必须先通过CMD10_SET_LONG_FILE_NAME命令和CMD10_SET_FILE_NAME命令分别将长文件名,以及短文件完整的路径名送入CH378 */

#define	CMD0H_AUTO_DEMO				0x64						/* 芯片自动演示,在U盘或者SD卡中自动建立名为"芯片演示.TXT"的文件，并写入当前信息 */
/* 输出中断 */

#define	CMD1H_GET_SHORT_FILE_NAME	0x65						/* 主机文件模式: 由部分短文件名路径加上完整的长文件名得到相应的短文件名 */
/* 输出中断 */
/* 输出: 读取长度(2个字节,低字节在前,最大为MAX_PATH_LEN)、短文件名路径 */
/* 注意：发起该命令前,必须先通过CMD10_SET_FILE_NAME命令将部分短文件路径名送入CH378,并通过CMD10_SET_LONG_FILE_NAME
   命令将完整的长文件名输入CH378 */

#define	CMD0H_LONG_FILE_OPEN		0x66						/* 主机文件模式: 通过长文件名打开文件或者目录(文件夹) */
/* 输出中断 */

#define	CMD0H_LONG_FILE_ERASE		0x67						/* 主机文件模式: 通过长文件名删除文件,如果已经打开则直接删除,否则对于文件会先打开再删除,子目录必须先打开 */
/* 输出中断 */
/* ********************************************************************************************************************* */
/* USB设备模式相关命令 */

#define	CMD40_SET_USB_ID			0x11						/* 设置USB厂商VID和产品PID */
/* 输入: 4个字节VID\PID数据,分别为厂商ID低字节, 厂商ID高字节, 产品ID低字节, 产品ID高字节 */
/* 注：该命令必须在CMD11_SET_USB_MODE命令前发送 */

#define	CMD10_SET_USB_SPEED			0x13						/* 设置USB速度 */
/* 输入: 1个字节USB速度值,0x00为全速,0x02为高速,其它值无效 */
/* 注：该命令必须在CMD11_SET_USB_MODE命令前发送 */

#define	CMD01_GET_USB_SPEED			0x14						/* 获取USB速度 */
/* 输出: 1个字节的USB速度值, 0x00为全速,0x02为高速,其它值无效 */

#define	CMD70_INIT_ENDPx			0x17						/* 设置USB端点(端点索引x范围: 1--4) */
/* 输入: 1个字节USB端点索引号: 1--4, 1个字节端点号, 1个字节端点类型, 1个字节端点方向, 2个字节最大包大小(高字节在前),1个字节的中断状态  */
/* 注：该命令必须在CMD11_SET_USB_MODE命令前发送 */

#define	CMD20_SET_INDEXx_IN			0x18						/* 设置USB IN端点索引x(x范围: 0--4)的接收器 */
/* 输入: 1个字节索引号, 1个字节工作方式 */
/*           位7为1则复位该端点的同步位；
             位6为1则设置该端点事务响应方式为：STALL；
			 位5为1则清除该端点之前的STALL特性；
			 其它情况无实际数据上传,则芯片自动应答NAK */

#define	CMD20_SET_INDEXx_OUT		0x19						/* 设置USB OUT端点索引x(x范围: 0--4)的发送器 */
/* 输入: 1个字节索引号, 1个字节工作方式 */
/*           位7为1则复位该端点的同步位；
             位6为1则设置该端点事务响应方式为：STALL；
			 位5为1则清除该端点之前的STALL特性；
			 其它情况接收到下传数据包,则芯片自动应答ACK */

#define	CMD10_UNLOCK_USB			0x23						/* 释放当前USB缓冲区 */
/* 输入: 1个字节USB端点索引号: 0--4 */

#define	CMD1x_RD_USB_DATA			0x29						/* 从指定USB中断的端点缓冲区读取数据块,并释放缓冲区 */
/* 输入: 1个字节USB端点索引号: 0--4 */
/* 输出: 2个字节的数据长度, 数据流 */

#define	CMD30_WR_USB_DATA			0x2A						/* 向控制之外的USB端点的发送缓冲区写入数据块 */
/* 输入: 1个字节USB端点索引号: 1--4; 2个字节的数据长度; N个字节的数据流 */

#define	CMD10_WR_USB_DATA0			0x2B						/* 向USB控制端点的发送缓冲区写入数据块 */
/* 输入: 1个字节的数据长度; N个字节的数据流 */


/* ********************************************************************************************************************* */
/* USB设备模式速度定义 */
#define USB_DEV_SPEED_FS			0x00						/* 当前为全速设备 */
#define USB_DEV_SPEED_HS			0x02						/* 当前为高速设备 */

/* 端点索引定义 */
#define ENDP_INDEX_0				0x00						/* 端点索引0：控制端点 */
#define ENDP_INDEX_1				0x01						/* 端点索引1：自定义端点 */
#define ENDP_INDEX_2				0x02						/* 端点索引2：自定义端点 */
#define ENDP_INDEX_3				0x03						/* 端点索引3：自定义端点 */
#define ENDP_INDEX_4				0x04						/* 端点索引4：自定义端点 */

/* 端点传输方向 */
#define ENDP_DIR_IN					0x00						/* 上传端点 */
#define ENDP_DIR_OUT				0x01						/* 下传端点 */

/* 端点类型 */
#define ENDP_TYPE_RESERVED			0x00						/* 保留 */
#define ENDP_TYPE_ISO				0x01						/* 同步传输 */
#define ENDP_TYPE_BULK				0x02						/* 批量传输 */
#define ENDP_TYPE_INTERRUPT			0x03						/* 中断传输 */


/* ********************************************************************************************************************* */
/* 芯片睡眠模式定义 */
#ifndef	HALF_SLEEP_MODE
#define	HALF_SLEEP_MODE				0x11						/* 半睡眠状态,唤醒之后USB功能可以继续使用  */
#define	FULL_SLEEP_MODE				0x22						/* 深度睡眠状态,唤醒之后如果操作的是USB设备,需要全部重新初始化 */
#endif


/* ********************************************************************************************************************* */
/* 并口方式, 状态端口(读命令端口)的位定义 */
#ifndef	PARA_STATE_INTB
#define	PARA_STATE_INTB				0x80						/* 并口方式状态端口的位7: 中断标志,低有效 */
#define	PARA_STATE_BUSY				0x10						/* 并口方式状态端口的位4: 忙标志,高有效 */
#endif


/* ********************************************************************************************************************* */
/* 串口方式, 操作命令前的引导同步码 */
#ifndef	SER_CMD_TIMEOUT
#define	SER_CMD_TIMEOUT				32							/* 串口命令超时时间, 单位为mS, 同步码之间及同步码与命令码之间的间隔应该尽量短, 超时后的处理方式为丢弃 */
#define	SER_SYNC_CODE1				0x57						/* 启动操作的第1个串口同步码 */
#define	SER_SYNC_CODE2				0xAB						/* 启动操作的第2个串口同步码 */
#define	SER_TIMEOUT_RET				0x03FFFA					/* 串口接收超时返回 */
#endif


/* ********************************************************************************************************************* */
/* 操作状态 */
#ifndef	CMD_RET_SUCCESS
#define	CMD_RET_SUCCESS				0x51						/* 命令操作成功 */
#define	CMD_RET_ABORT				0x5F						/* 命令操作失败 */
#define	CMD_PARAME_ERR				0x5E						/* 命令参数错误 */
#define	CMD_IDENTIFY_ERR			0x5D						/* 无该命令错误 */
#define	CMD_TIMEOUT_ERR				0x5C						/* 命令超时错误 */
#endif


/* ********************************************************************************************************************* */
/* CH378中断状态 */
#ifndef	USB_INT_WAKE_UP
#define	USB_INT_WAKE_UP				0xE0						/* CH378唤醒中断状态 */
#endif


/* ********************************************************************************************************************* */
/* USB中断状态 */

/* 以下状态代码0XH用于USB设备方式 */
/*   内置固件模式下只需要处理: USB_INT_INDEX1_IN, USB_INT_INDEX2_OUT, USB_INT_INDEX3_IN, USB_INT_INDEX4_OUT */
/*   位7-位5为000 */
/*   位4-位3指示当前事务, 00=OUT, 10=IN, 11=SETUP */
/*   位2-位0指示当前端点索引, 000=0号端点, 001=1号端点, 010=2号端点,011=3号端点,100=4号端点, 101=USB挂起, 110=USB总线复位 */
/*   端点索引为0时即为端点0(控制端点), 端点索引为1-4时为其他端点, 端点号、端点类型、端点最大包大小、端点方向可在端点配置时设置 */

#define	USB_INT_EP0_SETUP			0x18						/* USB端点0的SETUP */
#define	USB_INT_EP0_OUT				0x00						/* USB端点0的OUT */
#define	USB_INT_EP0_IN				0x10						/* USB端点0的IN */
#define	USB_INT_INDEX1_OUT			0x01						/* USB 1号端点的OUT */
#define	USB_INT_INDEX1_IN			0x11						/* USB 1号端点的IN */
#define	USB_INT_INDEX2_OUT			0x02						/* USB 2号端点的OUT */
#define	USB_INT_INDEX2_IN			0x12						/* USB 2号端点的IN */
#define	USB_INT_INDEX3_OUT			0x03						/* USB 3号端点的OUT */
#define	USB_INT_INDEX3_IN			0x13						/* USB 3号端点的IN */
#define	USB_INT_INDEX4_OUT			0x04						/* USB 4号端点的OUT */
#define	USB_INT_INDEX4_IN			0x14						/* USB 4号端点的IN */
#define	USB_INT_BUS_SUSP			0x05						/* USB总线挂起 */
#define	USB_INT_BUS_RESET			0x06						/* USB总线复位 */
#define	USB_INT_SET_CONFIG			0x07						/* USB接收到SET_CONFIG命令 */


/* 以下状态代码1XH用于USB主机方式的操作状态代码 */
#ifndef	USB_INT_SUCCESS
#define	USB_INT_SUCCESS				0x14						/* USB事务或者传输操作成功 */
#define	USB_INT_CONNECT				0x15						/* 检测到USB设备连接事件, 可能是新连接或者断开后重新连接 */
#define	USB_INT_DISCONNECT			0x16						/* 检测到USB设备断开事件 */
#define	USB_INT_BUF_OVER			0x17						/* USB控制传输的数据缓冲区溢出或者USB中断传输的数据块错误 */
#define	USB_INT_USB_READY			0x18						/* USB设备已经被初始化(已经分配USB地址) */
#define	USB_INT_DISK_READ			0x1D						/* USB存储器读数据块,请求数据读出 */
#define	USB_INT_DISK_WRITE			0x1E						/* USB存储器写数据块,请求数据写入 */
#define	USB_INT_DISK_ERR			0x1F						/* USB存储器操作失败 */
#endif

/* 以下状态代码2XH用于USB主机方式的错误状态代码 */
#ifndef	USB_INT_RET_ACK
#define	USB_INT_RET_ACK				0x00						/* 错误:对于IN事务返回ACK */
#define	USB_INT_RET_NAK				0x2A						/* 错误:返回NAK */
#define	USB_INT_RET_STALL			0x2E						/* 错误:返回STALL */
#define	USB_INT_RET_DATA0			0x00						/* 错误:返回数据0 */
#define	USB_INT_RET_DATA1			0x00						/* 错误:返回数据1 */
#define	USB_INT_RET_PID				0x00						/* 错误:未定义 */
#define	USB_INT_RET_TOUT			0x20						/* 错误:返回超时 */
#define	USB_INT_RET_TOGX			0x00						/* 错误:对于IN事务返回不匹配的数据切换 */
#define	USB_INT_RET_TOGY			0x00						/* 错误:对于IN事务返回不匹配的数据切换 */
#endif

/* 以下状态代码用于主机文件模式 */
#ifndef	ERR_OPEN_DIR
#define	ERR_OPEN_DIR				0x41						/* 尝试打开的是目录(文件夹) */
#define	ERR_MISS_FILE				0x42						/* 指定的文件不存在 */
#define	ERR_FOUND_NAME				0x43						/* 搜索到与通配符匹配的文件名,文件名及其完整路径在命令缓冲区中 */
#endif
#ifndef	ERR_DISK_DISCON
#define	ERR_DISK_DISCON				0x82						/* 磁盘尚未连接 */
#define	ERR_LARGE_SECTOR			0x84						/* 磁盘的扇区太大,不支持 */
#define	ERR_TYPE_ERROR				0x92						/* 磁盘分区类型不支持 */
#define	ERR_BPB_ERROR				0xA1						/* 磁盘尚未格式化或者参数错误 */
#define	ERR_DISK_FULL				0xB1						/* 磁盘空间不足 */
#define	ERR_FDT_OVER				0xB2						/* 目录(文件夹)内文件太多 */
#define	ERR_FILE_CLOSE				0xB4						/* 文件已经关闭,如果需要使用文件,应该重新打开文件 */
#endif

/* ********************************************************************************************************************* */
/* 文件系统常量定义 */
#ifndef	DEF_SECTOR_SIZE
#define	DEF_SECTOR_SIZE				512							/* U盘或者SD卡的默认物理扇区大小为512字节,该值不能修改 */
#endif

#ifndef	MAX_FILE_NAME_LEN
#define	MAX_FILE_NAME_LEN			( 13+1 )					/* 文件名的最大长度,含结束符,不含路径分隔符 */
#define	MAX_PATH_LEN				( 64+1 )					/* 文件路径的最大长度,含结束符 */
#define	LONG_NAME_BUF_LEN			( 520+4 )					/* 长文件名缓冲区的长度 */
#endif

/* ********************************************************************************************************************* */
/* 文件属性常量定义 */
#ifndef	ATTR_READ_ONLY
#define	ATTR_READ_ONLY				0x01						/* 文件属性-只读 */
#define	ATTR_HIDDEN					0x02						/* 文件属性-隐含 */
#define	ATTR_SYSTEM					0x04						/* 文件属性-系统 */
#define	ATTR_VOLUME_ID				0x08						/* 文件属性-卷标 */
#define	ATTR_DIRECTORY				0x10						/* 文件属性-目录(文件夹) */
#define	ATTR_ARCHIVE				0x20						/* 文件属性-归档 */
#define	ATTR_LONG_NAME				( ATTR_READ_ONLY | ATTR_HIDDEN | ATTR_SYSTEM | ATTR_VOLUME_ID )	/* 长文件名属性 */
#endif

/* ********************************************************************************************************************* */
/* 文件系统错误代码 */
#ifndef	ERR_SUCCESS
#define	ERR_SUCCESS					0x00						/* 操作成功 */
#define	ERR_CE_WRITE_PROTECTED		0x03						/* 磁盘写保护 */
#define	ERR_CE_NOT_READY			0x12						/* 磁盘没有准备好 */
#define	ERR_CE_BAD_SECTOR_READ		0x13						/* 磁盘扇区读失败 */
#define	ERR_CE_WRITE_ERROR			0x15						/* 磁盘扇区写失败 */
#define	ERR_LOGICALDRIVE_NOT_EXIST	0x20						/* 指定的逻辑盘不存在 */
#define	ERR_DIR_NOT_EXIST			0x21						/* 指定的目录路径不存在 */
#define	ERR_FILE_NOT_EXIST			0x22						/* 指定的文件不存在,或者已经被删除,或者指定了错误的文件名 */
#define	ERR_FIND_FILE_NOT_FOUND		0x23						/* 搜索文件失败,没有找到符合通配符的文件名,或者已经没有匹配的文件名 */
#define	ERR_OPEN_DIR_NOT_FOUND		0x24						/* 打开目录失败,没有找到指定的目录名,或者已经没有子目录 */
#define	ERR_MISS_DIR				0x25						/* 指定的目录路径无效或者不存在 */
#define	ERR_MISS_FILE				0x26						/* 指定的文件无效或者不存在 */
#define	ERR_FOUND_NAME				0x27						/* 指定的文件名或者目录名已经存在 */
#define	ERR_DISK_NOT_READY			0x28						/* 磁盘没有准备好,没有连接或者初始化未完成 */
#define	ERR_DISK_SIZE				0x29						/* 磁盘容量太大,不支持 */
#define	ERR_DISK_FULL				0x2A						/* 磁盘空间不足 */
#define	ERR_DIR_FULL				0x2B						/* 目录内文件太多,或者磁盘的根目录内文件太多 */
#define	ERR_FILE_CLOSE				0x2C						/* 文件已经关闭,如果需要读写文件数据,那么必须重新打开文件 */
#define	ERR_FILE_NOT_OPEN			0x2D						/* 文件没有打开,如果需要读写文件数据,那么必须先打开文件 */
#define	ERR_FILE_NOT_WRITE			0x2E						/* 当前文件是只读文件或者写操作被禁止 */
#define	ERR_FILE_EOF				0x2F						/* 文件指针到达文件尾 */
#define	ERR_OPEN_TOO_MANY			0x30						/* 打开的文件太多,没有空闲的文件控制块FCB */
#define	ERR_LONG_BUF_OVER			0x31						/* 长文件名缓冲区溢出 */
#define	ERR_LONG_NAME_ERR			0x32						/* 长文件名格式错误 */
#define	ERR_NAME_EXIST				0x33						/* 指定的文件名或者目录名已经存在 */
#endif

#ifdef __cplusplus
}
#endif

#endif
