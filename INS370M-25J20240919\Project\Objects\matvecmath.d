.\objects\matvecmath.o: ..\INAV\matvecmath.c
.\objects\matvecmath.o: ..\Source\inc\appmain.h
.\objects\matvecmath.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cm4.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\matvecmath.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\matvecmath.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx_libopt.h
.\objects\matvecmath.o: ..\Protocol\RTE_Components.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\matvecmath.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\matvecmath.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\matvecmath.o: ..\Source\inc\systick.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\matvecmath.o: ..\Source\inc\main.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_gpio.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_flash.h
.\objects\matvecmath.o: ..\Source\inc\INS_Data.h
.\objects\matvecmath.o: ..\Library\CMSIS\arm_math.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cm4.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\matvecmath.o: ..\Source\inc\gnss.h
.\objects\matvecmath.o: ..\Common\inc\data_convert.h
.\objects\matvecmath.o: ..\Source\inc\tlhtype.h
.\objects\matvecmath.o: ..\Source\inc\can_data.h
.\objects\matvecmath.o: ..\Source\inc\imu_data.h
.\objects\matvecmath.o: ..\Source\inc\INS_sys.h
.\objects\matvecmath.o: ..\Source\inc\appmain.h
.\objects\matvecmath.o: ..\Source\inc\INS912AlgorithmEntry.h
.\objects\matvecmath.o: ..\Source\inc\deviceconfig.h
.\objects\matvecmath.o: ..\Protocol\frame_analysis.h
.\objects\matvecmath.o: ..\Protocol\protocol.h
.\objects\matvecmath.o: ..\Protocol\config.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\matvecmath.o: ..\Source\inc\board.h
.\objects\matvecmath.o: ..\Protocol\frame_analysis.h
.\objects\matvecmath.o: ..\Protocol\insdef.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_sys.h
.\objects\matvecmath.o: ..\Library\CMSIS\core_cm4.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_rtc.h
.\objects\matvecmath.o: ..\Source\inc\Time_unify.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_can.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_fwdgt.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_fmc.h
.\objects\matvecmath.o: ..\bsp\inc\bsp_exti.h
.\objects\matvecmath.o: ..\bsp\inc\bmp280.h
.\objects\matvecmath.o: ..\bsp\inc\bmp2.h
.\objects\matvecmath.o: ..\bsp\inc\bmp2_defs.h
.\objects\matvecmath.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\matvecmath.o: ..\bsp\inc\common.h
.\objects\matvecmath.o: ..\bsp\inc\logger.h
.\objects\matvecmath.o: ..\bsp\inc\FILE_SYS.h
