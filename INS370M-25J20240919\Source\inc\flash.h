/*!
    \file    flash.h
    \brief   flash header file for GD32F4xx
*/

#ifndef FLASH_H
#define FLASH_H

#include "gd32f4xx.h"
#include "bsp_flash.h"

/* Flash compatibility definitions for GD32F4xx platform */

/* Function declarations */
void flash_init(void);
int flash_write(uint32_t addr, const uint8_t *data, uint32_t len);
int flash_read(uint32_t addr, uint8_t *data, uint32_t len);
int flash_erase(uint32_t addr, uint32_t len);

/* Compatibility macros */
#define FLASH_INIT()                flash_init()

#endif /* FLASH_H */
