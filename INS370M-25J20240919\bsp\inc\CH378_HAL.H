/********************************** (C) COPYRIGHT *******************************
* File Name          : HAL.H
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : CH378芯片 硬件抽象层头文件
*                      提供I/O接口子程序 
*******************************************************************************/



/*******************************************************************************/
/* 头文件包含 */
#include "ch378_config.h"
#include "CH378INC.H"

#ifndef	__HAL_H__
#define __HAL_H__

#include "bsp_sys.h"

#if CHIP_USED == USE_CHIP_GD32
#include "gd32f4xx.h"
#else
#include "usb_lib.h"
#include "stm32f10x_gpio.h"
#endif

/*******************************************************************************/
/* 附加的USB操作状态定义 */
#define	ERR_USB_UNKNOWN				0xFA		/* 未知错误,不应该发生的情况,需检查硬件或者程序错误 */


/*******************************************************************************/
/* 硬件相关宏定义 */
/* 本例中的硬件连接方式如下(实际应用电路可以参照修改下述定义及子程序) */
/* 单片机的引脚					CH378芯片的引脚
	SPI1_NSS-----PB0			SCS
	SPI1_SCK-----PA5			SCK
	SPI1_MISO----PA6			SDO
	SPI1_MOSI----PA7			SDI
	INT#---------PA1			INT#
*/

#define CH378_SPI_Port				GPIOG
#define CH378_SPI_CS_PIN			GPIO_PIN_14
#define CH378_SPI_MOSI_PIN			GPIO_PIN_13
#define CH378_SPI_MISO_PIN			GPIO_PIN_12
#define CH378_SPI_CLK_PIN			GPIO_PIN_11

#define CH378_INT_Port				GPIOA
#define CH378_INT_PIN				GPIO_PIN_4

#define CH378_RST_IO_PORT			GPIOB
#define CH378_RST_IO_PIN			GPIO_PIN_4



#if CHIP_USED == USE_CHIP_GD32

#define CH378_SPI_SCS_LOW()			gpio_bit_reset(CH378_SPI_Port,CH378_SPI_CS_PIN)
#define CH378_SPI_SCS_HIGH()		gpio_bit_set(CH378_SPI_Port,CH378_SPI_CS_PIN)
#define CH378_SPI_SCK_LOW()			gpio_bit_reset(CH378_SPI_Port,CH378_SPI_CLK_PIN)
#define CH378_SPI_SCK_HIGH()		gpio_bit_set(CH378_SPI_Port,CH378_SPI_CLK_PIN)
#define CH378_SPI_SDI_LOW()			gpio_bit_reset(CH378_SPI_Port,CH378_SPI_MOSI_PIN)
#define CH378_SPI_SDI_HIGH()		gpio_bit_set(CH378_SPI_Port,CH378_SPI_MOSI_PIN)

#define CH378_INT_WIRE				1

#if CH378_INT_WIRE
#define	CH378_INT_PIN_WIRE()		gpio_input_bit_get(CH378_INT_Port,CH378_INT_PIN)
#else
#define	CH378_SPI_SDO_PIN()			gpio_input_bit_get(CH378_SPI_Port,CH378_SPI_MISO_PIN)
#endif

#define CH378_RST_High()			gpio_bit_set(CH378_RST_IO_PORT,CH378_RST_IO_PIN)
#define CH378_RST_Low()				gpio_bit_reset(CH378_RST_IO_PORT,CH378_RST_IO_PIN)

#else

#define CH378_SPI_SCS_LOW( )		( GPIOB->BRR  = GPIO_Pin_0 )	/* SPI片选引脚输出低电平 */
#define CH378_SPI_SCS_HIGH( )		( GPIOB->BSRR = GPIO_Pin_0 )	/* SPI片选引脚输出高电平 */
#define CH378_SPI_SCK_LOW( )		( GPIOA->BRR  = GPIO_Pin_5 )	/* SPI时钟引脚输出低电平 */
#define CH378_SPI_SCK_HIGH( )		( GPIOA->BSRR = GPIO_Pin_5 )	/* SPI时钟引脚输出高电平 */
#define CH378_SPI_SDI_LOW( )		( GPIOA->BRR  = GPIO_Pin_7 )	/* SPI数据引脚输出低电平 */
#define CH378_SPI_SDI_HIGH( )		( GPIOA->BSRR = GPIO_Pin_7 )	/* SPI数据引脚输出高电平 */
#define	CH378_SPI_SDO_PIN( )		GPIO_ReadInputDataBit( GPIOA, GPIO_Pin_6 )	/* 获取CH378的SPI数据输出引脚电平 */

#define CH378_INT_WIRE				1
#define	CH378_INT_PIN_WIRE( )		GPIO_ReadInputDataBit( GPIOA, GPIO_Pin_1 )	/* 假定CH378的INT#引脚,如果未连接那么也可以通过查询兼做中断输出的SDO引脚状态实现 */

#endif

#define	xEndCH378Cmd( )				{ CH378_SPI_SCS_HIGH( ); }		/* SPI片选无效,结束CH378命令,仅用于SPI接口方式 */

/*******************************************************************************/
/* 函数外扩 */
void CH378_mDelayuS( UINT8 us );								/* 延时指定微秒时间,根据单片机主频调整,不精确 */
void CH378_mDelaymS( UINT8 ms );								/* 延时指定毫秒时间,根据单片机主频调整,不精确 */
void mStopIfError( UINT8 iError );
void CH378_PORT_INIT( void );									/* CH378通讯接口初始化 */
//void xEndCH378Cmd( void );									/* 结束CH378命令,仅用于SPI接口方式 */
void xWriteCH378Cmd( UINT8 mCmd );								/* 向CH378写命令 */
void xWriteCH378Data( UINT8 mData );							/* 向CH378写数据 */
UINT8 xReadCH378Data( void );									/* 从CH378读数据 */
UINT8 Query378Interrupt( void );								/* 查询CH378中断(INT#引脚为低电平) */
UINT8 mInitCH378Host( void );									/* 初始化CH378 */

#endif
