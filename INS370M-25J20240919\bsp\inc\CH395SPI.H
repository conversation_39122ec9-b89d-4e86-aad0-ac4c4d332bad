
#ifndef __CH395SPI_H_
#define __CH395SPI_H_

#include "CH395INC.H"
#include "bsp_sys.h"

#if CHIP_USED == USE_CHIP_GD32
#include "systick.h"
#else
#include "delay.h"
#endif

#if CHIP_USED == USE_CHIP_GD32

//ʱ��
#define CH395_CONFIG_SPI_CLK()		rcu_periph_clock_enable(RCU_SPI4)
#define CH395_CONFIG_GPIO_CLK()		rcu_periph_clock_enable(RCU_GPIOF);rcu_periph_clock_enable(RCU_GPIOE);rcu_periph_clock_enable(RCU_GPIOB)
//����ʹ�õ�SPI
#define USE_SPI				SPI4
//SPI_CS  --  ����ģ��SCS����
#define CH395_SPI_PORT		GPIOF
#define CH395_CS_PIN		GPIO_PIN_6
//SPI_CLK --   ����ģ��SCK����
#define CH395_CLK_PIN		GPIO_PIN_7
//SPI_MISO --  ����ģ��SDO����
#define CH395_MISO_PIN		GPIO_PIN_8
//SPI_MOSI --  ����ģ��SDI����
#define CH395_MOSI_PIN		GPIO_PIN_9
//RST --  ����ģ��RST����
#define CH395_RST_PORT		GPIOE
#define CH395_RST_PIN		GPIO_PIN_6
//TX --  ����ģ��TX����
#define CH395_TX_PORT		GPIOF
#define CH395_TX_PIN		GPIO_PIN_3
//INT  -- ����ģ��INT���� (��⵽�����ŵ͵�ƽ�ź�֮���ٻ�ȡ����)
#define CH395_INT_PORT		GPIOB
#define CH395_INT_PIN		GPIO_PIN_8
/***********************************************************************/

#define CH395_SPI_CS_LOW()			(gpio_bit_reset(CH395_SPI_PORT,CH395_CS_PIN))     /*CS�����*/
#define CH395_SPI_CS_HIGH()			(gpio_bit_set(CH395_SPI_PORT,CH395_CS_PIN))    /*CS�����*/

#define CH395_RST_PIN_HIGH()		(gpio_bit_set(CH395_RST_PORT,CH395_RST_PIN))   /*RST�����*/
#define CH395_RST_PIN_LOW()			(gpio_bit_reset(CH395_RST_PORT,CH395_RST_PIN))    /*RST�����*/

//#define CH395_TX_PIN_HIGH()		(gpio_bit_set(CH395_TX_PORT,CH395_TX_PIN))    /*TX�����*/
//#define CH395_TX_PIN_LOW()		(gpio_bit_reset(CH395_TX_PORT,CH395_TX_PIN))     /*TX�����*/

#define CH395_INT_PIN_INPUT()		(gpio_input_bit_get(CH395_INT_PORT,CH395_INT_PIN))   /* ��ȡINT��ƽ */ 

#define	xEndCH395Cmd()				{ CH395_SPI_CS_HIGH(); }				/* SPIƬѡ��Ч,����CH395����,������SPI�ӿڷ�ʽ */

#else


/*******************************����GPIO (�����Լ����޸�)****************************************/
//ʱ��
#define CH395_CONFIG_SPI_CLK()   ( RCC_APB1PeriphClockCmd( RCC_APB1Periph_SPI2,ENABLE) )
#define CH395_CONFIG_GPIO_CLK()  ( RCC_APB2PeriphClockCmd( RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB,ENABLE) )
//����ʹ�õ�SPI
#define USE_SPI SPI2
//SPI_CS  --  ����ģ��SCS����
#define CH395_CS_PORT    GPIOB
#define CH395_CS_PIN     GPIO_Pin_12
//SPI_CLK --   ����ģ��SCK����
#define CH395_CLK_PORT   GPIOB
#define CH395_CLK_PIN    GPIO_Pin_13
//SPI_MISO --  ����ģ��SDO����
#define CH395_MISO_PORT  GPIOB
#define CH395_MISO_PIN   GPIO_Pin_14
//SPI_MOSI --  ����ģ��SDI����
#define CH395_MOSI_PORT  GPIOB
#define CH395_MOSI_PIN   GPIO_Pin_15
//RST --  ����ģ��RST����
#define CH395_RST_PORT  GPIOA
#define CH395_RST_PIN   GPIO_Pin_8
//TX --  ����ģ��TX����
#define CH395_TX_PORT  GPIOA
#define CH395_TX_PIN   GPIO_Pin_3
//INT  -- ����ģ��INT���� (��⵽�����ŵ͵�ƽ�ź�֮���ٻ�ȡ����)
#define CH395_INT_PORT  GPIOA
#define CH395_INT_PIN   GPIO_Pin_0
/***********************************************************************/

#define CH395_SPI_CS_LOW()       (CH395_CS_PORT->BRR = CH395_CS_PIN)     /*CS�����*/
#define CH395_SPI_CS_HIGH()      (CH395_CS_PORT->BSRR = CH395_CS_PIN)    /*CS�����*/

#define CH395_RST_PIN_HIGH()     (CH395_RST_PORT->BSRR = CH395_RST_PIN)   /*RST�����*/
#define CH395_RST_PIN_LOW()      (CH395_RST_PORT->BRR = CH395_RST_PIN)    /*RST�����*/

#define CH395_TX_PIN_HIGH()      (CH395_TX_PORT->BSRR = CH395_TX_PIN)    /*TX�����*/
#define CH395_TX_PIN_LOW()       (CH395_TX_PORT->BRR = CH395_TX_PIN)     /*TX�����*/

#define CH395_INT_PIN_INPUT()    (CH395_INT_PORT->IDR & CH395_INT_PIN)   /* ��ȡINT��ƽ */   

#endif

/*******************************************************************************
* Function Name  : Delay_uS
* Description    : ΢�뼶��ʱ����(����׼ȷ)
* Input          : delay---��ʱֵ
* Output         : None
* Return         : None
*******************************************************************************/
void mDelayuS( UINT8 delay );

/*******************************************************************************
* Function Name  : Delay_mS
* Description    : ���뼶��ʱ����(����׼ȷ)
* Input          : delay---��ʱֵ
* Output         : None
* Return         : None
*******************************************************************************/
void mDelaymS( uint32_t delay );


/*******************************************************************************
* Function Name  : CH395_Port_Init
* Description    : CH395�˿ڳ�ʼ��
*                  ����ʹ��SPI��дʱ��,���Խ��г�ʼ��
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395_PORT_INIT( void );


/*******************************************************************************
* Function Name  : xWriteCH395Cmd
* Description    : ��CH395д����
* Input          : mCmd---��Ҫд��CH395��������
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Cmd( UINT8 mCmd );



/*******************************************************************************
* Function Name  : xWriteCH395Data
* Description    : ��CH395д����
* Input          : mData---��Ҫд��CH395������
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Data( UINT8 mData );

/*******************************************************************************
* Function Name  : xReadCH395Data
* Description    : ��CH395������
* Input          : None
* Output         : None
* Return         : ���ض�ȡ������
*******************************************************************************/
UINT8 xReadCH395Data( void );

/*******************************************************************************
* Function Name  : CH395_RST
* Description    : ��λ CH395
* Input          : None
* Output         : None
* Return         : �����ж�״̬
*******************************************************************************/
void CH395_RST( void );


UINT8 Query395Interrupt( void );

#endif

