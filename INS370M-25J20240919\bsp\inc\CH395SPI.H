#ifndef __CH395SPI_H_
#define __CH395SPI_H_

#include "CH395INC.H"
#include "bsp_sys.h"

// 前向声明延时函数，避免包含冲突
#if CHIP_USED == USE_CHIP_GD32
extern void delay_us(uint32_t nus);
extern void delay_ms(uint32_t nms);
#else
#include "delay.h"
#endif

#if CHIP_USED == USE_CHIP_GD32

//时钟
#define CH395_CONFIG_SPI_CLK()		rcu_periph_clock_enable(RCU_SPI4)
#define CH395_CONFIG_GPIO_CLK()		rcu_periph_clock_enable(RCU_GPIOF);rcu_periph_clock_enable(RCU_GPIOE);rcu_periph_clock_enable(RCU_GPIOB)
//定义使用的SPI
#define USE_SPI				SPI4
//SPI_CS  --  网络模块SCS引脚
#define CH395_SPI_PORT		GPIOF
#define CH395_CS_PIN		GPIO_PIN_6
//SPI_CLK --   网络模块SCK引脚
#define CH395_CLK_PIN		GPIO_PIN_7
//SPI_MISO --  网络模块SDO引脚
#define CH395_MISO_PIN		GPIO_PIN_8
//SPI_MOSI --  网络模块SDI引脚
#define CH395_MOSI_PIN		GPIO_PIN_9
//RST --  网络模块RST引脚
#define CH395_RST_PORT		GPIOE
#define CH395_RST_PIN		GPIO_PIN_6
//TX --  网络模块TX引脚
#define CH395_TX_PORT		GPIOF
#define CH395_TX_PIN		GPIO_PIN_3
//INT  -- 网络模块INT引脚 (检测到中断信号低电平信号之后再获取数据)
#define CH395_INT_PORT		GPIOB
#define CH395_INT_PIN		GPIO_PIN_8
/***********************************************************************/

#define CH395_SPI_CS_LOW()			(gpio_bit_reset(CH395_SPI_PORT,CH395_CS_PIN))     /*CS拉低*/
#define CH395_SPI_CS_HIGH()			(gpio_bit_set(CH395_SPI_PORT,CH395_CS_PIN))    /*CS拉高*/

#define CH395_RST_PIN_HIGH()		(gpio_bit_set(CH395_RST_PORT,CH395_RST_PIN))   /*RST拉高*/
#define CH395_RST_PIN_LOW()			(gpio_bit_reset(CH395_RST_PORT,CH395_RST_PIN))    /*RST拉低*/

//#define CH395_TX_PIN_HIGH()		(gpio_bit_set(CH395_TX_PORT,CH395_TX_PIN))    /*TX拉高*/
//#define CH395_TX_PIN_LOW()		(gpio_bit_reset(CH395_TX_PORT,CH395_TX_PIN))     /*TX拉低*/

#define CH395_INT_PIN_INPUT()		(gpio_input_bit_get(CH395_INT_PORT,CH395_INT_PIN))   /* 获取INT电平 */ 

#define	xEndCH395Cmd()				{ CH395_SPI_CS_HIGH(); }				/* SPI片选无效,结束CH395命令,仅用于SPI接口方式 */

#else


/*******************************定义GPIO (请根据自己需要修改)****************************************/
//时钟
#define CH395_CONFIG_SPI_CLK()   ( RCC_APB1PeriphClockCmd( RCC_APB1Periph_SPI2,ENABLE) )
#define CH395_CONFIG_GPIO_CLK()  ( RCC_APB2PeriphClockCmd( RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB,ENABLE) )
//定义使用的SPI
#define USE_SPI SPI2
//SPI_CS  --  网络模块SCS引脚
#define CH395_CS_PORT    GPIOB
#define CH395_CS_PIN     GPIO_Pin_12
//SPI_CLK --   网络模块SCK引脚
#define CH395_CLK_PORT   GPIOB
#define CH395_CLK_PIN    GPIO_Pin_13
//SPI_MISO --  网络模块SDO引脚
#define CH395_MISO_PORT  GPIOB
#define CH395_MISO_PIN   GPIO_Pin_14
//SPI_MOSI --  网络模块SDI引脚
#define CH395_MOSI_PORT  GPIOB
#define CH395_MOSI_PIN   GPIO_Pin_15
//RST --  网络模块RST引脚
#define CH395_RST_PORT  GPIOA
#define CH395_RST_PIN   GPIO_Pin_8
//TX --  网络模块TX引脚
#define CH395_TX_PORT  GPIOA
#define CH395_TX_PIN   GPIO_Pin_3
//INT  -- 网络模块INT引脚 (检测到中断信号低电平信号之后再获取数据)
#define CH395_INT_PORT  GPIOA
#define CH395_INT_PIN   GPIO_Pin_0
/***********************************************************************/

#define CH395_SPI_CS_LOW()       (CH395_CS_PORT->BRR = CH395_CS_PIN)     /*CS拉低*/
#define CH395_SPI_CS_HIGH()      (CH395_CS_PORT->BSRR = CH395_CS_PIN)    /*CS拉高*/

#define CH395_RST_PIN_HIGH()     (CH395_RST_PORT->BSRR = CH395_RST_PIN)   /*RST拉高*/
#define CH395_RST_PIN_LOW()      (CH395_RST_PORT->BRR = CH395_RST_PIN)    /*RST拉低*/

#define CH395_TX_PIN_HIGH()      (CH395_TX_PORT->BSRR = CH395_TX_PIN)    /*TX拉高*/
#define CH395_TX_PIN_LOW()       (CH395_TX_PORT->BRR = CH395_TX_PIN)     /*TX拉低*/

#define CH395_INT_PIN_INPUT()    (CH395_INT_PORT->IDR & CH395_INT_PIN)   /* 获取INT电平 */   

#endif

/*******************************************************************************
* Function Name  : Delay_uS
* Description    : 微秒级延时函数(不够准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelayuS( UINT8 delay );

/*******************************************************************************
* Function Name  : Delay_mS
* Description    : 毫秒级延时函数(不够准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void mDelaymS( UINT8 delay );


/*******************************************************************************
* Function Name  : CH395_Port_Init
* Description    : CH395端口初始化
*                  如果使用SPI读写时钟,可以进行初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395_PORT_INIT( void );


/*******************************************************************************
* Function Name  : xWriteCH395Cmd
* Description    : 向CH395写命令
* Input          : mCmd---需要写入CH395的命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Cmd( UINT8 mCmd );



/*******************************************************************************
* Function Name  : xWriteCH395Data
* Description    : 向CH395写数据
* Input          : mData---需要写入CH395的数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH395Data( UINT8 mData );

/*******************************************************************************
* Function Name  : xReadCH395Data
* Description    : 从CH395读数据
* Input          : None
* Output         : None
* Return         : 返回读取的数据
*******************************************************************************/
UINT8 xReadCH395Data( void );

/*******************************************************************************
* Function Name  : CH395_RST
* Description    : 复位 CH395
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
void CH395_RST( void );


UINT8 Query395Interrupt( void );

#endif

