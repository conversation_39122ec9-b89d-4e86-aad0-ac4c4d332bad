/********************************** (C) COPYRIGHT *******************************
* File Name          : CH395CMD.C
* Author             : WCH
* Version            : V1.1
* Date               : 2014/8/1
* Description        : CH395芯片命令接口文件
*                      
*******************************************************************************/

/* 头文件包含*/
#include "CH395INC.H"
#include "ch395cmd.h"
#include "CH395SPI.H"

/********************************************************************************
* Function Name  : CH395CMDReset
* Description    : 复位CH395芯片
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDReset(void)
{
	xWriteCH395Cmd(CMD00_RESET_ALL);
	xEndCH395Cmd();
}

/*******************************************************************************
* Function Name  : CH395CMDSleep
* Description    : 使CH395进入睡眠状态
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSleep(void)
{
	xWriteCH395Cmd(CMD00_ENTER_SLEEP);
	xEndCH395Cmd();
}

/********************************************************************************
* Function Name  : CH395CMDSleep
* Description    : 获取芯片以及固件版本号（1字节），高两位表示芯片版本，
                   低六位表示固件版本
* Input          : None
* Output         : None
* Return         : 1字节芯片及固件版本号
*******************************************************************************/
uint8_t CH395CMDGetVer(void)
{
	uint8_t i;
	xWriteCH395Cmd(CMD01_GET_IC_VER);
	CH395_SPI_CS_LOW();
	i = xReadCH395Data();
	xEndCH395Cmd();
	return i;
}

/********************************************************************************
* Function Name  : CH395CMDCheckExist
* Description    : 测试通讯接口和硬件以及接口通讯
* Input          : testdata 1字节测试数据
* Output         : None
* Return         : 硬件OK，返回 testdata按位取反
*******************************************************************************/
uint8_t CH395CMDCheckExist(uint8_t testdata)
{
	uint8_t i;

	xWriteCH395Cmd(CMD11_CHECK_EXIST);
	xWriteCH395Data(testdata);
	i = xReadCH395Data();
	xEndCH395Cmd();
	return i;
}

/********************************************************************************
* Function Name  : CH395CMDSetPHY
* Description    : 设置PHY，主要设置CH395 PHY为100/10M 以及全双工或者半双工，CH395默
                    认为自动协商。
* Input          : phystat 参考PHY 参数定义/状态
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetPHY(uint8_t phystat)
{
	xWriteCH395Cmd(CMD10_SET_PHY);
	xWriteCH395Data(phystat);
	xEndCH395Cmd();
}

/*******************************************************************************
* Function Name  : CH395CMDGetPHYStatus
* Description    : 获取PHY的状态
* Input          : None
* Output         : None
* Return         : 当前CH395PHY状态，参考PHY参数/状态定义
*******************************************************************************/
uint8_t CH395CMDGetPHYStatus(void)
{
	uint8_t i;

	xWriteCH395Cmd(CMD01_GET_PHY_STATUS);
	CH395_SPI_CS_LOW();
	i = xReadCH395Data();
	i = xReadCH395Data();
	xEndCH395Cmd();
	return i;
}

/*******************************************************************************
* Function Name  : CH395CMDGetGlobIntStatus
* Description    : 获取全局中断状态，收到数据后CH395自动取消中断，0x43以上版本使用
* Input          : None
* Output         : None
* Return         : 返回当前的全局中断状态
*******************************************************************************/
uint8_t CH395CMDGetGlobIntStatus(void)
{
	uint8_t init_status;

	xWriteCH395Cmd(CMD01_GET_GLOB_INT_STATUS);
	CH395_SPI_CS_LOW();
	init_status = xReadCH395Data();
	xEndCH395Cmd();
	return  init_status;
}

/********************************************************************************
* Function Name  : CH395CMDInitCH395
* Description    : 初始化CH395芯片。
* Input          : None
* Output         : None
* Return         : 返回执行结果
*******************************************************************************/
uint8_t CH395CMDInitCH395(void)
{
	uint8_t i = 0;
	uint8_t s = 0;

	xWriteCH395Cmd(CMD0W_INIT_CH395);
	xEndCH395Cmd();
	delay_ms(800);
	while(1)
	{
		mDelaymS(20);								/* 延时查询，建议2MS以上*/
		s = CH395GetCmdStatus();					/* 不建议过于频繁查询*/
		if(s !=CH395_ERR_BUSY)break;				/* 如果CH395芯片返回忙状态*/
		if(i++ > 100)return CH395_ERR_UNKNOW;		/* 超时退出,一般需要500MS以上执行完毕 */
	}
	return s;
}

/********************************************************************************
* Function Name  : CH395CMDSetUartBaudRate
* Description    : 设置CH395串口波特率，仅在串口模式下有效
* Input          : baudrate 串口波特率
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetUartBaudRate(uint32_t baudrate)
{
	xWriteCH395Cmd(CMD31_SET_BAUDRATE);
	xWriteCH395Data((uint8_t)baudrate);
	xWriteCH395Data((uint8_t)((uint16_t)baudrate >> 8));
	xWriteCH395Data((uint8_t)(baudrate >> 16));
	xEndCH395Cmd();
}

/*******************************************************************************
* Function Name  : CH395GetCmdStatus
* Description    : 获取命令执行状态，某些命令需要等待命令执行结果
* Input          : None
* Output         : None
* Return         : 返回上一条命令执行状态
*******************************************************************************/
uint8_t CH395GetCmdStatus(void)
{
	uint8_t i;

	xWriteCH395Cmd(CMD01_GET_CMD_STATUS);
	CH395_SPI_CS_HIGH();
	CH395_SPI_CS_LOW();
	i = xReadCH395Data();
	i = xReadCH395Data();
	xEndCH395Cmd();
	return i;
}

/********************************************************************************
* Function Name  : CH395CMDSetIPAddr
* Description    : 设置CH395的IP地址
* Input          : ipaddr 指IP地址
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetIPAddr(uint8_t *ipaddr)
{
	uint8_t i;

	xWriteCH395Cmd(CMD40_SET_IP_ADDR);
	for(i = 0; i < 4;i++)xWriteCH395Data(*ipaddr++);
	xEndCH395Cmd();
}

/********************************************************************************
* Function Name  : CH395CMDSetGWIPAddr
* Description    : 设置CH395网关IP地址
* Input          : ipaddr 指网关IP地址
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetGWIPAddr(uint8_t *gwipaddr)
{
	uint8_t i;

	xWriteCH395Cmd(CMD40_SET_GWIP_ADDR);
	for(i = 0; i < 4;i++)xWriteCH395Data(*gwipaddr++);
	xEndCH395Cmd();
}

/********************************************************************************
* Function Name  : CH395CMDSetMASKAddr
* Description    : 设置CH395子网掩码，默认为*************
* Input          : maskaddr 指子网掩码地址
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetMASKAddr(uint8_t *maskaddr)
{
	uint8_t i;

	xWriteCH395Cmd(CMD40_SET_MASK_ADDR);
	for(i = 0; i < 4;i++)xWriteCH395Data(*maskaddr++);
	xEndCH395Cmd();
}

/********************************************************************************
* Function Name  : CH395CMDSetMACAddr
* Description    : 设置CH395的MAC地址。
* Input          : amcaddr MAC地址指针
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDSetMACAddr(uint8_t *amcaddr)
{
	uint8_t i;

	xWriteCH395Cmd(CMD60_SET_MAC_ADDR);
	for(i = 0; i < 6;i++)xWriteCH395Data(*amcaddr++);
	xEndCH395Cmd();
	mDelaymS(100); 
}

/********************************************************************************
* Function Name  : CH395CMDGetMACAddr
* Description    : 获取CH395的MAC地址。
* Input          : amcaddr MAC地址指针
* Output         : None
* Return         : None
*******************************************************************************/
void CH395CMDGetMACAddr(uint8_t *amcaddr)
{
	uint8_t i;

	xWriteCH395Cmd(CMD06_GET_MAC_ADDR);
	CH395_SPI_CS_LOW();
	for(i = 0; i < 6;i++)*amcaddr++ = xReadCH395Data();
	xEndCH395Cmd();
 }
