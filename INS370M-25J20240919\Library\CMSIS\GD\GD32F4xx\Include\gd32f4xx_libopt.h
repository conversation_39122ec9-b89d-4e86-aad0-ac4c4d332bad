/*!
    \file    gd32f4xx_libopt.h
    \brief   library optional for gd32f4xx
*/

#ifndef GD32F4XX_LIBOPT_H
#define GD32F4XX_LIBOPT_H

/* Comment the line below to disable peripheral header file inclusion */
#include "RTE_Components.h"

/* gd32f4xx_rcu.h header file */
#include "gd32f4xx_rcu.h"

/* gd32f4xx_adc.h header file */
#include "gd32f4xx_adc.h"

/* gd32f4xx_can.h header file */
#include "gd32f4xx_can.h"

/* gd32f4xx_crc.h header file */
#include "gd32f4xx_crc.h"

/* gd32f4xx_ctc.h header file */
#include "gd32f4xx_ctc.h"

/* gd32f4xx_dac.h header file */
#include "gd32f4xx_dac.h"

/* gd32f4xx_dbg.h header file */
#include "gd32f4xx_dbg.h"

/* gd32f4xx_dci.h header file */
#include "gd32f4xx_dci.h"

/* gd32f4xx_dma.h header file */
#include "gd32f4xx_dma.h"

/* gd32f4xx_exti.h header file */
#include "gd32f4xx_exti.h"

/* gd32f4xx_fmc.h header file */
#include "gd32f4xx_fmc.h"

/* gd32f4xx_fwdgt.h header file */
#include "gd32f4xx_fwdgt.h"

/* gd32f4xx_gpio.h header file */
#include "gd32f4xx_gpio.h"

/* gd32f4xx_syscfg.h header file */
#include "gd32f4xx_syscfg.h"

/* gd32f4xx_i2c.h header file */
#include "gd32f4xx_i2c.h"

/* gd32f4xx_iref.h header file */
#include "gd32f4xx_iref.h"

/* gd32f4xx_pmu.h header file */
#include "gd32f4xx_pmu.h"

/* gd32f4xx_rtc.h header file */
#include "gd32f4xx_rtc.h"

/* gd32f4xx_sdio.h header file */
#include "gd32f4xx_sdio.h"

/* gd32f4xx_spi.h header file */
#include "gd32f4xx_spi.h"

/* gd32f4xx_timer.h header file */
#include "gd32f4xx_timer.h"

/* gd32f4xx_trng.h header file */
#include "gd32f4xx_trng.h"

/* gd32f4xx_usart.h header file */
#include "gd32f4xx_usart.h"

/* gd32f4xx_wwdgt.h header file */
#include "gd32f4xx_wwdgt.h"

/* gd32f4xx_misc.h header file */
#include "gd32f4xx_misc.h"

/* gd32f4xx_exmc.h header file */
#include "gd32f4xx_exmc.h"

/* gd32f4xx_enet.h header file */
#include "gd32f4xx_enet.h"

/* gd32f4xx_ipa.h header file */
#include "gd32f4xx_ipa.h"

/* gd32f4xx_tli.h header file */
#include "gd32f4xx_tli.h"

#endif /* GD32F4XX_LIBOPT_H */
