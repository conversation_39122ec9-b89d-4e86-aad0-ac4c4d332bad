#ifndef ____LOGGER_H____
#define ____LOGGER_H____

#include "CH378_HAL.h"
#include "FILE_SYS.h"
#include "stdio.h"
#include "stdlib.h"
#include "string.h"

typedef struct log_buf_t
{
	char* pbuf;
	uint32_t nSize;
}LogBufTypeDef;

typedef enum log_type_t
{
	Log_Type_0 = 0,
	Log_Type_1 = 1,
	Log_Type_2 = 2,
	Log_Type_3 = 3,
	Log_Type_4 = 4,
	Log_Type_5 = 5,
	Log_Type_6 = 6,
	Log_Type_7 = 7,
	Log_Type_MAX,
}LogTypeEnumDef;

#define LOG_BUF_SIZE	512

#define NATURE_CONSTANT_e		2.718281828459045 			//自然常数e

extern char g_charBuf[];

/****************************************************************************
* 函数名	:	generateCSVLogFileName
* 参数		:	char* fileName, 		文件名指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	根据内核文件名合成成一个文件名
****************************************************************************/
int generateCSVLogFileName(char* fileName);
/****************************************************************************
* 函数名	:	writeCSVLog
* 参数		:	unsigned char* filename,	文件名指针
* 参数		:	LogBufTypeDef* pLog		需要写入数据结构体指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	将数据写入到文件中
****************************************************************************/
int writeCSVLog(unsigned char* filename, LogBufTypeDef* pLog);
/****************************************************************************
* 函数名	:	synthesisLogBuf
* 参数		:	uint8_t* pBuf, 			接收到数据的指针
* 参数		:	uint32_t nSize, 		接收到数据的长度
* 参数		:	uint16_t type, 			需要发送的数据类型
* 参数		:	LogBufTypeDef* pLog		需要发送的数据结构体指针
* 返回值	:	int,  1 fail   0 success
****************************************************************************
* 功能说明	:	将接收到数据转换成结构体, 该结构体里保存数据的字符串形式和发送缓冲区大小
****************************************************************************/
int synthesisLogBuf(uint8_t* pBuf, uint32_t nSize, uint16_t type, LogBufTypeDef* pLog);


int parseFPGABuff(float* vAngle, float* vGyro, float* vAcc, float* lat, float* lon,float* height, \
					float* speed_nor,float* speed_est,float* speed_earth, uint8_t* state, float* time, uint32_t* week,\
						float*LonStd, float* LatStd, float* AltitudeStd, uint8_t* StarNum, \
					float* vn_std,float* ve_std, float* vd_std,float* rollstd,float* pitchstd,float* yawstd,float* temper,\
						uint16_t* wheelState,uint8_t* GPSSpeedState,uint16_t* ResolveState);

int writeCSVFileHead(void);
					
#endif // ____LOGGER_H____
