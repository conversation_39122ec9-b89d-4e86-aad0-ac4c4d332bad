/******************** (C) COPYRIGHT 2011 WCH ***********************************
* File Name          : HAL.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : 硬件相关部分程序
*******************************************************************************/

/* 条件编译：只有在使用软件SPI时才编译此文件 */
#include "ch378_config.h"
#ifndef USE_CH378_HARDWARE_SPI

/******************************************************************************/
/* 头文件包含 */
#include "stdio.h"
#include "bsp_sys.h"

#include "CH378INC.H"										 /* 常变量定义头文件 */
#include "CH378_HAL.H"										 /* 硬件引脚宏定义头文件 */

#if CHIP_USED == USE_CHIP_GD32 
#include "gd32f4xx.h"
#include "systick.h"
#else
#include "stm32f10x.h"
#endif

/*******************************************************************************
* Function Name  : Delay_uS
* Description    : 微秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_mDelayuS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_us((uint32_t)delay);
#else
	UINT8 i, j;

	for( i = delay; i != 0; i -- ) 
	{
		for( j = 8; j != 0; j -- )
		{
		}		
	}
#endif
}

/*******************************************************************************
* Function Name  : Delay_mS
* Description    : 毫秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_mDelaymS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_ms((uint32_t)delay);
#else
	UINT8 i, j;

	for( i = delay; i != 0; i -- ) 
	{
		for( j = 200; j != 0; j -- )
		{
			CH378_mDelayuS( 5 );
		}		
	}
#endif
}

/*******************************************************************************
* Function Name  : mStopIfError
* Description    : 检查操作状态
*                  如果错误,则显示错误代码并停机,应该替换为实际的处理措施,
*                  例如显示错误信息,等待用户确认后重试等
* Input          : iError---待检测的状态值
* Output         : None
* Return         : None
*******************************************************************************/
void mStopIfError( UINT8 iError )
{
	if( iError == ERR_SUCCESS ) 
	{
		return;  												 /* 操作成功 */
	}
	printf( "Error: %02X\n", (UINT16)iError );  			 	 /* 显示错误 */
	while( 1 ) 
	{
	}
}

/*******************************************************************************
* Function Name  : CH378_PORT_INIT
* Description    : CH378通讯接口初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_PORT_INIT( void )
{
#if CHIP_USED == USE_CHIP_GD32
	/* 配置GPIO时钟 */
	rcu_periph_clock_enable(RCU_GPIOG);
	rcu_periph_clock_enable(RCU_GPIOA);
	rcu_periph_clock_enable(RCU_GPIOB);
	
	/* 配置SPI引脚 */
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, 
		CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, 
		CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_SPI_MISO_PIN);
	
	/* 配置中断引脚 */
	gpio_mode_set(CH378_INT_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_INT_PIN);
	
	/* 配置复位引脚 */
	gpio_mode_set(CH378_RST_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, CH378_RST_IO_PIN);
	gpio_output_options_set(CH378_RST_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_RST_IO_PIN);
	
	/* 初始化引脚状态 */
	CH378_SPI_SCS_HIGH();
	CH378_SPI_SCK_LOW();
	CH378_RST_High();
#else
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 配置GPIO时钟 */
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_GPIOG, ENABLE);
	
	/* 配置SPI引脚 */
	GPIO_InitStructure.GPIO_Pin = CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(CH378_SPI_Port, &GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Pin = CH378_SPI_MISO_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_Init(CH378_SPI_Port, &GPIO_InitStructure);
	
	/* 配置中断引脚 */
	GPIO_InitStructure.GPIO_Pin = CH378_INT_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_Init(CH378_INT_Port, &GPIO_InitStructure);
	
	/* 配置复位引脚 */
	GPIO_InitStructure.GPIO_Pin = CH378_RST_IO_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(CH378_RST_IO_PORT, &GPIO_InitStructure);
	
	/* 初始化引脚状态 */
	CH378_SPI_SCS_HIGH();
	CH378_SPI_SCK_LOW();
	CH378_RST_High();
#endif
}

/*******************************************************************************
* Function Name  : xWriteCH378Cmd
* Description    : 向CH378写命令
* Input          : mCmd---命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Cmd( UINT8 mCmd )
{
	UINT8 i;
	CH378_SPI_SCS_LOW();
	CH378_mDelayuS(1);
	for( i = 0; i != 8; i ++ ) {
		if( mCmd & 0x80 ) CH378_SPI_SDI_HIGH();
		else CH378_SPI_SDI_LOW();
		CH378_SPI_SCK_HIGH();
		mCmd <<= 1;
		CH378_SPI_SCK_LOW();
	}
	CH378_mDelayuS(1);
	CH378_SPI_SCS_HIGH();
}

/*******************************************************************************
* Function Name  : xWriteCH378Data
* Description    : 向CH378写数据
* Input          : mData---数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Data( UINT8 mData )
{
	UINT8 i;
	CH378_SPI_SCS_LOW();
	CH378_mDelayuS(1);
	for( i = 0; i != 8; i ++ ) {
		if( mData & 0x80 ) CH378_SPI_SDI_HIGH();
		else CH378_SPI_SDI_LOW();
		CH378_SPI_SCK_HIGH();
		mData <<= 1;
		CH378_SPI_SCK_LOW();
	}
	CH378_mDelayuS(1);
}

/*******************************************************************************
* Function Name  : xReadCH378Data
* Description    : 从CH378读数据
* Input          : None
* Output         : None
* Return         : 读取的数据
*******************************************************************************/
UINT8 xReadCH378Data( void )
{
	UINT8 i, mData;
	mData = 0;
	for( i = 0; i != 8; i ++ ) {
		CH378_SPI_SCK_HIGH();
		mData <<= 1;
#if CH378_INT_WIRE
		if( CH378_INT_PIN_WIRE() ) mData++;
#else
		if( CH378_SPI_SDO_PIN() ) mData++;
#endif
		CH378_SPI_SCK_LOW();
	}
	return( mData );
}

/*******************************************************************************
* Function Name  : Query378Interrupt
* Description    : 查询CH378中断(INT#引脚为低电平)
* Input          : None
* Output         : None
* Return         : 中断状态
*******************************************************************************/
UINT8 Query378Interrupt( void )
{
#if CH378_INT_WIRE
	return( CH378_INT_PIN_WIRE() ? FALSE : TRUE );
#else
	return( CH378_SPI_SDO_PIN() ? FALSE : TRUE );
#endif
}

/*******************************************************************************
* Function Name  : mInitCH378Host
* Description    : 初始化CH378
* Input          : None
* Output         : None
* Return         : 错误码
*******************************************************************************/
UINT8 mInitCH378Host( void )
{
	UINT8 res;
	
	CH378_PORT_INIT();
	xWriteCH378Cmd( CMD11_CHECK_EXIST );
	xWriteCH378Data( 0x65 );
	res = xReadCH378Data();
	xEndCH378Cmd();
	if( res != 0x9A ) return( ERR_USB_UNKNOWN );
	
	xWriteCH378Cmd( CMD11_SET_USB_MODE );
	xWriteCH378Data( 0x06 );
	xEndCH378Cmd();
	CH378_mDelaymS( 10 );

	return( ERR_SUCCESS );
}

#endif /* USE_CH378_HARDWARE_SPI */
