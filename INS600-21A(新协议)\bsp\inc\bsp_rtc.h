#ifndef ____BSP_RTC_H____
#define ____BSP_RTC_H____

#include "gd32f4xx.h"
#include <stdio.h>
#include <string.h>
#include "Time_unify.h"

typedef struct rtc_para_t{
	uint8_t year;					/*!< RTC year value: 0x0 - 0x99(BCD format) */
	uint8_t month;					/*!< RTC month value */
	uint8_t date;					/*!< RTC date value: 0x1 - 0x31(BCD format) */
	uint8_t day_of_week;			/*!< RTC weekday value */
	uint8_t hour;					/*!< RTC hour value */
	uint8_t minute;					/*!< RTC minute value: 0x0 - 0x59(BCD format) */
	uint8_t second;					/*!< RTC second value: 0x0 - 0x59(BCD format) */
}RTCParaTypeDef;

typedef struct rtc_t{
	uint32_t rtc;
	rcu_periph_enum rtcClk;
	rtc_parameter_struct rtcSetting;
	rtc_timestamp_struct timestamp;
	
}RTCTypeDef;

typedef struct timestamp_t{
	char year[5];
	char month[4];
	char day[3];
	char hour[3];
	char minute[3];
	char second[3];
	char miniSecond[4];
}TimeStampTypeDef;

extern RTCTypeDef* pRTC;
extern TimeStampTypeDef* pTimestamep;

#define RTC_CLOCK_SOURCE_LXTAL
#define BKP_VALUE    0x32F1

//void rtc_show_time(RTCTypeDef* prtc);
void rtc_show_timestamp(RTCTypeDef* prtc,TimeStampTypeDef* pTime);
void rtc_setup(rtc_parameter_struct* pRTCPara);
void rtc_pre_config(RTCTypeDef* prtc);
void bsp_rtc_init(RTCTypeDef* prtc);
void timeSync(int week, double sec);
void getRTCWeekSecond(int* pWeek, double* pSec);

#endif //____BSP_RTC_H____
