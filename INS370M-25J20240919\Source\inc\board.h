/*!
    \file    board.h
    \brief   board configuration header file for GD32F4xx
*/

#ifndef BOARD_H
#define BOARD_H

#include "gd32f4xx.h"
#include "systick.h"

/* Board specific definitions */
#define BOARD_GD32F4XX

/* System clock configuration */
#define HXTAL_VALUE    ((uint32_t)25000000) /*!< value of the external oscillator in Hz */
#define __SYSTEM_CLOCK_200M_PLL_25M_HXTAL   (uint32_t)(200000000)

/* LED definitions */
#define LED_PIN                     GPIO_PIN_10
#define LED_GPIO_PORT              GPIOA
#define LED_GPIO_CLK               RCU_GPIOA

/* USART definitions */
#define EVAL_COM0                   USART0
#define EVAL_COM0_CLK               RCU_USART0
#define EVAL_COM0_TX_PIN            GPIO_PIN_9
#define EVAL_COM0_RX_PIN            GPIO_PIN_10
#define EVAL_COM0_GPIO_PORT         GPIOA
#define EVAL_COM0_GPIO_CLK          RCU_GPIOA
#define EVAL_COM0_AF                GPIO_AF_7

/* Function declarations */
void SystemInit(void);
void board_init(void);
// delay_ms函数已在systick.h中声明，此处删除避免重复声明

/* Compatibility macros for HPM platform */
#define mDelaymS(ms)                delay_ms(ms)
#define delay_1ms(ms)               delay_ms(ms)

/* Memory and register access macros */
#define REG32(addr)                 (*(volatile uint32_t *)(uint32_t)(addr))
#define REG16(addr)                 (*(volatile uint16_t *)(uint32_t)(addr))
#define REG8(addr)                  (*(volatile uint8_t *)(uint32_t)(addr))

/* Interrupt priority definitions */
#define NVIC_PRIORITY_GROUP_4       NVIC_PRIGROUP_PRE4_SUB0

/* Board specific GPIO definitions */
#define GPIO_PORT_A                 GPIOA
#define GPIO_PORT_B                 GPIOB
#define GPIO_PORT_C                 GPIOC
#define GPIO_PORT_D                 GPIOD
#define GPIO_PORT_E                 GPIOE

/* SPI definitions */
#define BOARD_SPI0                  SPI0
#define BOARD_SPI1                  SPI1
#define BOARD_SPI2                  SPI2

/* Timer definitions */
#define BOARD_TIMER0                TIMER0
#define BOARD_TIMER1                TIMER1
#define BOARD_TIMER2                TIMER2

/* ADC definitions */
#define BOARD_ADC0                  ADC0
#define BOARD_ADC1                  ADC1
#define BOARD_ADC2                  ADC2

/* CAN definitions */
#define BOARD_CAN0                  CAN0
#define BOARD_CAN1                  CAN1

/* I2C definitions */
#define BOARD_I2C0                  I2C0
#define BOARD_I2C1                  I2C1
#define BOARD_I2C2                  I2C2

#endif /* BOARD_H */
