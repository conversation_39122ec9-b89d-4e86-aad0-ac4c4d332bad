<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\arm2.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\arm2.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Wed Jun 11 20:21:32 2025
<BR><P>
<H3>Maximum Stack Usage =       2744 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[2]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">NMI_Handler</a><BR>
 <LI><a href="#[3]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">HardFault_Handler</a><BR>
 <LI><a href="#[4]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">MemManage_Handler</a><BR>
 <LI><a href="#[5]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">BusFault_Handler</a><BR>
 <LI><a href="#[6]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">UsageFault_Handler</a><BR>
 <LI><a href="#[7]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">SVC_Handler</a><BR>
 <LI><a href="#[8]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">DebugMon_Handler</a><BR>
 <LI><a href="#[9]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">PendSV_Handler</a><BR>
 <LI><a href="#[a]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">SysTick_Handler</a><BR>
 <LI><a href="#[1d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1d]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1d]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_RX0_IRQHandler</a> from bsp_can.o(i.CAN0_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_RX0_IRQHandler</a> from bsp_can.o(i.CAN1_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from drv_gpio.o(i.EXTI0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from drv_gpio.o(i.EXTI1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from drv_gpio.o(i.EXTI2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from bsp_exti.o(i.EXTI3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from drv_gpio.o(i.EXTI4_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">EXTI5_9_IRQHandler</a> from gd32f4xx_it.o(i.EXTI5_9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[d]">TAMPER_STAMP_IRQHandler</a> from bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_UP_TIMER9_IRQHandler</a> from gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER1_IRQHandler</a> from gd32f4xx_it.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">UART4_IRQHandler</a> from bsp_uart.o(i.UART4_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">UART6_IRQHandler</a> from bsp_uart.o(i.UART6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[68]">_sbackspace</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[67]">_sgetc</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[6b]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[6a]">fputc</a> from ins_init.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[69]">isspace</a> from isspace_o.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[64]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[66]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[185]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6c]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[8b]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[186]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[187]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[188]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[189]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[18a]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[18b]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f7]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_gdw_do
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpga_void
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[18c]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[18d]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[18e]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[ca]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlign_Init
</UL>

<P><STRONG><a name="[18f]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[70]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[17d]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
</UL>

<P><STRONG><a name="[124]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
</UL>

<P><STRONG><a name="[123]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
</UL>

<P><STRONG><a name="[87]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[75]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Last_TIME
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7d]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectVn
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[73]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_after_otherDataDo
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7f]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[80]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_after_otherDataDo
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[81]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_after_otherDataDo
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[a3]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
</UL>

<P><STRONG><a name="[146]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[90]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Lost_Time
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnavout_set
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[190]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[14c]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[85]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[191]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[82]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[192]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[193]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[67]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[68]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[88]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtod
</UL>

<P><STRONG><a name="[194]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[84]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[195]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[7b]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>

<P><STRONG><a name="[89]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[72]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6d]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[196]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[69]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[197]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[198]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>ACC_gyroreset_r_TAFEAG16_buf</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, instestingentry.o(i.ACC_gyroreset_r_TAFEAG16_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ACC_gyroreset_r_TAFEAG16_buf &rArr; FPGATo422_00BB_send &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[8f]"></a>AlgorithmAct</STRONG> (Thumb, 1238 bytes, Stack size 48 bytes, instestingentry.o(i.AlgorithmAct))
<BR><BR>[Stack]<UL><LI>Max Depth = 2720<LI>Call Chain = AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalPredict
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlign_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[9e]"></a>AlgorithmDo</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, instestingentry.o(i.AlgorithmDo))
<BR><BR>[Stack]<UL><LI>Max Depth = 2744<LI>Call Chain = AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Algorithm_before_otherDataDo
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9f]"></a>Algorithm_before_otherDataDo</STRONG> (Thumb, 16 bytes, Stack size 24 bytes, datado.o(i.Algorithm_before_otherDataDo))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Algorithm_before_otherDataDo
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;caninfupdate
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>

<P><STRONG><a name="[a2]"></a>AnalyticCoordinateAxis</STRONG> (Thumb, 4864 bytes, Stack size 56 bytes, instestingentry.o(i.AnalyticCoordinateAxis))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = AnalyticCoordinateAxis &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
</UL>

<P><STRONG><a name="[a4]"></a>AttiToCnb</STRONG> (Thumb, 500 bytes, Stack size 88 bytes, navi.o(i.AttiToCnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = AttiToCnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
</UL>

<P><STRONG><a name="[178]"></a>BindDefaultSet_by_GNSS</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, navi.o(i.BindDefaultSet_by_GNSS))
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[1f]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 884 bytes, Stack size 8 bytes, bsp_can.o(i.CAN0_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN0_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_can.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[ab]"></a>CH378DiskConnect</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, file_sys.o(i.CH378DiskConnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378DiskConnect &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ad]"></a>CH378DiskReady</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, file_sys.o(i.CH378DiskReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378DiskReady &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ae]"></a>CH378GetIntStatus</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetIntStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[b1]"></a>CH378SendCmdDatWaitInt</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, file_sys.o(i.CH378SendCmdDatWaitInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378SendCmdDatWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ac]"></a>CH378SendCmdWaitInt</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, file_sys.o(i.CH378SendCmdWaitInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378DiskReady
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378DiskConnect
</UL>

<P><STRONG><a name="[b4]"></a>CH378SetFileName</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, file_sys.o(i.CH378SetFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378SetFileName &rArr; xWriteCH378Data &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b5]"></a>CH378WriteOfsBlock</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, file_sys.o(i.CH378WriteOfsBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378WriteOfsBlock &rArr; xWriteCH378Data &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b7]"></a>CH378WriteVar32</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, file_sys.o(i.CH378WriteVar32))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378WriteVar32 &rArr; xWriteCH378Data &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b6]"></a>CH378_mDelayuS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelayuS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>

<P><STRONG><a name="[b9]"></a>CnbToAtti</STRONG> (Thumb, 616 bytes, Stack size 32 bytes, navi.o(i.CnbToAtti))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = CnbToAtti &rArr; __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
</UL>

<P><STRONG><a name="[be]"></a>CnbToQ</STRONG> (Thumb, 1072 bytes, Stack size 48 bytes, navi.o(i.CnbToQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = CnbToQ &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
</UL>

<P><STRONG><a name="[c0]"></a>ComputeAttiRate</STRONG> (Thumb, 430 bytes, Stack size 112 bytes, navi.o(i.ComputeAttiRate))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = ComputeAttiRate &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[c2]"></a>ComputeCen</STRONG> (Thumb, 246 bytes, Stack size 64 bytes, align.o(i.ComputeCen))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ComputeCen &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[c3]"></a>ComputeCib0i</STRONG> (Thumb, 236 bytes, Stack size 344 bytes, align.o(i.ComputeCib0i))
<BR><BR>[Stack]<UL><LI>Max Depth = 888<LI>Call Chain = ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
</UL>

<P><STRONG><a name="[c7]"></a>ComputeCie</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, align.o(i.ComputeCie))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ComputeCie &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[c8]"></a>ComputeDeg_Ex</STRONG> (Thumb, 182 bytes, Stack size 8 bytes, navi.o(i.ComputeDeg_Ex))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ComputeDeg_Ex &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[c9]"></a>ComputeDelSenbb</STRONG> (Thumb, 228 bytes, Stack size 56 bytes, navi.o(i.ComputeDelSenbb))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputeDelSenbb &rArr; Vec_Cross &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[cb]"></a>ComputeFk</STRONG> (Thumb, 518 bytes, Stack size 88 bytes, kalman.o(i.ComputeFk))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[9a]"></a>ComputeFn</STRONG> (Thumb, 5676 bytes, Stack size 104 bytes, kalman.o(i.ComputeFn))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = ComputeFn &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[cc]"></a>ComputeG</STRONG> (Thumb, 346 bytes, Stack size 96 bytes, navi.o(i.ComputeG))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ComputeG &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[cd]"></a>ComputeKk</STRONG> (Thumb, 234 bytes, Stack size 2112 bytes, kalman.o(i.ComputeKk))
<BR><BR>[Stack]<UL><LI>Max Depth = 2656<LI>Call Chain = ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[cf]"></a>ComputeLeverArmSn</STRONG> (Thumb, 46 bytes, Stack size 96 bytes, navi.o(i.ComputeLeverArmSn))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = ComputeLeverArmSn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[d0]"></a>ComputeLeverArmVn</STRONG> (Thumb, 58 bytes, Stack size 128 bytes, navi.o(i.ComputeLeverArmVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[d1]"></a>ComputePk</STRONG> (Thumb, 198 bytes, Stack size 1840 bytes, kalman.o(i.ComputePk))
<BR><BR>[Stack]<UL><LI>Max Depth = 1992<LI>Call Chain = ComputePk &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[d2]"></a>ComputePkk_1_Step1</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, kalman.o(i.ComputePkk_1_Step1))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ComputePkk_1_Step1 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[d3]"></a>ComputePkk_1_Step2</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, kalman.o(i.ComputePkk_1_Step2))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputePkk_1_Step2 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[d4]"></a>ComputePos</STRONG> (Thumb, 498 bytes, Stack size 96 bytes, navi.o(i.ComputePos))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ComputePos &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[d5]"></a>ComputeQ</STRONG> (Thumb, 838 bytes, Stack size 176 bytes, navi.o(i.ComputeQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = ComputeQ &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[d7]"></a>ComputeRmRn</STRONG> (Thumb, 236 bytes, Stack size 72 bytes, navi.o(i.ComputeRmRn))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ComputeRmRn &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[d8]"></a>ComputeSi</STRONG> (Thumb, 566 bytes, Stack size 320 bytes, dynamic_align.o(i.ComputeSi))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = ComputeSi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[d9]"></a>ComputeSib0</STRONG> (Thumb, 214 bytes, Stack size 56 bytes, dynamic_align.o(i.ComputeSib0))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = ComputeSib0 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[da]"></a>ComputeVi</STRONG> (Thumb, 196 bytes, Stack size 264 bytes, align.o(i.ComputeVi))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = ComputeVi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
</UL>

<P><STRONG><a name="[db]"></a>ComputeVib0</STRONG> (Thumb, 124 bytes, Stack size 64 bytes, align.o(i.ComputeVib0))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = ComputeVib0 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
</UL>

<P><STRONG><a name="[dc]"></a>ComputeVibn</STRONG> (Thumb, 280 bytes, Stack size 120 bytes, navi.o(i.ComputeVibn))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ComputeVibn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[dd]"></a>ComputeVn</STRONG> (Thumb, 746 bytes, Stack size 96 bytes, navi.o(i.ComputeVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = ComputeVn &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[de]"></a>ComputeWenn</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, navi.o(i.ComputeWenn))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = ComputeWenn &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[df]"></a>ComputeWien</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, navi.o(i.ComputeWien))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = ComputeWien &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[e0]"></a>ComputeWnbb</STRONG> (Thumb, 198 bytes, Stack size 88 bytes, navi.o(i.ComputeWnbb))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = ComputeWnbb &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[e1]"></a>ComputeXk</STRONG> (Thumb, 198 bytes, Stack size 272 bytes, kalman.o(i.ComputeXk))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = ComputeXk &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e2]"></a>ComputeXkk_1</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, kalman.o(i.ComputeXkk_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ComputeXkk_1 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalPredict
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[e3]"></a>ComputeZk</STRONG> (Thumb, 2426 bytes, Stack size 16 bytes, kalman.o(i.ComputeZk))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ComputeZk &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e4]"></a>CorrectAtti</STRONG> (Thumb, 840 bytes, Stack size 176 bytes, kalman.o(i.CorrectAtti))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = CorrectAtti &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
</UL>

<P><STRONG><a name="[e5]"></a>CorrectVn</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, kalman.o(i.CorrectVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CorrectVn &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
</UL>

<P><STRONG><a name="[f1]"></a>DRam_Read</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, bsp_fmc.o(i.DRam_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRam_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>DeviceInit</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, datado.o(i.DeviceInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DeviceInit &rArr; INS_Init &rArr; ReadParaFromFlash &rArr; Drv_FlashRead &rArr; fmc_read_8bit_data
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e9]"></a>Drv_FlashRead</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ins_init.o(i.Drv_FlashRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Drv_FlashRead &rArr; fmc_read_8bit_data
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_read_8bit_data
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
</UL>

<P><STRONG><a name="[96]"></a>DynamicInertialSysAlignCompute</STRONG> (Thumb, 518 bytes, Stack size 40 bytes, dynamic_align.o(i.DynamicInertialSysAlignCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 512<LI>Call Chain = DynamicInertialSysAlignCompute &rArr; ComputeSi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateAlignPosAndVn
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[92]"></a>DynamicInertialSysAlign_Init</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, dynamic_align.o(i.DynamicInertialSysAlign_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DynamicInertialSysAlign_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[98]"></a>DynamicNavi_Init</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, navi.o(i.DynamicNavi_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = DynamicNavi_Init &rArr; AttiToCnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI0_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI1_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI2_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bsp_exti.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI4_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI5_9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EXTI5_9_IRQHandler &rArr; writeCSVLog &rArr; CH378SendCmdDatWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRam_Read
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f4]"></a>ErrCorrect_1_Navi_Time</STRONG> (Thumb, 1852 bytes, Stack size 120 bytes, kalman.o(i.ErrCorrect_1_Navi_Time))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = ErrCorrect_1_Navi_Time &rArr; CorrectAtti &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectVn
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[9d]"></a>ErrStore_1s</STRONG> (Thumb, 2562 bytes, Stack size 56 bytes, kalman.o(i.ErrStore_1s))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ErrStore_1s &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[f5]"></a>Exti_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ins_init.o(i.Exti_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Exti_Init &rArr; bsp_exti_init &rArr; bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[8e]"></a>FPGATo422_00BB_send</STRONG> (Thumb, 570 bytes, Stack size 16 bytes, instestingentry.o(i.FPGATo422_00BB_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = FPGATo422_00BB_send &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_accum_verify_8bit
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_gyroreset_r_TAFEAG16_buf
</UL>

<P><STRONG><a name="[97]"></a>FinishDynamicInertialSysAlign</STRONG> (Thumb, 152 bytes, Stack size 232 bytes, dynamic_align.o(i.FinishDynamicInertialSysAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 1120<LI>Call Chain = FinishDynamicInertialSysAlign &rArr; ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[94]"></a>FinishInertialSysAlign</STRONG> (Thumb, 152 bytes, Stack size 232 bytes, align.o(i.FinishInertialSysAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 1120<LI>Call Chain = FinishInertialSysAlign &rArr; ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[ee]"></a>GD32_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[fa]"></a>GNSSAndHeadDataTest</STRONG> (Thumb, 316 bytes, Stack size 24 bytes, read_and_check_gnss_data.o(i.GNSSAndHeadDataTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GNSSAndHeadDataTest &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
</UL>

<P><STRONG><a name="[fb]"></a>GNSS_Last_TIME</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Last_TIME))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = GNSS_Last_TIME &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[fc]"></a>GNSS_Lost_Time</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Lost_Time))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GNSS_Lost_Time
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[fd]"></a>GNSS_Valid_PPSStart</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Valid_PPSStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = GNSS_Valid_PPSStart &rArr; ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveINSData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[117]"></a>Hk_Init</STRONG> (Thumb, 270 bytes, Stack size 0 bytes, kalman.o(i.Hk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[a0]"></a>INS912AlgorithmEntry</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, instestingentry.o(i.INS912AlgorithmEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 2736<LI>Call Chain = INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnavout_set
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>

<P><STRONG><a name="[101]"></a>INS912_Output</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, ins_output.o(i.INS912_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 720<LI>Call Chain = INS912_Output &rArr; output_fpga_void &rArr; mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_normal_do
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_gdw_do
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpga_void
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>INS_Init</STRONG> (Thumb, 206 bytes, Stack size 8 bytes, ins_init.o(i.INS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = INS_Init &rArr; ReadParaFromFlash &rArr; Drv_FlashRead &rArr; fmc_read_8bit_data
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFlashAddr
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init_basic
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Exti_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeviceInit
</UL>

<P><STRONG><a name="[93]"></a>InertialSysAlignCompute</STRONG> (Thumb, 392 bytes, Stack size 32 bytes, align.o(i.InertialSysAlignCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = InertialSysAlignCompute &rArr; ComputeVi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[91]"></a>InertialSysAlign_Init</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, align.o(i.InertialSysAlign_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = InertialSysAlign_Init &rArr; ComputeCen &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[109]"></a>InitFlashAddr</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, bsp_flash.o(i.InitFlashAddr))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[113]"></a>InitParaToAlgorithm</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, ins_init.o(i.InitParaToAlgorithm))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = InitParaToAlgorithm
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
</UL>

<P><STRONG><a name="[9b]"></a>KalCompute</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, kalman.o(i.KalCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 2672<LI>Call Chain = KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[9c]"></a>KalPredict</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, kalman.o(i.KalPredict))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = KalPredict &rArr; ComputeXkk_1 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[118]"></a>Kalman_Init</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, kalman.o(i.Kalman_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Kalman_Init &rArr; Rk_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qk_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pk_Init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
</UL>

<P><STRONG><a name="[11b]"></a>Kalman_StartUp</STRONG> (Thumb, 1160 bytes, Stack size 40 bytes, kalman.o(i.Kalman_StartUp))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = Kalman_StartUp &rArr; AttiToCnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[c5]"></a>Mat_Inv</STRONG> (Thumb, 712 bytes, Stack size 456 bytes, matvecmath.o(i.Mat_Inv))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
</UL>

<P><STRONG><a name="[c6]"></a>Mat_Mul</STRONG> (Thumb, 176 bytes, Stack size 64 bytes, matvecmath.o(i.Mat_Mul))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
</UL>

<P><STRONG><a name="[ce]"></a>Mat_Tr</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, matvecmath.o(i.Mat_Tr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Mat_Tr
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
</UL>

<P><STRONG><a name="[99]"></a>NaviCompute</STRONG> (Thumb, 682 bytes, Stack size 48 bytes, navi.o(i.NaviCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = NaviCompute &rArr; ErrCorrect_1_Navi_Time &rArr; CorrectAtti &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[95]"></a>Navi_Init</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, navi.o(i.Navi_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Navi_Init &rArr; CnbToAtti &rArr; __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[119]"></a>Pk_Init</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, kalman.o(i.Pk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[ec]"></a>QToCnb</STRONG> (Thumb, 1252 bytes, Stack size 64 bytes, navi.o(i.QToCnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = QToCnb &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[11a]"></a>Qk_Init</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, kalman.o(i.Qk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[d6]"></a>Qua_Mul</STRONG> (Thumb, 802 bytes, Stack size 64 bytes, matvecmath.o(i.Qua_Mul))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Qua_Mul &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
</UL>

<P><STRONG><a name="[11c]"></a>Query378Interrupt</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ch378_hal.o(i.Query378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Query378Interrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[10b]"></a>ReadParaFromFlash</STRONG> (Thumb, 450 bytes, Stack size 616 bytes, ins_init.o(i.ReadParaFromFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = ReadParaFromFlash &rArr; Drv_FlashRead &rArr; fmc_read_8bit_data
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashRead
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[11f]"></a>Read_And_Check_GNSS_Data</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, read_and_check_gnss_data.o(i.Read_And_Check_GNSS_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = Read_And_Check_GNSS_Data &rArr; SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[116]"></a>Rk_Init</STRONG> (Thumb, 666 bytes, Stack size 8 bytes, kalman.o(i.Rk_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Rk_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[121]"></a>SDUartIrqInit</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, main.o(i.SDUartIrqInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDUartIrqInit
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init_basic
</UL>

<P><STRONG><a name="[120]"></a>SaveGNSSData</STRONG> (Thumb, 1184 bytes, Stack size 72 bytes, read_and_check_gnss_data.o(i.SaveGNSSData))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
</UL>

<P><STRONG><a name="[fe]"></a>SaveINSData</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, kalman.o(i.SaveINSData))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[e8]"></a>SendVersionInfo</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, datado.o(i.SendVersionInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SendVersionInfo &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeviceInit
</UL>

<P><STRONG><a name="[125]"></a>SysInit</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, navi.o(i.SysInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysInit &rArr; Kalman_Init &rArr; Rk_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysVarDefaultSet
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[126]"></a>SysVarDefaultSet</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, navi.o(i.SysVarDefaultSet))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
</UL>

<P><STRONG><a name="[65]"></a>SystemInit</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[d]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, bsp_rtc.o(i.TAMPER_STAMP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TAMPER_STAMP_IRQHandler &rArr; rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_get
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER0_UP_TIMER9_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[ed]"></a>TransHeading0to360</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, navi.o(i.TransHeading0to360))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = TransHeading0to360 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[40]"></a>UART4_IRQHandler</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, bsp_uart.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART4_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART6_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, bsp_uart.o(i.UART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART6_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[130]"></a>UartIrqInit</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, main.o(i.UartIrqInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = UartIrqInit &rArr; bsp_systick_init01 &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init_basic
</UL>

<P><STRONG><a name="[132]"></a>UartIrqSendMsg</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, main.o(i.UartIrqSendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>

<P><STRONG><a name="[134]"></a>Uart_SendMsg</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_SendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Uart_SendMsg &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[eb]"></a>UpdateAlignPosAndVn</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, dynamic_align.o(i.UpdateAlignPosAndVn))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[c4]"></a>Vec_Cross</STRONG> (Thumb, 294 bytes, Stack size 32 bytes, matvecmath.o(i.Vec_Cross))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Vec_Cross &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
</UL>

<P><STRONG><a name="[135]"></a>Virtual_PPS_insert_5hz</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, instestingentry.o(i.Virtual_PPS_insert_5hz))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Virtual_PPS_insert_5hz &rArr; ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveINSData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[b3]"></a>Wait378Interrupt</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, file_sys.o(i.Wait378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdDatWaitInt
</UL>

<P><STRONG><a name="[136]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[199]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[11e]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
</UL>

<P><STRONG><a name="[19a]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[19b]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[138]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[19c]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[17c]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[19d]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[19e]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[13c]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[bd]"></a>__hardfp_asin</STRONG> (Thumb, 770 bytes, Stack size 88 bytes, asin.o(i.__hardfp_asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
</UL>

<P><STRONG><a name="[bc]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
</UL>

<P><STRONG><a name="[a6]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>

<P><STRONG><a name="[ba]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
</UL>

<P><STRONG><a name="[144]"></a>__hardfp_log</STRONG> (Thumb, 872 bytes, Stack size 88 bytes, log.o(i.__hardfp_log))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __hardfp_log &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[a5]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>

<P><STRONG><a name="[bf]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[147]"></a>__hardfp_strtod</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, strtod.o(i.__hardfp_strtod))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[c1]"></a>__hardfp_tan</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, tan.o(i.__hardfp_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
</UL>

<P><STRONG><a name="[141]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[143]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[13e]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[142]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[148]"></a>__kernel_tan</STRONG> (Thumb, 764 bytes, Stack size 128 bytes, tan_i.o(i.__kernel_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
</UL>

<P><STRONG><a name="[145]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
</UL>

<P><STRONG><a name="[139]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[13b]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[13d]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[19f]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[1a0]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[1a1]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[13a]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[76]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[f8]"></a>app_accum_verify_8bit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, app_tool.o(i.app_accum_verify_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_accum_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[14d]"></a>bsp_exti_config</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_exti_line_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_init
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
</UL>

<P><STRONG><a name="[f6]"></a>bsp_exti_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_exti_init &rArr; bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Exti_Init
</UL>

<P><STRONG><a name="[107]"></a>bsp_gpio_init</STRONG> (Thumb, 404 bytes, Stack size 8 bytes, bsp_gpio.o(i.bsp_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = bsp_gpio_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[122]"></a>bsp_systick_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, bsp_uart.o(i.bsp_systick_init))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDUartIrqInit
</UL>

<P><STRONG><a name="[131]"></a>bsp_systick_init01</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, bsp_uart.o(i.bsp_systick_init01))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = bsp_systick_init01 &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartIrqInit
</UL>

<P><STRONG><a name="[108]"></a>bsp_tim_init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_tim.o(i.bsp_tim_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_tim_init &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[a7]"></a>can_message_receive</STRONG> (Thumb, 228 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_message_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[a1]"></a>caninfupdate</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, ins_data.o(i.caninfupdate))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpga_void
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Algorithm_before_otherDataDo
</UL>

<P><STRONG><a name="[16b]"></a>comm_axis_read</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_axis_read))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[115]"></a>comm_nav_para_syn</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_nav_para_syn))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
</UL>

<P><STRONG><a name="[114]"></a>comm_param_setbits</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_param_setbits))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
</UL>

<P><STRONG><a name="[16c]"></a>comm_read_currentFreq</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_read_currentFreq))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[10e]"></a>delay_ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, systick.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms_impl
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[158]"></a>delay_ms_impl</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, systick.o(i.delay_ms_impl))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[b8]"></a>delay_us</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, systick.o(i.delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
</UL>

<P><STRONG><a name="[10a]"></a>exmc_asynchronous_sram_init</STRONG> (Thumb, 364 bytes, Stack size 120 bytes, bsp_fmc.o(i.exmc_asynchronous_sram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = exmc_asynchronous_sram_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[15a]"></a>exmc_norsram_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(i.exmc_norsram_enable))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[159]"></a>exmc_norsram_init</STRONG> (Thumb, 224 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(i.exmc_norsram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = exmc_norsram_init
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[151]"></a>exti_init</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, gd32f4xx_exti.o(i.exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[f0]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
</UL>

<P><STRONG><a name="[ef]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[15b]"></a>fmc2sinsraw</STRONG> (Thumb, 3004 bytes, Stack size 144 bytes, readpaoche.o(i.fmc2sinsraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_8bit_I16
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_I32
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_D64
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_D32
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtod
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
</UL>

<P><STRONG><a name="[ea]"></a>fmc_read_8bit_data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_fmc.o(i.fmc_read_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_read_8bit_data
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashRead
</UL>

<P><STRONG><a name="[ff]"></a>fpgadata_Predo</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, instestingentry.o(i.fpgadata_Predo))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = fpgadata_Predo &rArr; gnss_check_bind &rArr; Read_And_Check_GNSS_Data &rArr; SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Lost_Time
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Last_TIME
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_gyroreset_r_TAFEAG16_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[160]"></a>fpgadata_Predo_chen</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, instestingentry.o(i.fpgadata_Predo_chen))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = fpgadata_Predo_chen &rArr; fpgadata_Predo_chen_OutDataSet &rArr; fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_algParmCash
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_setRParm_acc
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[163]"></a>fpgadata_Predo_chen_OutDataSet</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, instestingentry.o(i.fpgadata_Predo_chen_OutDataSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = fpgadata_Predo_chen_OutDataSet &rArr; fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[168]"></a>fpgadata_Predo_chen_SetAlgParm_acc</STRONG> (Thumb, 990 bytes, Stack size 48 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = fpgadata_Predo_chen_SetAlgParm_acc &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[166]"></a>fpgadata_Predo_chen_SetAlgParm_gyro</STRONG> (Thumb, 984 bytes, Stack size 48 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = fpgadata_Predo_chen_SetAlgParm_gyro &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[167]"></a>fpgadata_Predo_chen_SetAlgParm_setRParm_acc</STRONG> (Thumb, 130 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_acc))
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[165]"></a>fpgadata_Predo_chen_SetAlgParm_setRParm_gyro</STRONG> (Thumb, 130 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_gyro))
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[164]"></a>fpgadata_Predo_chen_algParmCash</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_algParmCash))
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[162]"></a>fpgadata_Predo_chen_preAlgParm_370</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = fpgadata_Predo_chen_preAlgParm_370 &rArr; AnalyticCoordinateAxis &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnalyticCoordinateAxis
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[169]"></a>fpgadata_syn_count_do</STRONG> (Thumb, 158 bytes, Stack size 8 bytes, fpgad.o(i.fpgadata_syn_count_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fpgadata_syn_count_do
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[6a]"></a>fputc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ins_init.o(i.fputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[16a]"></a>frame_pack_and_send</STRONG> (Thumb, 1984 bytes, Stack size 96 bytes, frame_analysis.o(i.frame_pack_and_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = frame_pack_and_send &rArr; __hardfp_log &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xor_check
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_axis_read
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_send
</UL>

<P><STRONG><a name="[10f]"></a>gd_eval_com_init</STRONG> (Thumb, 238 bytes, Stack size 16 bytes, ins_init.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[10c]"></a>gd_eval_com_init_basic</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ins_init.o(i.gd_eval_com_init_basic))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = gd_eval_com_init_basic &rArr; UartIrqInit &rArr; bsp_systick_init01 &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartIrqInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDUartIrqInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[173]"></a>get_fpgadata</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fpgad.o(i.get_fpgadata))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = get_fpgadata &rArr; get_fpgadata_after_otherDataDo &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_before
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_after_otherDataDo
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[176]"></a>get_fpgadata_after_otherDataDo</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, fpgad.o(i.get_fpgadata_after_otherDataDo))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = get_fpgadata_after_otherDataDo &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[174]"></a>get_fpgadata_before</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, fpgad.o(i.get_fpgadata_before))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[175]"></a>get_fpgadata_do</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, fpgad.o(i.get_fpgadata_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_fpgadata_do &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_exmc_initialized
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[161]"></a>gnss_check_bind</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, instestingentry.o(i.gnss_check_bind))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = gnss_check_bind &rArr; Read_And_Check_GNSS_Data &rArr; SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BindDefaultSet_by_GNSS
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[153]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[a9]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[aa]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteVar32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdDatWaitInt
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[11d]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
</UL>

<P><STRONG><a name="[14f]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[152]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[106]"></a>initializationdriversettings</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, gdwatch.o(i.initializationdriversettings))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = initializationdriversettings
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[179]"></a>loopDoOther</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, datado.o(i.loopDoOther))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[64]"></a>main</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2744<LI>Call Chain = main &rArr; AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeviceInit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[17a]"></a>mcusendtopcdriversdata</STRONG> (Thumb, 284 bytes, Stack size 328 bytes, gdwatch.o(i.mcusendtopcdriversdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_gdw_do
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpga_void
</UL>

<P><STRONG><a name="[15e]"></a>myget_16bit_D32</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, readpaoche.o(i.myget_16bit_D32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_16bit_D32
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[15f]"></a>myget_16bit_D64</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, readpaoche.o(i.myget_16bit_D64))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = myget_16bit_D64
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[15c]"></a>myget_16bit_I32</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, readpaoche.o(i.myget_16bit_I32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_16bit_I32
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[15d]"></a>myget_8bit_I16</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, readpaoche.o(i.myget_8bit_I16))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_8bit_I16
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[10d]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[17b]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[105]"></a>output_fpga_void</STRONG> (Thumb, 372 bytes, Stack size 336 bytes, ins_output.o(i.output_fpga_void))
<BR><BR>[Stack]<UL><LI>Max Depth = 704<LI>Call Chain = output_fpga_void &rArr; mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;caninfupdate
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
</UL>

<P><STRONG><a name="[103]"></a>output_fpgatxt_do</STRONG> (Thumb, 180 bytes, Stack size 224 bytes, ins_output.o(i.output_fpgatxt_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = output_fpgatxt_do &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
</UL>

<P><STRONG><a name="[104]"></a>output_gdw_do</STRONG> (Thumb, 840 bytes, Stack size 312 bytes, ins_output.o(i.output_gdw_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = output_gdw_do &rArr; mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
</UL>

<P><STRONG><a name="[102]"></a>output_normal_do</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ins_output.o(i.output_normal_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = output_normal_do &rArr; protocol_send &rArr; frame_pack_and_send &rArr; __hardfp_log &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_send
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
</UL>

<P><STRONG><a name="[100]"></a>pnavout_set</STRONG> (Thumb, 176 bytes, Stack size 8 bytes, instestingentry.o(i.pnavout_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pnavout_set &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[17e]"></a>protocol_send</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, computerframeparse.o(i.protocol_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = protocol_send &rArr; frame_pack_and_send &rArr; __hardfp_log &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_normal_do
</UL>

<P><STRONG><a name="[182]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[14e]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[184]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[183]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[12a]"></a>rtc_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[128]"></a>rtc_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>rtc_show_timestamp</STRONG> (Thumb, 388 bytes, Stack size 32 bytes, bsp_rtc.o(i.rtc_show_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_subsecond_get
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_get
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[17f]"></a>rtc_timestamp_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_get))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[180]"></a>rtc_timestamp_subsecond_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[140]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>

<P><STRONG><a name="[f2]"></a>synthesisLogBuf</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, logger.o(i.synthesisLogBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = synthesisLogBuf &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[150]"></a>syscfg_exti_line_config</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, gd32f4xx_syscfg.o(i.syscfg_exti_line_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[12c]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[f9]"></a>uart4sendmsg</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart4sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = uart4sendmsg &rArr; UartIrqSendMsg &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartIrqSendMsg
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fpgatxt_do
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[155]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[12f]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[133]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartIrqSendMsg
</UL>

<P><STRONG><a name="[154]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[111]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[110]"></a>usart_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[12e]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartIrqSendMsg
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[172]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_cts_config))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[171]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_rts_config))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[112]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[12d]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[170]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[156]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[16f]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[157]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_systick_init01
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[16e]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[f3]"></a>writeCSVLog</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, logger.o(i.writeCSVLog))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = writeCSVLog &rArr; CH378SendCmdDatWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteVar32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdDatWaitInt
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378DiskReady
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378DiskConnect
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>xReadCH378Data</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, ch378_hal.o(i.xReadCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xReadCH378Data
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>

<P><STRONG><a name="[af]"></a>xWriteCH378Cmd</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, ch378_hal.o(i.xWriteCH378Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xWriteCH378Cmd &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteVar32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdDatWaitInt
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>

<P><STRONG><a name="[b2]"></a>xWriteCH378Data</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, ch378_hal.o(i.xWriteCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xWriteCH378Data &rArr; CH378_mDelayuS
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteVar32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdDatWaitInt
</UL>

<P><STRONG><a name="[16d]"></a>xor_check</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, frame_analysis.o(i.xor_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xor_check
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[177]"></a>check_exmc_initialized</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, fpgad.o(i.check_exmc_initialized))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
</UL>

<P><STRONG><a name="[181]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[127]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[149]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[137]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[14b]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[14a]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[71]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[86]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
