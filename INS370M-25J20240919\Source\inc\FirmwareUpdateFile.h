//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// FirmwareUpdateFile.h
//
//
//
// V1.0
//     zhangjianzhou
// 2024.10.23
//---------------------------------------------------------

#ifndef ____FIRM_UPDATA_H____
#define ____FIRM_UPDATA_H____

#include "appmain.h"
#include "stdint.h"
#include <stdio.h>


#define	ADDR_STEP		4096



#define APP_FILE_SIZE		0x64000                // 定义APP空间大小400k
#define APP_ADDRESS             0xD000                 // APP程序运行的起始地址 40k

#define APP_UPDATE_ADDRESS      APP_ADDRESS + APP_FILE_SIZE   // 固件程序升级时的备份地址

#define APP_UPDATE_CFG_ADDR     APP_UPDATE_ADDRESS + APP_FILE_SIZE // 升级标志保存地址

#define SAVE_SET_PARA_ADDR      APP_UPDATE_CFG_ADDR + ADDR_STEP // 设置固化参数保存地址



#define FLASH_SIZE        			0x400000      // FLASH 4M
#define FLASH_WRITE_SECTOR_SIZE                 0x1000        // 4K
#define FLASH_ERASE_BLOCK_SIZE       		0x10000       // 64K



//
typedef enum{
	UPDATE_FIRM_START	=0x01,//
	UPDATE_FIRM_END		=0x05,//
	UPDATE_FIRM_SEND	=0x02,//
	UPDATE_FIRM_STOP	=0x09,//
}UpdateFirm_e;

#define SOFTWARE_VER   	1 //

//擦除Flash
void Drv_FlashErase(uint32_t address);
//写入FLASH
void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen);
//读FLASH
void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen);


void UpdateFileHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen);
void Drv_SystemReset(void);
uint8_t Get_CRC8(uint8_t *ptr, uint8_t len);



void FlashTest(void);//FLASH

// SD卡操作函数声明
void SdFileOperateTypeSet(unsigned char operateType, unsigned char fileType);






#endif

